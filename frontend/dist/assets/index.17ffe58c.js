var x0=Object.defineProperty;var w0=(e,t,r)=>t in e?x0(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Yr=(e,t,r)=>(w0(e,typeof t!="symbol"?t+"":t,r),r);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))n(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function r(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerpolicy&&(o.referrerPolicy=l.referrerpolicy),l.crossorigin==="use-credentials"?o.credentials="include":l.crossorigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(l){if(l.ep)return;l.ep=!0;const o=r(l);fetch(l.href,o)}})();function k0(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var E={exports:{}},q={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bl=Symbol.for("react.element"),S0=Symbol.for("react.portal"),E0=Symbol.for("react.fragment"),C0=Symbol.for("react.strict_mode"),N0=Symbol.for("react.profiler"),D0=Symbol.for("react.provider"),F0=Symbol.for("react.context"),B0=Symbol.for("react.forward_ref"),R0=Symbol.for("react.suspense"),A0=Symbol.for("react.memo"),b0=Symbol.for("react.lazy"),Bs=Symbol.iterator;function P0(e){return e===null||typeof e!="object"?null:(e=Bs&&e[Bs]||e["@@iterator"],typeof e=="function"?e:null)}var hd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},pd=Object.assign,md={};function Fn(e,t,r){this.props=e,this.context=t,this.refs=md,this.updater=r||hd}Fn.prototype.isReactComponent={};Fn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Fn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function yd(){}yd.prototype=Fn.prototype;function cu(e,t,r){this.props=e,this.context=t,this.refs=md,this.updater=r||hd}var du=cu.prototype=new yd;du.constructor=cu;pd(du,Fn.prototype);du.isPureReactComponent=!0;var Rs=Array.isArray,gd=Object.prototype.hasOwnProperty,fu={current:null},vd={key:!0,ref:!0,__self:!0,__source:!0};function xd(e,t,r){var n,l={},o=null,i=null;if(t!=null)for(n in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(o=""+t.key),t)gd.call(t,n)&&!vd.hasOwnProperty(n)&&(l[n]=t[n]);var u=arguments.length-2;if(u===1)l.children=r;else if(1<u){for(var a=Array(u),s=0;s<u;s++)a[s]=arguments[s+2];l.children=a}if(e&&e.defaultProps)for(n in u=e.defaultProps,u)l[n]===void 0&&(l[n]=u[n]);return{$$typeof:Bl,type:e,key:o,ref:i,props:l,_owner:fu.current}}function _0(e,t){return{$$typeof:Bl,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function hu(e){return typeof e=="object"&&e!==null&&e.$$typeof===Bl}function L0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var As=/\/+/g;function Ai(e,t){return typeof e=="object"&&e!==null&&e.key!=null?L0(""+e.key):t.toString(36)}function ao(e,t,r,n,l){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case Bl:case S0:i=!0}}if(i)return i=e,l=l(i),e=n===""?"."+Ai(i,0):n,Rs(l)?(r="",e!=null&&(r=e.replace(As,"$&/")+"/"),ao(l,t,r,"",function(s){return s})):l!=null&&(hu(l)&&(l=_0(l,r+(!l.key||i&&i.key===l.key?"":(""+l.key).replace(As,"$&/")+"/")+e)),t.push(l)),1;if(i=0,n=n===""?".":n+":",Rs(e))for(var u=0;u<e.length;u++){o=e[u];var a=n+Ai(o,u);i+=ao(o,t,r,a,l)}else if(a=P0(e),typeof a=="function")for(e=a.call(e),u=0;!(o=e.next()).done;)o=o.value,a=n+Ai(o,u++),i+=ao(o,t,r,a,l);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function jl(e,t,r){if(e==null)return e;var n=[],l=0;return ao(e,n,"","",function(o){return t.call(r,o,l++)}),n}function I0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){(e._status===0||e._status===-1)&&(e._status=1,e._result=r)},function(r){(e._status===0||e._status===-1)&&(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var We={current:null},uo={transition:null},M0={ReactCurrentDispatcher:We,ReactCurrentBatchConfig:uo,ReactCurrentOwner:fu};function wd(){throw Error("act(...) is not supported in production builds of React.")}q.Children={map:jl,forEach:function(e,t,r){jl(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return jl(e,function(){t++}),t},toArray:function(e){return jl(e,function(t){return t})||[]},only:function(e){if(!hu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};q.Component=Fn;q.Fragment=E0;q.Profiler=N0;q.PureComponent=cu;q.StrictMode=C0;q.Suspense=R0;q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=M0;q.act=wd;q.cloneElement=function(e,t,r){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var n=pd({},e.props),l=e.key,o=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,i=fu.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(a in t)gd.call(t,a)&&!vd.hasOwnProperty(a)&&(n[a]=t[a]===void 0&&u!==void 0?u[a]:t[a])}var a=arguments.length-2;if(a===1)n.children=r;else if(1<a){u=Array(a);for(var s=0;s<a;s++)u[s]=arguments[s+2];n.children=u}return{$$typeof:Bl,type:e.type,key:l,ref:o,props:n,_owner:i}};q.createContext=function(e){return e={$$typeof:F0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:D0,_context:e},e.Consumer=e};q.createElement=xd;q.createFactory=function(e){var t=xd.bind(null,e);return t.type=e,t};q.createRef=function(){return{current:null}};q.forwardRef=function(e){return{$$typeof:B0,render:e}};q.isValidElement=hu;q.lazy=function(e){return{$$typeof:b0,_payload:{_status:-1,_result:e},_init:I0}};q.memo=function(e,t){return{$$typeof:A0,type:e,compare:t===void 0?null:t}};q.startTransition=function(e){var t=uo.transition;uo.transition={};try{e()}finally{uo.transition=t}};q.unstable_act=wd;q.useCallback=function(e,t){return We.current.useCallback(e,t)};q.useContext=function(e){return We.current.useContext(e)};q.useDebugValue=function(){};q.useDeferredValue=function(e){return We.current.useDeferredValue(e)};q.useEffect=function(e,t){return We.current.useEffect(e,t)};q.useId=function(){return We.current.useId()};q.useImperativeHandle=function(e,t,r){return We.current.useImperativeHandle(e,t,r)};q.useInsertionEffect=function(e,t){return We.current.useInsertionEffect(e,t)};q.useLayoutEffect=function(e,t){return We.current.useLayoutEffect(e,t)};q.useMemo=function(e,t){return We.current.useMemo(e,t)};q.useReducer=function(e,t,r){return We.current.useReducer(e,t,r)};q.useRef=function(e){return We.current.useRef(e)};q.useState=function(e){return We.current.useState(e)};q.useSyncExternalStore=function(e,t,r){return We.current.useSyncExternalStore(e,t,r)};q.useTransition=function(){return We.current.useTransition()};q.version="18.3.1";(function(e){e.exports=q})(E);const T0=k0(E.exports);var pu={exports:{}},ot={},kd={exports:{}},Sd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(L,$){var V=L.length;L.push($);e:for(;0<V;){var ee=V-1>>>1,le=L[ee];if(0<l(le,$))L[ee]=$,L[V]=le,V=ee;else break e}}function r(L){return L.length===0?null:L[0]}function n(L){if(L.length===0)return null;var $=L[0],V=L.pop();if(V!==$){L[0]=V;e:for(var ee=0,le=L.length,xe=le>>>1;ee<xe;){var $e=2*(ee+1)-1,Wt=L[$e],je=$e+1,Wr=L[je];if(0>l(Wt,V))je<le&&0>l(Wr,Wt)?(L[ee]=Wr,L[je]=V,ee=je):(L[ee]=Wt,L[$e]=V,ee=$e);else if(je<le&&0>l(Wr,V))L[ee]=Wr,L[je]=V,ee=je;else break e}}return $}function l(L,$){var V=L.sortIndex-$.sortIndex;return V!==0?V:L.id-$.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,u=i.now();e.unstable_now=function(){return i.now()-u}}var a=[],s=[],f=1,y=null,h=3,w=!1,k=!1,C=!1,A=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function c(L){for(var $=r(s);$!==null;){if($.callback===null)n(s);else if($.startTime<=L)n(s),$.sortIndex=$.expirationTime,t(a,$);else break;$=r(s)}}function g(L){if(C=!1,c(L),!k)if(r(a)!==null)k=!0,Z(B);else{var $=r(s);$!==null&&Se(g,$.startTime-L)}}function B(L,$){k=!1,C&&(C=!1,m(b),b=-1),w=!0;var V=h;try{for(c($),y=r(a);y!==null&&(!(y.expirationTime>$)||L&&!j());){var ee=y.callback;if(typeof ee=="function"){y.callback=null,h=y.priorityLevel;var le=ee(y.expirationTime<=$);$=e.unstable_now(),typeof le=="function"?y.callback=le:y===r(a)&&n(a),c($)}else n(a);y=r(a)}if(y!==null)var xe=!0;else{var $e=r(s);$e!==null&&Se(g,$e.startTime-$),xe=!1}return xe}finally{y=null,h=V,w=!1}}var v=!1,R=null,b=-1,z=5,D=-1;function j(){return!(e.unstable_now()-D<z)}function Fe(){if(R!==null){var L=e.unstable_now();D=L;var $=!0;try{$=R(!0,L)}finally{$?_e():(v=!1,R=null)}}else v=!1}var _e;if(typeof p=="function")_e=function(){p(Fe)};else if(typeof MessageChannel<"u"){var re=new MessageChannel,M=re.port2;re.port1.onmessage=Fe,_e=function(){M.postMessage(null)}}else _e=function(){A(Fe,0)};function Z(L){R=L,v||(v=!0,_e())}function Se(L,$){b=A(function(){L(e.unstable_now())},$)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(L){L.callback=null},e.unstable_continueExecution=function(){k||w||(k=!0,Z(B))},e.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):z=0<L?Math.floor(1e3/L):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return r(a)},e.unstable_next=function(L){switch(h){case 1:case 2:case 3:var $=3;break;default:$=h}var V=h;h=$;try{return L()}finally{h=V}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(L,$){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var V=h;h=L;try{return $()}finally{h=V}},e.unstable_scheduleCallback=function(L,$,V){var ee=e.unstable_now();switch(typeof V=="object"&&V!==null?(V=V.delay,V=typeof V=="number"&&0<V?ee+V:ee):V=ee,L){case 1:var le=-1;break;case 2:le=250;break;case 5:le=**********;break;case 4:le=1e4;break;default:le=5e3}return le=V+le,L={id:f++,callback:$,priorityLevel:L,startTime:V,expirationTime:le,sortIndex:-1},V>ee?(L.sortIndex=V,t(s,L),r(a)===null&&L===r(s)&&(C?(m(b),b=-1):C=!0,Se(g,V-ee))):(L.sortIndex=le,t(a,L),k||w||(k=!0,Z(B))),L},e.unstable_shouldYield=j,e.unstable_wrapCallback=function(L){var $=h;return function(){var V=h;h=$;try{return L.apply(this,arguments)}finally{h=V}}}})(Sd);(function(e){e.exports=Sd})(kd);/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var z0=E.exports,lt=kd.exports;function _(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Ed=new Set,ul={};function jr(e,t){wn(e,t),wn(e+"Capture",t)}function wn(e,t){for(ul[e]=t,e=0;e<t.length;e++)Ed.add(t[e])}var zt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ua=Object.prototype.hasOwnProperty,O0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,bs={},Ps={};function $0(e){return ua.call(Ps,e)?!0:ua.call(bs,e)?!1:O0.test(e)?Ps[e]=!0:(bs[e]=!0,!1)}function j0(e,t,r,n){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return n?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function U0(e,t,r,n){if(t===null||typeof t>"u"||j0(e,t,r,n))return!0;if(n)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Qe(e,t,r,n,l,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=n,this.attributeNamespace=l,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var Pe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Pe[e]=new Qe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Pe[t]=new Qe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Pe[e]=new Qe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Pe[e]=new Qe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Pe[e]=new Qe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Pe[e]=new Qe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Pe[e]=new Qe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Pe[e]=new Qe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Pe[e]=new Qe(e,5,!1,e.toLowerCase(),null,!1,!1)});var mu=/[\-:]([a-z])/g;function yu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(mu,yu);Pe[t]=new Qe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(mu,yu);Pe[t]=new Qe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(mu,yu);Pe[t]=new Qe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Pe[e]=new Qe(e,1,!1,e.toLowerCase(),null,!1,!1)});Pe.xlinkHref=new Qe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Pe[e]=new Qe(e,1,!1,e.toLowerCase(),null,!0,!0)});function gu(e,t,r,n){var l=Pe.hasOwnProperty(t)?Pe[t]:null;(l!==null?l.type!==0:n||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(U0(t,r,l,n)&&(r=null),n||l===null?$0(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):l.mustUseProperty?e[l.propertyName]=r===null?l.type===3?!1:"":r:(t=l.attributeName,n=l.attributeNamespace,r===null?e.removeAttribute(t):(l=l.type,r=l===3||l===4&&r===!0?"":""+r,n?e.setAttributeNS(n,t,r):e.setAttribute(t,r))))}var Ut=z0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ul=Symbol.for("react.element"),qr=Symbol.for("react.portal"),Zr=Symbol.for("react.fragment"),vu=Symbol.for("react.strict_mode"),sa=Symbol.for("react.profiler"),Cd=Symbol.for("react.provider"),Nd=Symbol.for("react.context"),xu=Symbol.for("react.forward_ref"),ca=Symbol.for("react.suspense"),da=Symbol.for("react.suspense_list"),wu=Symbol.for("react.memo"),Xt=Symbol.for("react.lazy"),Dd=Symbol.for("react.offscreen"),_s=Symbol.iterator;function Mn(e){return e===null||typeof e!="object"?null:(e=_s&&e[_s]||e["@@iterator"],typeof e=="function"?e:null)}var pe=Object.assign,bi;function Kn(e){if(bi===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);bi=t&&t[1]||""}return`
`+bi+e}var Pi=!1;function _i(e,t){if(!e||Pi)return"";Pi=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(s){var n=s}Reflect.construct(e,[],t)}else{try{t.call()}catch(s){n=s}e.call(t.prototype)}else{try{throw Error()}catch(s){n=s}e()}}catch(s){if(s&&n&&typeof s.stack=="string"){for(var l=s.stack.split(`
`),o=n.stack.split(`
`),i=l.length-1,u=o.length-1;1<=i&&0<=u&&l[i]!==o[u];)u--;for(;1<=i&&0<=u;i--,u--)if(l[i]!==o[u]){if(i!==1||u!==1)do if(i--,u--,0>u||l[i]!==o[u]){var a=`
`+l[i].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=i&&0<=u);break}}}finally{Pi=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?Kn(e):""}function V0(e){switch(e.tag){case 5:return Kn(e.type);case 16:return Kn("Lazy");case 13:return Kn("Suspense");case 19:return Kn("SuspenseList");case 0:case 2:case 15:return e=_i(e.type,!1),e;case 11:return e=_i(e.type.render,!1),e;case 1:return e=_i(e.type,!0),e;default:return""}}function fa(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Zr:return"Fragment";case qr:return"Portal";case sa:return"Profiler";case vu:return"StrictMode";case ca:return"Suspense";case da:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Nd:return(e.displayName||"Context")+".Consumer";case Cd:return(e._context.displayName||"Context")+".Provider";case xu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case wu:return t=e.displayName||null,t!==null?t:fa(e.type)||"Memo";case Xt:t=e._payload,e=e._init;try{return fa(e(t))}catch{}}return null}function H0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return fa(t);case 8:return t===vu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function fr(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Fd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function W0(e){var t=Fd(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var l=r.get,o=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(i){n=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return n},setValue:function(i){n=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Vl(e){e._valueTracker||(e._valueTracker=W0(e))}function Bd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),n="";return e&&(n=Fd(e)?e.checked?"true":"false":e.value),e=n,e!==r?(t.setValue(e),!0):!1}function No(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ha(e,t){var r=t.checked;return pe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r!=null?r:e._wrapperState.initialChecked})}function Ls(e,t){var r=t.defaultValue==null?"":t.defaultValue,n=t.checked!=null?t.checked:t.defaultChecked;r=fr(t.value!=null?t.value:r),e._wrapperState={initialChecked:n,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Rd(e,t){t=t.checked,t!=null&&gu(e,"checked",t,!1)}function pa(e,t){Rd(e,t);var r=fr(t.value),n=t.type;if(r!=null)n==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(n==="submit"||n==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ma(e,t.type,r):t.hasOwnProperty("defaultValue")&&ma(e,t.type,fr(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Is(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var n=t.type;if(!(n!=="submit"&&n!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function ma(e,t,r){(t!=="number"||No(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var Yn=Array.isArray;function fn(e,t,r,n){if(e=e.options,t){t={};for(var l=0;l<r.length;l++)t["$"+r[l]]=!0;for(r=0;r<e.length;r++)l=t.hasOwnProperty("$"+e[r].value),e[r].selected!==l&&(e[r].selected=l),l&&n&&(e[r].defaultSelected=!0)}else{for(r=""+fr(r),t=null,l=0;l<e.length;l++){if(e[l].value===r){e[l].selected=!0,n&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function ya(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(_(91));return pe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ms(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(_(92));if(Yn(r)){if(1<r.length)throw Error(_(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:fr(r)}}function Ad(e,t){var r=fr(t.value),n=fr(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),n!=null&&(e.defaultValue=""+n)}function Ts(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function bd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ga(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?bd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Hl,Pd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,n,l){MSApp.execUnsafeLocalFunction(function(){return e(t,r,n,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Hl=Hl||document.createElement("div"),Hl.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Hl.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function sl(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var Zn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Q0=["Webkit","ms","Moz","O"];Object.keys(Zn).forEach(function(e){Q0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Zn[t]=Zn[e]})});function _d(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||Zn.hasOwnProperty(e)&&Zn[e]?(""+t).trim():t+"px"}function Ld(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var n=r.indexOf("--")===0,l=_d(r,t[r],n);r==="float"&&(r="cssFloat"),n?e.setProperty(r,l):e[r]=l}}var K0=pe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function va(e,t){if(t){if(K0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(_(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(_(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(_(61))}if(t.style!=null&&typeof t.style!="object")throw Error(_(62))}}function xa(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var wa=null;function ku(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ka=null,hn=null,pn=null;function zs(e){if(e=bl(e)){if(typeof ka!="function")throw Error(_(280));var t=e.stateNode;t&&(t=si(t),ka(e.stateNode,e.type,t))}}function Id(e){hn?pn?pn.push(e):pn=[e]:hn=e}function Md(){if(hn){var e=hn,t=pn;if(pn=hn=null,zs(e),t)for(e=0;e<t.length;e++)zs(t[e])}}function Td(e,t){return e(t)}function zd(){}var Li=!1;function Od(e,t,r){if(Li)return e(t,r);Li=!0;try{return Td(e,t,r)}finally{Li=!1,(hn!==null||pn!==null)&&(zd(),Md())}}function cl(e,t){var r=e.stateNode;if(r===null)return null;var n=si(r);if(n===null)return null;r=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(_(231,t,typeof r));return r}var Sa=!1;if(zt)try{var Tn={};Object.defineProperty(Tn,"passive",{get:function(){Sa=!0}}),window.addEventListener("test",Tn,Tn),window.removeEventListener("test",Tn,Tn)}catch{Sa=!1}function Y0(e,t,r,n,l,o,i,u,a){var s=Array.prototype.slice.call(arguments,3);try{t.apply(r,s)}catch(f){this.onError(f)}}var el=!1,Do=null,Fo=!1,Ea=null,G0={onError:function(e){el=!0,Do=e}};function J0(e,t,r,n,l,o,i,u,a){el=!1,Do=null,Y0.apply(G0,arguments)}function X0(e,t,r,n,l,o,i,u,a){if(J0.apply(this,arguments),el){if(el){var s=Do;el=!1,Do=null}else throw Error(_(198));Fo||(Fo=!0,Ea=s)}}function Ur(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function $d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Os(e){if(Ur(e)!==e)throw Error(_(188))}function q0(e){var t=e.alternate;if(!t){if(t=Ur(e),t===null)throw Error(_(188));return t!==e?null:e}for(var r=e,n=t;;){var l=r.return;if(l===null)break;var o=l.alternate;if(o===null){if(n=l.return,n!==null){r=n;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===r)return Os(l),e;if(o===n)return Os(l),t;o=o.sibling}throw Error(_(188))}if(r.return!==n.return)r=l,n=o;else{for(var i=!1,u=l.child;u;){if(u===r){i=!0,r=l,n=o;break}if(u===n){i=!0,n=l,r=o;break}u=u.sibling}if(!i){for(u=o.child;u;){if(u===r){i=!0,r=o,n=l;break}if(u===n){i=!0,n=o,r=l;break}u=u.sibling}if(!i)throw Error(_(189))}}if(r.alternate!==n)throw Error(_(190))}if(r.tag!==3)throw Error(_(188));return r.stateNode.current===r?e:t}function jd(e){return e=q0(e),e!==null?Ud(e):null}function Ud(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Ud(e);if(t!==null)return t;e=e.sibling}return null}var Vd=lt.unstable_scheduleCallback,$s=lt.unstable_cancelCallback,Z0=lt.unstable_shouldYield,ep=lt.unstable_requestPaint,ge=lt.unstable_now,tp=lt.unstable_getCurrentPriorityLevel,Su=lt.unstable_ImmediatePriority,Hd=lt.unstable_UserBlockingPriority,Bo=lt.unstable_NormalPriority,rp=lt.unstable_LowPriority,Wd=lt.unstable_IdlePriority,oi=null,Rt=null;function np(e){if(Rt&&typeof Rt.onCommitFiberRoot=="function")try{Rt.onCommitFiberRoot(oi,e,void 0,(e.current.flags&128)===128)}catch{}}var kt=Math.clz32?Math.clz32:ip,lp=Math.log,op=Math.LN2;function ip(e){return e>>>=0,e===0?32:31-(lp(e)/op|0)|0}var Wl=64,Ql=4194304;function Gn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ro(e,t){var r=e.pendingLanes;if(r===0)return 0;var n=0,l=e.suspendedLanes,o=e.pingedLanes,i=r&268435455;if(i!==0){var u=i&~l;u!==0?n=Gn(u):(o&=i,o!==0&&(n=Gn(o)))}else i=r&~l,i!==0?n=Gn(i):o!==0&&(n=Gn(o));if(n===0)return 0;if(t!==0&&t!==n&&(t&l)===0&&(l=n&-n,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if((n&4)!==0&&(n|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=n;0<t;)r=31-kt(t),l=1<<r,n|=e[r],t&=~l;return n}function ap(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function up(e,t){for(var r=e.suspendedLanes,n=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-kt(o),u=1<<i,a=l[i];a===-1?((u&r)===0||(u&n)!==0)&&(l[i]=ap(u,t)):a<=t&&(e.expiredLanes|=u),o&=~u}}function Ca(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Qd(){var e=Wl;return Wl<<=1,(Wl&4194240)===0&&(Wl=64),e}function Ii(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function Rl(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-kt(t),e[t]=r}function sp(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var n=e.eventTimes;for(e=e.expirationTimes;0<r;){var l=31-kt(r),o=1<<l;t[l]=0,n[l]=-1,e[l]=-1,r&=~o}}function Eu(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var n=31-kt(r),l=1<<n;l&t|e[n]&t&&(e[n]|=t),r&=~l}}var oe=0;function Kd(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Yd,Cu,Gd,Jd,Xd,Na=!1,Kl=[],lr=null,or=null,ir=null,dl=new Map,fl=new Map,Zt=[],cp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function js(e,t){switch(e){case"focusin":case"focusout":lr=null;break;case"dragenter":case"dragleave":or=null;break;case"mouseover":case"mouseout":ir=null;break;case"pointerover":case"pointerout":dl.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":fl.delete(t.pointerId)}}function zn(e,t,r,n,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:r,eventSystemFlags:n,nativeEvent:o,targetContainers:[l]},t!==null&&(t=bl(t),t!==null&&Cu(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function dp(e,t,r,n,l){switch(t){case"focusin":return lr=zn(lr,e,t,r,n,l),!0;case"dragenter":return or=zn(or,e,t,r,n,l),!0;case"mouseover":return ir=zn(ir,e,t,r,n,l),!0;case"pointerover":var o=l.pointerId;return dl.set(o,zn(dl.get(o)||null,e,t,r,n,l)),!0;case"gotpointercapture":return o=l.pointerId,fl.set(o,zn(fl.get(o)||null,e,t,r,n,l)),!0}return!1}function qd(e){var t=Ar(e.target);if(t!==null){var r=Ur(t);if(r!==null){if(t=r.tag,t===13){if(t=$d(r),t!==null){e.blockedOn=t,Xd(e.priority,function(){Gd(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function so(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=Da(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var n=new r.constructor(r.type,r);wa=n,r.target.dispatchEvent(n),wa=null}else return t=bl(r),t!==null&&Cu(t),e.blockedOn=r,!1;t.shift()}return!0}function Us(e,t,r){so(e)&&r.delete(t)}function fp(){Na=!1,lr!==null&&so(lr)&&(lr=null),or!==null&&so(or)&&(or=null),ir!==null&&so(ir)&&(ir=null),dl.forEach(Us),fl.forEach(Us)}function On(e,t){e.blockedOn===t&&(e.blockedOn=null,Na||(Na=!0,lt.unstable_scheduleCallback(lt.unstable_NormalPriority,fp)))}function hl(e){function t(l){return On(l,e)}if(0<Kl.length){On(Kl[0],e);for(var r=1;r<Kl.length;r++){var n=Kl[r];n.blockedOn===e&&(n.blockedOn=null)}}for(lr!==null&&On(lr,e),or!==null&&On(or,e),ir!==null&&On(ir,e),dl.forEach(t),fl.forEach(t),r=0;r<Zt.length;r++)n=Zt[r],n.blockedOn===e&&(n.blockedOn=null);for(;0<Zt.length&&(r=Zt[0],r.blockedOn===null);)qd(r),r.blockedOn===null&&Zt.shift()}var mn=Ut.ReactCurrentBatchConfig,Ao=!0;function hp(e,t,r,n){var l=oe,o=mn.transition;mn.transition=null;try{oe=1,Nu(e,t,r,n)}finally{oe=l,mn.transition=o}}function pp(e,t,r,n){var l=oe,o=mn.transition;mn.transition=null;try{oe=4,Nu(e,t,r,n)}finally{oe=l,mn.transition=o}}function Nu(e,t,r,n){if(Ao){var l=Da(e,t,r,n);if(l===null)Wi(e,t,n,bo,r),js(e,n);else if(dp(l,e,t,r,n))n.stopPropagation();else if(js(e,n),t&4&&-1<cp.indexOf(e)){for(;l!==null;){var o=bl(l);if(o!==null&&Yd(o),o=Da(e,t,r,n),o===null&&Wi(e,t,n,bo,r),o===l)break;l=o}l!==null&&n.stopPropagation()}else Wi(e,t,n,null,r)}}var bo=null;function Da(e,t,r,n){if(bo=null,e=ku(n),e=Ar(e),e!==null)if(t=Ur(e),t===null)e=null;else if(r=t.tag,r===13){if(e=$d(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return bo=e,null}function Zd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(tp()){case Su:return 1;case Hd:return 4;case Bo:case rp:return 16;case Wd:return 536870912;default:return 16}default:return 16}}var rr=null,Du=null,co=null;function ef(){if(co)return co;var e,t=Du,r=t.length,n,l="value"in rr?rr.value:rr.textContent,o=l.length;for(e=0;e<r&&t[e]===l[e];e++);var i=r-e;for(n=1;n<=i&&t[r-n]===l[o-n];n++);return co=l.slice(e,1<n?1-n:void 0)}function fo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Yl(){return!0}function Vs(){return!1}function it(e){function t(r,n,l,o,i){this._reactName=r,this._targetInst=l,this.type=n,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var u in e)e.hasOwnProperty(u)&&(r=e[u],this[u]=r?r(o):o[u]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Yl:Vs,this.isPropagationStopped=Vs,this}return pe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=Yl)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=Yl)},persist:function(){},isPersistent:Yl}),t}var Bn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Fu=it(Bn),Al=pe({},Bn,{view:0,detail:0}),mp=it(Al),Mi,Ti,$n,ii=pe({},Al,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Bu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==$n&&($n&&e.type==="mousemove"?(Mi=e.screenX-$n.screenX,Ti=e.screenY-$n.screenY):Ti=Mi=0,$n=e),Mi)},movementY:function(e){return"movementY"in e?e.movementY:Ti}}),Hs=it(ii),yp=pe({},ii,{dataTransfer:0}),gp=it(yp),vp=pe({},Al,{relatedTarget:0}),zi=it(vp),xp=pe({},Bn,{animationName:0,elapsedTime:0,pseudoElement:0}),wp=it(xp),kp=pe({},Bn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Sp=it(kp),Ep=pe({},Bn,{data:0}),Ws=it(Ep),Cp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Np={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Dp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Fp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Dp[e])?!!t[e]:!1}function Bu(){return Fp}var Bp=pe({},Al,{key:function(e){if(e.key){var t=Cp[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=fo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Np[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Bu,charCode:function(e){return e.type==="keypress"?fo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?fo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Rp=it(Bp),Ap=pe({},ii,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Qs=it(Ap),bp=pe({},Al,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Bu}),Pp=it(bp),_p=pe({},Bn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Lp=it(_p),Ip=pe({},ii,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Mp=it(Ip),Tp=[9,13,27,32],Ru=zt&&"CompositionEvent"in window,tl=null;zt&&"documentMode"in document&&(tl=document.documentMode);var zp=zt&&"TextEvent"in window&&!tl,tf=zt&&(!Ru||tl&&8<tl&&11>=tl),Ks=String.fromCharCode(32),Ys=!1;function rf(e,t){switch(e){case"keyup":return Tp.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function nf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var en=!1;function Op(e,t){switch(e){case"compositionend":return nf(t);case"keypress":return t.which!==32?null:(Ys=!0,Ks);case"textInput":return e=t.data,e===Ks&&Ys?null:e;default:return null}}function $p(e,t){if(en)return e==="compositionend"||!Ru&&rf(e,t)?(e=ef(),co=Du=rr=null,en=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return tf&&t.locale!=="ko"?null:t.data;default:return null}}var jp={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Gs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!jp[e.type]:t==="textarea"}function lf(e,t,r,n){Id(n),t=Po(t,"onChange"),0<t.length&&(r=new Fu("onChange","change",null,r,n),e.push({event:r,listeners:t}))}var rl=null,pl=null;function Up(e){yf(e,0)}function ai(e){var t=nn(e);if(Bd(t))return e}function Vp(e,t){if(e==="change")return t}var of=!1;if(zt){var Oi;if(zt){var $i="oninput"in document;if(!$i){var Js=document.createElement("div");Js.setAttribute("oninput","return;"),$i=typeof Js.oninput=="function"}Oi=$i}else Oi=!1;of=Oi&&(!document.documentMode||9<document.documentMode)}function Xs(){rl&&(rl.detachEvent("onpropertychange",af),pl=rl=null)}function af(e){if(e.propertyName==="value"&&ai(pl)){var t=[];lf(t,pl,e,ku(e)),Od(Up,t)}}function Hp(e,t,r){e==="focusin"?(Xs(),rl=t,pl=r,rl.attachEvent("onpropertychange",af)):e==="focusout"&&Xs()}function Wp(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ai(pl)}function Qp(e,t){if(e==="click")return ai(t)}function Kp(e,t){if(e==="input"||e==="change")return ai(t)}function Yp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Et=typeof Object.is=="function"?Object.is:Yp;function ml(e,t){if(Et(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++){var l=r[n];if(!ua.call(t,l)||!Et(e[l],t[l]))return!1}return!0}function qs(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Zs(e,t){var r=qs(e);e=0;for(var n;r;){if(r.nodeType===3){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=qs(r)}}function uf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?uf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function sf(){for(var e=window,t=No();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=No(e.document)}return t}function Au(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Gp(e){var t=sf(),r=e.focusedElem,n=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&uf(r.ownerDocument.documentElement,r)){if(n!==null&&Au(r)){if(t=n.start,e=n.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=r.textContent.length,o=Math.min(n.start,l);n=n.end===void 0?o:Math.min(n.end,l),!e.extend&&o>n&&(l=n,n=o,o=l),l=Zs(r,o);var i=Zs(r,n);l&&i&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>n?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Jp=zt&&"documentMode"in document&&11>=document.documentMode,tn=null,Fa=null,nl=null,Ba=!1;function ec(e,t,r){var n=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;Ba||tn==null||tn!==No(n)||(n=tn,"selectionStart"in n&&Au(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),nl&&ml(nl,n)||(nl=n,n=Po(Fa,"onSelect"),0<n.length&&(t=new Fu("onSelect","select",null,t,r),e.push({event:t,listeners:n}),t.target=tn)))}function Gl(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var rn={animationend:Gl("Animation","AnimationEnd"),animationiteration:Gl("Animation","AnimationIteration"),animationstart:Gl("Animation","AnimationStart"),transitionend:Gl("Transition","TransitionEnd")},ji={},cf={};zt&&(cf=document.createElement("div").style,"AnimationEvent"in window||(delete rn.animationend.animation,delete rn.animationiteration.animation,delete rn.animationstart.animation),"TransitionEvent"in window||delete rn.transitionend.transition);function ui(e){if(ji[e])return ji[e];if(!rn[e])return e;var t=rn[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in cf)return ji[e]=t[r];return e}var df=ui("animationend"),ff=ui("animationiteration"),hf=ui("animationstart"),pf=ui("transitionend"),mf=new Map,tc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function mr(e,t){mf.set(e,t),jr(t,[e])}for(var Ui=0;Ui<tc.length;Ui++){var Vi=tc[Ui],Xp=Vi.toLowerCase(),qp=Vi[0].toUpperCase()+Vi.slice(1);mr(Xp,"on"+qp)}mr(df,"onAnimationEnd");mr(ff,"onAnimationIteration");mr(hf,"onAnimationStart");mr("dblclick","onDoubleClick");mr("focusin","onFocus");mr("focusout","onBlur");mr(pf,"onTransitionEnd");wn("onMouseEnter",["mouseout","mouseover"]);wn("onMouseLeave",["mouseout","mouseover"]);wn("onPointerEnter",["pointerout","pointerover"]);wn("onPointerLeave",["pointerout","pointerover"]);jr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));jr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));jr("onBeforeInput",["compositionend","keypress","textInput","paste"]);jr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));jr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));jr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Jn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Zp=new Set("cancel close invalid load scroll toggle".split(" ").concat(Jn));function rc(e,t,r){var n=e.type||"unknown-event";e.currentTarget=r,X0(n,t,void 0,e),e.currentTarget=null}function yf(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var n=e[r],l=n.event;n=n.listeners;e:{var o=void 0;if(t)for(var i=n.length-1;0<=i;i--){var u=n[i],a=u.instance,s=u.currentTarget;if(u=u.listener,a!==o&&l.isPropagationStopped())break e;rc(l,u,s),o=a}else for(i=0;i<n.length;i++){if(u=n[i],a=u.instance,s=u.currentTarget,u=u.listener,a!==o&&l.isPropagationStopped())break e;rc(l,u,s),o=a}}}if(Fo)throw e=Ea,Fo=!1,Ea=null,e}function se(e,t){var r=t[_a];r===void 0&&(r=t[_a]=new Set);var n=e+"__bubble";r.has(n)||(gf(t,e,2,!1),r.add(n))}function Hi(e,t,r){var n=0;t&&(n|=4),gf(r,e,n,t)}var Jl="_reactListening"+Math.random().toString(36).slice(2);function yl(e){if(!e[Jl]){e[Jl]=!0,Ed.forEach(function(r){r!=="selectionchange"&&(Zp.has(r)||Hi(r,!1,e),Hi(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Jl]||(t[Jl]=!0,Hi("selectionchange",!1,t))}}function gf(e,t,r,n){switch(Zd(t)){case 1:var l=hp;break;case 4:l=pp;break;default:l=Nu}r=l.bind(null,t,r,e),l=void 0,!Sa||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),n?l!==void 0?e.addEventListener(t,r,{capture:!0,passive:l}):e.addEventListener(t,r,!0):l!==void 0?e.addEventListener(t,r,{passive:l}):e.addEventListener(t,r,!1)}function Wi(e,t,r,n,l){var o=n;if((t&1)===0&&(t&2)===0&&n!==null)e:for(;;){if(n===null)return;var i=n.tag;if(i===3||i===4){var u=n.stateNode.containerInfo;if(u===l||u.nodeType===8&&u.parentNode===l)break;if(i===4)for(i=n.return;i!==null;){var a=i.tag;if((a===3||a===4)&&(a=i.stateNode.containerInfo,a===l||a.nodeType===8&&a.parentNode===l))return;i=i.return}for(;u!==null;){if(i=Ar(u),i===null)return;if(a=i.tag,a===5||a===6){n=o=i;continue e}u=u.parentNode}}n=n.return}Od(function(){var s=o,f=ku(r),y=[];e:{var h=mf.get(e);if(h!==void 0){var w=Fu,k=e;switch(e){case"keypress":if(fo(r)===0)break e;case"keydown":case"keyup":w=Rp;break;case"focusin":k="focus",w=zi;break;case"focusout":k="blur",w=zi;break;case"beforeblur":case"afterblur":w=zi;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=Hs;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=gp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=Pp;break;case df:case ff:case hf:w=wp;break;case pf:w=Lp;break;case"scroll":w=mp;break;case"wheel":w=Mp;break;case"copy":case"cut":case"paste":w=Sp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=Qs}var C=(t&4)!==0,A=!C&&e==="scroll",m=C?h!==null?h+"Capture":null:h;C=[];for(var p=s,c;p!==null;){c=p;var g=c.stateNode;if(c.tag===5&&g!==null&&(c=g,m!==null&&(g=cl(p,m),g!=null&&C.push(gl(p,g,c)))),A)break;p=p.return}0<C.length&&(h=new w(h,k,null,r,f),y.push({event:h,listeners:C}))}}if((t&7)===0){e:{if(h=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",h&&r!==wa&&(k=r.relatedTarget||r.fromElement)&&(Ar(k)||k[Ot]))break e;if((w||h)&&(h=f.window===f?f:(h=f.ownerDocument)?h.defaultView||h.parentWindow:window,w?(k=r.relatedTarget||r.toElement,w=s,k=k?Ar(k):null,k!==null&&(A=Ur(k),k!==A||k.tag!==5&&k.tag!==6)&&(k=null)):(w=null,k=s),w!==k)){if(C=Hs,g="onMouseLeave",m="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(C=Qs,g="onPointerLeave",m="onPointerEnter",p="pointer"),A=w==null?h:nn(w),c=k==null?h:nn(k),h=new C(g,p+"leave",w,r,f),h.target=A,h.relatedTarget=c,g=null,Ar(f)===s&&(C=new C(m,p+"enter",k,r,f),C.target=c,C.relatedTarget=A,g=C),A=g,w&&k)t:{for(C=w,m=k,p=0,c=C;c;c=Gr(c))p++;for(c=0,g=m;g;g=Gr(g))c++;for(;0<p-c;)C=Gr(C),p--;for(;0<c-p;)m=Gr(m),c--;for(;p--;){if(C===m||m!==null&&C===m.alternate)break t;C=Gr(C),m=Gr(m)}C=null}else C=null;w!==null&&nc(y,h,w,C,!1),k!==null&&A!==null&&nc(y,A,k,C,!0)}}e:{if(h=s?nn(s):window,w=h.nodeName&&h.nodeName.toLowerCase(),w==="select"||w==="input"&&h.type==="file")var B=Vp;else if(Gs(h))if(of)B=Kp;else{B=Wp;var v=Hp}else(w=h.nodeName)&&w.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(B=Qp);if(B&&(B=B(e,s))){lf(y,B,r,f);break e}v&&v(e,h,s),e==="focusout"&&(v=h._wrapperState)&&v.controlled&&h.type==="number"&&ma(h,"number",h.value)}switch(v=s?nn(s):window,e){case"focusin":(Gs(v)||v.contentEditable==="true")&&(tn=v,Fa=s,nl=null);break;case"focusout":nl=Fa=tn=null;break;case"mousedown":Ba=!0;break;case"contextmenu":case"mouseup":case"dragend":Ba=!1,ec(y,r,f);break;case"selectionchange":if(Jp)break;case"keydown":case"keyup":ec(y,r,f)}var R;if(Ru)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else en?rf(e,r)&&(b="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(b="onCompositionStart");b&&(tf&&r.locale!=="ko"&&(en||b!=="onCompositionStart"?b==="onCompositionEnd"&&en&&(R=ef()):(rr=f,Du="value"in rr?rr.value:rr.textContent,en=!0)),v=Po(s,b),0<v.length&&(b=new Ws(b,e,null,r,f),y.push({event:b,listeners:v}),R?b.data=R:(R=nf(r),R!==null&&(b.data=R)))),(R=zp?Op(e,r):$p(e,r))&&(s=Po(s,"onBeforeInput"),0<s.length&&(f=new Ws("onBeforeInput","beforeinput",null,r,f),y.push({event:f,listeners:s}),f.data=R))}yf(y,t)})}function gl(e,t,r){return{instance:e,listener:t,currentTarget:r}}function Po(e,t){for(var r=t+"Capture",n=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=cl(e,r),o!=null&&n.unshift(gl(e,o,l)),o=cl(e,t),o!=null&&n.push(gl(e,o,l))),e=e.return}return n}function Gr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function nc(e,t,r,n,l){for(var o=t._reactName,i=[];r!==null&&r!==n;){var u=r,a=u.alternate,s=u.stateNode;if(a!==null&&a===n)break;u.tag===5&&s!==null&&(u=s,l?(a=cl(r,o),a!=null&&i.unshift(gl(r,a,u))):l||(a=cl(r,o),a!=null&&i.push(gl(r,a,u)))),r=r.return}i.length!==0&&e.push({event:t,listeners:i})}var em=/\r\n?/g,tm=/\u0000|\uFFFD/g;function lc(e){return(typeof e=="string"?e:""+e).replace(em,`
`).replace(tm,"")}function Xl(e,t,r){if(t=lc(t),lc(e)!==t&&r)throw Error(_(425))}function _o(){}var Ra=null,Aa=null;function ba(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Pa=typeof setTimeout=="function"?setTimeout:void 0,rm=typeof clearTimeout=="function"?clearTimeout:void 0,oc=typeof Promise=="function"?Promise:void 0,nm=typeof queueMicrotask=="function"?queueMicrotask:typeof oc<"u"?function(e){return oc.resolve(null).then(e).catch(lm)}:Pa;function lm(e){setTimeout(function(){throw e})}function Qi(e,t){var r=t,n=0;do{var l=r.nextSibling;if(e.removeChild(r),l&&l.nodeType===8)if(r=l.data,r==="/$"){if(n===0){e.removeChild(l),hl(t);return}n--}else r!=="$"&&r!=="$?"&&r!=="$!"||n++;r=l}while(r);hl(t)}function ar(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ic(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var Rn=Math.random().toString(36).slice(2),Bt="__reactFiber$"+Rn,vl="__reactProps$"+Rn,Ot="__reactContainer$"+Rn,_a="__reactEvents$"+Rn,om="__reactListeners$"+Rn,im="__reactHandles$"+Rn;function Ar(e){var t=e[Bt];if(t)return t;for(var r=e.parentNode;r;){if(t=r[Ot]||r[Bt]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=ic(e);e!==null;){if(r=e[Bt])return r;e=ic(e)}return t}e=r,r=e.parentNode}return null}function bl(e){return e=e[Bt]||e[Ot],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function nn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(_(33))}function si(e){return e[vl]||null}var La=[],ln=-1;function yr(e){return{current:e}}function ce(e){0>ln||(e.current=La[ln],La[ln]=null,ln--)}function ue(e,t){ln++,La[ln]=e.current,e.current=t}var hr={},ze=yr(hr),Ge=yr(!1),Mr=hr;function kn(e,t){var r=e.type.contextTypes;if(!r)return hr;var n=e.stateNode;if(n&&n.__reactInternalMemoizedUnmaskedChildContext===t)return n.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in r)l[o]=t[o];return n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Je(e){return e=e.childContextTypes,e!=null}function Lo(){ce(Ge),ce(ze)}function ac(e,t,r){if(ze.current!==hr)throw Error(_(168));ue(ze,t),ue(Ge,r)}function vf(e,t,r){var n=e.stateNode;if(t=t.childContextTypes,typeof n.getChildContext!="function")return r;n=n.getChildContext();for(var l in n)if(!(l in t))throw Error(_(108,H0(e)||"Unknown",l));return pe({},r,n)}function Io(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||hr,Mr=ze.current,ue(ze,e),ue(Ge,Ge.current),!0}function uc(e,t,r){var n=e.stateNode;if(!n)throw Error(_(169));r?(e=vf(e,t,Mr),n.__reactInternalMemoizedMergedChildContext=e,ce(Ge),ce(ze),ue(ze,e)):ce(Ge),ue(Ge,r)}var Lt=null,ci=!1,Ki=!1;function xf(e){Lt===null?Lt=[e]:Lt.push(e)}function am(e){ci=!0,xf(e)}function gr(){if(!Ki&&Lt!==null){Ki=!0;var e=0,t=oe;try{var r=Lt;for(oe=1;e<r.length;e++){var n=r[e];do n=n(!0);while(n!==null)}Lt=null,ci=!1}catch(l){throw Lt!==null&&(Lt=Lt.slice(e+1)),Vd(Su,gr),l}finally{oe=t,Ki=!1}}return null}var on=[],an=0,Mo=null,To=0,ct=[],dt=0,Tr=null,It=1,Mt="";function Fr(e,t){on[an++]=To,on[an++]=Mo,Mo=e,To=t}function wf(e,t,r){ct[dt++]=It,ct[dt++]=Mt,ct[dt++]=Tr,Tr=e;var n=It;e=Mt;var l=32-kt(n)-1;n&=~(1<<l),r+=1;var o=32-kt(t)+l;if(30<o){var i=l-l%5;o=(n&(1<<i)-1).toString(32),n>>=i,l-=i,It=1<<32-kt(t)+l|r<<l|n,Mt=o+e}else It=1<<o|r<<l|n,Mt=e}function bu(e){e.return!==null&&(Fr(e,1),wf(e,1,0))}function Pu(e){for(;e===Mo;)Mo=on[--an],on[an]=null,To=on[--an],on[an]=null;for(;e===Tr;)Tr=ct[--dt],ct[dt]=null,Mt=ct[--dt],ct[dt]=null,It=ct[--dt],ct[dt]=null}var nt=null,rt=null,de=!1,wt=null;function kf(e,t){var r=ft(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function sc(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,nt=e,rt=ar(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,nt=e,rt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=Tr!==null?{id:It,overflow:Mt}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=ft(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,nt=e,rt=null,!0):!1;default:return!1}}function Ia(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ma(e){if(de){var t=rt;if(t){var r=t;if(!sc(e,t)){if(Ia(e))throw Error(_(418));t=ar(r.nextSibling);var n=nt;t&&sc(e,t)?kf(n,r):(e.flags=e.flags&-4097|2,de=!1,nt=e)}}else{if(Ia(e))throw Error(_(418));e.flags=e.flags&-4097|2,de=!1,nt=e}}}function cc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;nt=e}function ql(e){if(e!==nt)return!1;if(!de)return cc(e),de=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ba(e.type,e.memoizedProps)),t&&(t=rt)){if(Ia(e))throw Sf(),Error(_(418));for(;t;)kf(e,t),t=ar(t.nextSibling)}if(cc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){rt=ar(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}rt=null}}else rt=nt?ar(e.stateNode.nextSibling):null;return!0}function Sf(){for(var e=rt;e;)e=ar(e.nextSibling)}function Sn(){rt=nt=null,de=!1}function _u(e){wt===null?wt=[e]:wt.push(e)}var um=Ut.ReactCurrentBatchConfig;function jn(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(_(309));var n=r.stateNode}if(!n)throw Error(_(147,e));var l=n,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var u=l.refs;i===null?delete u[o]:u[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(_(284));if(!r._owner)throw Error(_(290,e))}return e}function Zl(e,t){throw e=Object.prototype.toString.call(t),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function dc(e){var t=e._init;return t(e._payload)}function Ef(e){function t(m,p){if(e){var c=m.deletions;c===null?(m.deletions=[p],m.flags|=16):c.push(p)}}function r(m,p){if(!e)return null;for(;p!==null;)t(m,p),p=p.sibling;return null}function n(m,p){for(m=new Map;p!==null;)p.key!==null?m.set(p.key,p):m.set(p.index,p),p=p.sibling;return m}function l(m,p){return m=dr(m,p),m.index=0,m.sibling=null,m}function o(m,p,c){return m.index=c,e?(c=m.alternate,c!==null?(c=c.index,c<p?(m.flags|=2,p):c):(m.flags|=2,p)):(m.flags|=1048576,p)}function i(m){return e&&m.alternate===null&&(m.flags|=2),m}function u(m,p,c,g){return p===null||p.tag!==6?(p=ea(c,m.mode,g),p.return=m,p):(p=l(p,c),p.return=m,p)}function a(m,p,c,g){var B=c.type;return B===Zr?f(m,p,c.props.children,g,c.key):p!==null&&(p.elementType===B||typeof B=="object"&&B!==null&&B.$$typeof===Xt&&dc(B)===p.type)?(g=l(p,c.props),g.ref=jn(m,p,c),g.return=m,g):(g=xo(c.type,c.key,c.props,null,m.mode,g),g.ref=jn(m,p,c),g.return=m,g)}function s(m,p,c,g){return p===null||p.tag!==4||p.stateNode.containerInfo!==c.containerInfo||p.stateNode.implementation!==c.implementation?(p=ta(c,m.mode,g),p.return=m,p):(p=l(p,c.children||[]),p.return=m,p)}function f(m,p,c,g,B){return p===null||p.tag!==7?(p=Ir(c,m.mode,g,B),p.return=m,p):(p=l(p,c),p.return=m,p)}function y(m,p,c){if(typeof p=="string"&&p!==""||typeof p=="number")return p=ea(""+p,m.mode,c),p.return=m,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Ul:return c=xo(p.type,p.key,p.props,null,m.mode,c),c.ref=jn(m,null,p),c.return=m,c;case qr:return p=ta(p,m.mode,c),p.return=m,p;case Xt:var g=p._init;return y(m,g(p._payload),c)}if(Yn(p)||Mn(p))return p=Ir(p,m.mode,c,null),p.return=m,p;Zl(m,p)}return null}function h(m,p,c,g){var B=p!==null?p.key:null;if(typeof c=="string"&&c!==""||typeof c=="number")return B!==null?null:u(m,p,""+c,g);if(typeof c=="object"&&c!==null){switch(c.$$typeof){case Ul:return c.key===B?a(m,p,c,g):null;case qr:return c.key===B?s(m,p,c,g):null;case Xt:return B=c._init,h(m,p,B(c._payload),g)}if(Yn(c)||Mn(c))return B!==null?null:f(m,p,c,g,null);Zl(m,c)}return null}function w(m,p,c,g,B){if(typeof g=="string"&&g!==""||typeof g=="number")return m=m.get(c)||null,u(p,m,""+g,B);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Ul:return m=m.get(g.key===null?c:g.key)||null,a(p,m,g,B);case qr:return m=m.get(g.key===null?c:g.key)||null,s(p,m,g,B);case Xt:var v=g._init;return w(m,p,c,v(g._payload),B)}if(Yn(g)||Mn(g))return m=m.get(c)||null,f(p,m,g,B,null);Zl(p,g)}return null}function k(m,p,c,g){for(var B=null,v=null,R=p,b=p=0,z=null;R!==null&&b<c.length;b++){R.index>b?(z=R,R=null):z=R.sibling;var D=h(m,R,c[b],g);if(D===null){R===null&&(R=z);break}e&&R&&D.alternate===null&&t(m,R),p=o(D,p,b),v===null?B=D:v.sibling=D,v=D,R=z}if(b===c.length)return r(m,R),de&&Fr(m,b),B;if(R===null){for(;b<c.length;b++)R=y(m,c[b],g),R!==null&&(p=o(R,p,b),v===null?B=R:v.sibling=R,v=R);return de&&Fr(m,b),B}for(R=n(m,R);b<c.length;b++)z=w(R,m,b,c[b],g),z!==null&&(e&&z.alternate!==null&&R.delete(z.key===null?b:z.key),p=o(z,p,b),v===null?B=z:v.sibling=z,v=z);return e&&R.forEach(function(j){return t(m,j)}),de&&Fr(m,b),B}function C(m,p,c,g){var B=Mn(c);if(typeof B!="function")throw Error(_(150));if(c=B.call(c),c==null)throw Error(_(151));for(var v=B=null,R=p,b=p=0,z=null,D=c.next();R!==null&&!D.done;b++,D=c.next()){R.index>b?(z=R,R=null):z=R.sibling;var j=h(m,R,D.value,g);if(j===null){R===null&&(R=z);break}e&&R&&j.alternate===null&&t(m,R),p=o(j,p,b),v===null?B=j:v.sibling=j,v=j,R=z}if(D.done)return r(m,R),de&&Fr(m,b),B;if(R===null){for(;!D.done;b++,D=c.next())D=y(m,D.value,g),D!==null&&(p=o(D,p,b),v===null?B=D:v.sibling=D,v=D);return de&&Fr(m,b),B}for(R=n(m,R);!D.done;b++,D=c.next())D=w(R,m,b,D.value,g),D!==null&&(e&&D.alternate!==null&&R.delete(D.key===null?b:D.key),p=o(D,p,b),v===null?B=D:v.sibling=D,v=D);return e&&R.forEach(function(Fe){return t(m,Fe)}),de&&Fr(m,b),B}function A(m,p,c,g){if(typeof c=="object"&&c!==null&&c.type===Zr&&c.key===null&&(c=c.props.children),typeof c=="object"&&c!==null){switch(c.$$typeof){case Ul:e:{for(var B=c.key,v=p;v!==null;){if(v.key===B){if(B=c.type,B===Zr){if(v.tag===7){r(m,v.sibling),p=l(v,c.props.children),p.return=m,m=p;break e}}else if(v.elementType===B||typeof B=="object"&&B!==null&&B.$$typeof===Xt&&dc(B)===v.type){r(m,v.sibling),p=l(v,c.props),p.ref=jn(m,v,c),p.return=m,m=p;break e}r(m,v);break}else t(m,v);v=v.sibling}c.type===Zr?(p=Ir(c.props.children,m.mode,g,c.key),p.return=m,m=p):(g=xo(c.type,c.key,c.props,null,m.mode,g),g.ref=jn(m,p,c),g.return=m,m=g)}return i(m);case qr:e:{for(v=c.key;p!==null;){if(p.key===v)if(p.tag===4&&p.stateNode.containerInfo===c.containerInfo&&p.stateNode.implementation===c.implementation){r(m,p.sibling),p=l(p,c.children||[]),p.return=m,m=p;break e}else{r(m,p);break}else t(m,p);p=p.sibling}p=ta(c,m.mode,g),p.return=m,m=p}return i(m);case Xt:return v=c._init,A(m,p,v(c._payload),g)}if(Yn(c))return k(m,p,c,g);if(Mn(c))return C(m,p,c,g);Zl(m,c)}return typeof c=="string"&&c!==""||typeof c=="number"?(c=""+c,p!==null&&p.tag===6?(r(m,p.sibling),p=l(p,c),p.return=m,m=p):(r(m,p),p=ea(c,m.mode,g),p.return=m,m=p),i(m)):r(m,p)}return A}var En=Ef(!0),Cf=Ef(!1),zo=yr(null),Oo=null,un=null,Lu=null;function Iu(){Lu=un=Oo=null}function Mu(e){var t=zo.current;ce(zo),e._currentValue=t}function Ta(e,t,r){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===r)break;e=e.return}}function yn(e,t){Oo=e,Lu=un=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(Ye=!0),e.firstContext=null)}function pt(e){var t=e._currentValue;if(Lu!==e)if(e={context:e,memoizedValue:t,next:null},un===null){if(Oo===null)throw Error(_(308));un=e,Oo.dependencies={lanes:0,firstContext:e}}else un=un.next=e;return t}var br=null;function Tu(e){br===null?br=[e]:br.push(e)}function Nf(e,t,r,n){var l=t.interleaved;return l===null?(r.next=r,Tu(t)):(r.next=l.next,l.next=r),t.interleaved=r,$t(e,n)}function $t(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var qt=!1;function zu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Df(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Tt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function ur(e,t,r){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,(te&2)!==0){var l=n.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),n.pending=t,$t(e,r)}return l=n.interleaved,l===null?(t.next=t,Tu(n)):(t.next=l.next,l.next=t),n.interleaved=t,$t(e,r)}function ho(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Eu(e,r)}}function fc(e,t){var r=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,r===n)){var l=null,o=null;if(r=r.firstBaseUpdate,r!==null){do{var i={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};o===null?l=o=i:o=o.next=i,r=r.next}while(r!==null);o===null?l=o=t:o=o.next=t}else l=o=t;r={baseState:n.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:n.shared,effects:n.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function $o(e,t,r,n){var l=e.updateQueue;qt=!1;var o=l.firstBaseUpdate,i=l.lastBaseUpdate,u=l.shared.pending;if(u!==null){l.shared.pending=null;var a=u,s=a.next;a.next=null,i===null?o=s:i.next=s,i=a;var f=e.alternate;f!==null&&(f=f.updateQueue,u=f.lastBaseUpdate,u!==i&&(u===null?f.firstBaseUpdate=s:u.next=s,f.lastBaseUpdate=a))}if(o!==null){var y=l.baseState;i=0,f=s=a=null,u=o;do{var h=u.lane,w=u.eventTime;if((n&h)===h){f!==null&&(f=f.next={eventTime:w,lane:0,tag:u.tag,payload:u.payload,callback:u.callback,next:null});e:{var k=e,C=u;switch(h=t,w=r,C.tag){case 1:if(k=C.payload,typeof k=="function"){y=k.call(w,y,h);break e}y=k;break e;case 3:k.flags=k.flags&-65537|128;case 0:if(k=C.payload,h=typeof k=="function"?k.call(w,y,h):k,h==null)break e;y=pe({},y,h);break e;case 2:qt=!0}}u.callback!==null&&u.lane!==0&&(e.flags|=64,h=l.effects,h===null?l.effects=[u]:h.push(u))}else w={eventTime:w,lane:h,tag:u.tag,payload:u.payload,callback:u.callback,next:null},f===null?(s=f=w,a=y):f=f.next=w,i|=h;if(u=u.next,u===null){if(u=l.shared.pending,u===null)break;h=u,u=h.next,h.next=null,l.lastBaseUpdate=h,l.shared.pending=null}}while(1);if(f===null&&(a=y),l.baseState=a,l.firstBaseUpdate=s,l.lastBaseUpdate=f,t=l.shared.interleaved,t!==null){l=t;do i|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);Or|=i,e.lanes=i,e.memoizedState=y}}function hc(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var n=e[t],l=n.callback;if(l!==null){if(n.callback=null,n=r,typeof l!="function")throw Error(_(191,l));l.call(n)}}}var Pl={},At=yr(Pl),xl=yr(Pl),wl=yr(Pl);function Pr(e){if(e===Pl)throw Error(_(174));return e}function Ou(e,t){switch(ue(wl,t),ue(xl,e),ue(At,Pl),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ga(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ga(t,e)}ce(At),ue(At,t)}function Cn(){ce(At),ce(xl),ce(wl)}function Ff(e){Pr(wl.current);var t=Pr(At.current),r=ga(t,e.type);t!==r&&(ue(xl,e),ue(At,r))}function $u(e){xl.current===e&&(ce(At),ce(xl))}var fe=yr(0);function jo(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Yi=[];function ju(){for(var e=0;e<Yi.length;e++)Yi[e]._workInProgressVersionPrimary=null;Yi.length=0}var po=Ut.ReactCurrentDispatcher,Gi=Ut.ReactCurrentBatchConfig,zr=0,he=null,Ce=null,Be=null,Uo=!1,ll=!1,kl=0,sm=0;function Ie(){throw Error(_(321))}function Uu(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!Et(e[r],t[r]))return!1;return!0}function Vu(e,t,r,n,l,o){if(zr=o,he=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,po.current=e===null||e.memoizedState===null?hm:pm,e=r(n,l),ll){o=0;do{if(ll=!1,kl=0,25<=o)throw Error(_(301));o+=1,Be=Ce=null,t.updateQueue=null,po.current=mm,e=r(n,l)}while(ll)}if(po.current=Vo,t=Ce!==null&&Ce.next!==null,zr=0,Be=Ce=he=null,Uo=!1,t)throw Error(_(300));return e}function Hu(){var e=kl!==0;return kl=0,e}function Ft(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Be===null?he.memoizedState=Be=e:Be=Be.next=e,Be}function mt(){if(Ce===null){var e=he.alternate;e=e!==null?e.memoizedState:null}else e=Ce.next;var t=Be===null?he.memoizedState:Be.next;if(t!==null)Be=t,Ce=e;else{if(e===null)throw Error(_(310));Ce=e,e={memoizedState:Ce.memoizedState,baseState:Ce.baseState,baseQueue:Ce.baseQueue,queue:Ce.queue,next:null},Be===null?he.memoizedState=Be=e:Be=Be.next=e}return Be}function Sl(e,t){return typeof t=="function"?t(e):t}function Ji(e){var t=mt(),r=t.queue;if(r===null)throw Error(_(311));r.lastRenderedReducer=e;var n=Ce,l=n.baseQueue,o=r.pending;if(o!==null){if(l!==null){var i=l.next;l.next=o.next,o.next=i}n.baseQueue=l=o,r.pending=null}if(l!==null){o=l.next,n=n.baseState;var u=i=null,a=null,s=o;do{var f=s.lane;if((zr&f)===f)a!==null&&(a=a.next={lane:0,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null}),n=s.hasEagerState?s.eagerState:e(n,s.action);else{var y={lane:f,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null};a===null?(u=a=y,i=n):a=a.next=y,he.lanes|=f,Or|=f}s=s.next}while(s!==null&&s!==o);a===null?i=n:a.next=u,Et(n,t.memoizedState)||(Ye=!0),t.memoizedState=n,t.baseState=i,t.baseQueue=a,r.lastRenderedState=n}if(e=r.interleaved,e!==null){l=e;do o=l.lane,he.lanes|=o,Or|=o,l=l.next;while(l!==e)}else l===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function Xi(e){var t=mt(),r=t.queue;if(r===null)throw Error(_(311));r.lastRenderedReducer=e;var n=r.dispatch,l=r.pending,o=t.memoizedState;if(l!==null){r.pending=null;var i=l=l.next;do o=e(o,i.action),i=i.next;while(i!==l);Et(o,t.memoizedState)||(Ye=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),r.lastRenderedState=o}return[o,n]}function Bf(){}function Rf(e,t){var r=he,n=mt(),l=t(),o=!Et(n.memoizedState,l);if(o&&(n.memoizedState=l,Ye=!0),n=n.queue,Wu(Pf.bind(null,r,n,e),[e]),n.getSnapshot!==t||o||Be!==null&&Be.memoizedState.tag&1){if(r.flags|=2048,El(9,bf.bind(null,r,n,l,t),void 0,null),Re===null)throw Error(_(349));(zr&30)!==0||Af(r,t,l)}return l}function Af(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=he.updateQueue,t===null?(t={lastEffect:null,stores:null},he.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function bf(e,t,r,n){t.value=r,t.getSnapshot=n,_f(t)&&Lf(e)}function Pf(e,t,r){return r(function(){_f(t)&&Lf(e)})}function _f(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!Et(e,r)}catch{return!0}}function Lf(e){var t=$t(e,1);t!==null&&St(t,e,1,-1)}function pc(e){var t=Ft();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Sl,lastRenderedState:e},t.queue=e,e=e.dispatch=fm.bind(null,he,e),[t.memoizedState,e]}function El(e,t,r,n){return e={tag:e,create:t,destroy:r,deps:n,next:null},t=he.updateQueue,t===null?(t={lastEffect:null,stores:null},he.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e)),e}function If(){return mt().memoizedState}function mo(e,t,r,n){var l=Ft();he.flags|=e,l.memoizedState=El(1|t,r,void 0,n===void 0?null:n)}function di(e,t,r,n){var l=mt();n=n===void 0?null:n;var o=void 0;if(Ce!==null){var i=Ce.memoizedState;if(o=i.destroy,n!==null&&Uu(n,i.deps)){l.memoizedState=El(t,r,o,n);return}}he.flags|=e,l.memoizedState=El(1|t,r,o,n)}function mc(e,t){return mo(8390656,8,e,t)}function Wu(e,t){return di(2048,8,e,t)}function Mf(e,t){return di(4,2,e,t)}function Tf(e,t){return di(4,4,e,t)}function zf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Of(e,t,r){return r=r!=null?r.concat([e]):null,di(4,4,zf.bind(null,t,e),r)}function Qu(){}function $f(e,t){var r=mt();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&Uu(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function jf(e,t){var r=mt();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&Uu(t,n[1])?n[0]:(e=e(),r.memoizedState=[e,t],e)}function Uf(e,t,r){return(zr&21)===0?(e.baseState&&(e.baseState=!1,Ye=!0),e.memoizedState=r):(Et(r,t)||(r=Qd(),he.lanes|=r,Or|=r,e.baseState=!0),t)}function cm(e,t){var r=oe;oe=r!==0&&4>r?r:4,e(!0);var n=Gi.transition;Gi.transition={};try{e(!1),t()}finally{oe=r,Gi.transition=n}}function Vf(){return mt().memoizedState}function dm(e,t,r){var n=cr(e);if(r={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null},Hf(e))Wf(t,r);else if(r=Nf(e,t,r,n),r!==null){var l=He();St(r,e,n,l),Qf(r,t,n)}}function fm(e,t,r){var n=cr(e),l={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null};if(Hf(e))Wf(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,u=o(i,r);if(l.hasEagerState=!0,l.eagerState=u,Et(u,i)){var a=t.interleaved;a===null?(l.next=l,Tu(t)):(l.next=a.next,a.next=l),t.interleaved=l;return}}catch{}finally{}r=Nf(e,t,l,n),r!==null&&(l=He(),St(r,e,n,l),Qf(r,t,n))}}function Hf(e){var t=e.alternate;return e===he||t!==null&&t===he}function Wf(e,t){ll=Uo=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function Qf(e,t,r){if((r&4194240)!==0){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Eu(e,r)}}var Vo={readContext:pt,useCallback:Ie,useContext:Ie,useEffect:Ie,useImperativeHandle:Ie,useInsertionEffect:Ie,useLayoutEffect:Ie,useMemo:Ie,useReducer:Ie,useRef:Ie,useState:Ie,useDebugValue:Ie,useDeferredValue:Ie,useTransition:Ie,useMutableSource:Ie,useSyncExternalStore:Ie,useId:Ie,unstable_isNewReconciler:!1},hm={readContext:pt,useCallback:function(e,t){return Ft().memoizedState=[e,t===void 0?null:t],e},useContext:pt,useEffect:mc,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,mo(4194308,4,zf.bind(null,t,e),r)},useLayoutEffect:function(e,t){return mo(4194308,4,e,t)},useInsertionEffect:function(e,t){return mo(4,2,e,t)},useMemo:function(e,t){var r=Ft();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var n=Ft();return t=r!==void 0?r(t):t,n.memoizedState=n.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},n.queue=e,e=e.dispatch=dm.bind(null,he,e),[n.memoizedState,e]},useRef:function(e){var t=Ft();return e={current:e},t.memoizedState=e},useState:pc,useDebugValue:Qu,useDeferredValue:function(e){return Ft().memoizedState=e},useTransition:function(){var e=pc(!1),t=e[0];return e=cm.bind(null,e[1]),Ft().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var n=he,l=Ft();if(de){if(r===void 0)throw Error(_(407));r=r()}else{if(r=t(),Re===null)throw Error(_(349));(zr&30)!==0||Af(n,t,r)}l.memoizedState=r;var o={value:r,getSnapshot:t};return l.queue=o,mc(Pf.bind(null,n,o,e),[e]),n.flags|=2048,El(9,bf.bind(null,n,o,r,t),void 0,null),r},useId:function(){var e=Ft(),t=Re.identifierPrefix;if(de){var r=Mt,n=It;r=(n&~(1<<32-kt(n)-1)).toString(32)+r,t=":"+t+"R"+r,r=kl++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=sm++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},pm={readContext:pt,useCallback:$f,useContext:pt,useEffect:Wu,useImperativeHandle:Of,useInsertionEffect:Mf,useLayoutEffect:Tf,useMemo:jf,useReducer:Ji,useRef:If,useState:function(){return Ji(Sl)},useDebugValue:Qu,useDeferredValue:function(e){var t=mt();return Uf(t,Ce.memoizedState,e)},useTransition:function(){var e=Ji(Sl)[0],t=mt().memoizedState;return[e,t]},useMutableSource:Bf,useSyncExternalStore:Rf,useId:Vf,unstable_isNewReconciler:!1},mm={readContext:pt,useCallback:$f,useContext:pt,useEffect:Wu,useImperativeHandle:Of,useInsertionEffect:Mf,useLayoutEffect:Tf,useMemo:jf,useReducer:Xi,useRef:If,useState:function(){return Xi(Sl)},useDebugValue:Qu,useDeferredValue:function(e){var t=mt();return Ce===null?t.memoizedState=e:Uf(t,Ce.memoizedState,e)},useTransition:function(){var e=Xi(Sl)[0],t=mt().memoizedState;return[e,t]},useMutableSource:Bf,useSyncExternalStore:Rf,useId:Vf,unstable_isNewReconciler:!1};function vt(e,t){if(e&&e.defaultProps){t=pe({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function za(e,t,r,n){t=e.memoizedState,r=r(n,t),r=r==null?t:pe({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var fi={isMounted:function(e){return(e=e._reactInternals)?Ur(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var n=He(),l=cr(e),o=Tt(n,l);o.payload=t,r!=null&&(o.callback=r),t=ur(e,o,l),t!==null&&(St(t,e,l,n),ho(t,e,l))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var n=He(),l=cr(e),o=Tt(n,l);o.tag=1,o.payload=t,r!=null&&(o.callback=r),t=ur(e,o,l),t!==null&&(St(t,e,l,n),ho(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=He(),n=cr(e),l=Tt(r,n);l.tag=2,t!=null&&(l.callback=t),t=ur(e,l,n),t!==null&&(St(t,e,n,r),ho(t,e,n))}};function yc(e,t,r,n,l,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,o,i):t.prototype&&t.prototype.isPureReactComponent?!ml(r,n)||!ml(l,o):!0}function Kf(e,t,r){var n=!1,l=hr,o=t.contextType;return typeof o=="object"&&o!==null?o=pt(o):(l=Je(t)?Mr:ze.current,n=t.contextTypes,o=(n=n!=null)?kn(e,l):hr),t=new t(r,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=fi,e.stateNode=t,t._reactInternals=e,n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function gc(e,t,r,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&fi.enqueueReplaceState(t,t.state,null)}function Oa(e,t,r,n){var l=e.stateNode;l.props=r,l.state=e.memoizedState,l.refs={},zu(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=pt(o):(o=Je(t)?Mr:ze.current,l.context=kn(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(za(e,t,o,r),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&fi.enqueueReplaceState(l,l.state,null),$o(e,r,l,n),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function Nn(e,t){try{var r="",n=t;do r+=V0(n),n=n.return;while(n);var l=r}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function qi(e,t,r){return{value:e,source:null,stack:r!=null?r:null,digest:t!=null?t:null}}function $a(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var ym=typeof WeakMap=="function"?WeakMap:Map;function Yf(e,t,r){r=Tt(-1,r),r.tag=3,r.payload={element:null};var n=t.value;return r.callback=function(){Wo||(Wo=!0,Ja=n),$a(e,t)},r}function Gf(e,t,r){r=Tt(-1,r),r.tag=3;var n=e.type.getDerivedStateFromError;if(typeof n=="function"){var l=t.value;r.payload=function(){return n(l)},r.callback=function(){$a(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(r.callback=function(){$a(e,t),typeof n!="function"&&(sr===null?sr=new Set([this]):sr.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),r}function vc(e,t,r){var n=e.pingCache;if(n===null){n=e.pingCache=new ym;var l=new Set;n.set(t,l)}else l=n.get(t),l===void 0&&(l=new Set,n.set(t,l));l.has(r)||(l.add(r),e=Am.bind(null,e,t,r),t.then(e,e))}function xc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function wc(e,t,r,n,l){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=Tt(-1,1),t.tag=2,ur(r,t,1))),r.lanes|=1),e):(e.flags|=65536,e.lanes=l,e)}var gm=Ut.ReactCurrentOwner,Ye=!1;function Ve(e,t,r,n){t.child=e===null?Cf(t,null,r,n):En(t,e.child,r,n)}function kc(e,t,r,n,l){r=r.render;var o=t.ref;return yn(t,l),n=Vu(e,t,r,n,o,l),r=Hu(),e!==null&&!Ye?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,jt(e,t,l)):(de&&r&&bu(t),t.flags|=1,Ve(e,t,n,l),t.child)}function Sc(e,t,r,n,l){if(e===null){var o=r.type;return typeof o=="function"&&!es(o)&&o.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=o,Jf(e,t,o,n,l)):(e=xo(r.type,null,n,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,(e.lanes&l)===0){var i=o.memoizedProps;if(r=r.compare,r=r!==null?r:ml,r(i,n)&&e.ref===t.ref)return jt(e,t,l)}return t.flags|=1,e=dr(o,n),e.ref=t.ref,e.return=t,t.child=e}function Jf(e,t,r,n,l){if(e!==null){var o=e.memoizedProps;if(ml(o,n)&&e.ref===t.ref)if(Ye=!1,t.pendingProps=n=o,(e.lanes&l)!==0)(e.flags&131072)!==0&&(Ye=!0);else return t.lanes=e.lanes,jt(e,t,l)}return ja(e,t,r,n,l)}function Xf(e,t,r){var n=t.pendingProps,l=n.children,o=e!==null?e.memoizedState:null;if(n.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ue(cn,Ze),Ze|=r;else{if((r&1073741824)===0)return e=o!==null?o.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ue(cn,Ze),Ze|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},n=o!==null?o.baseLanes:r,ue(cn,Ze),Ze|=n}else o!==null?(n=o.baseLanes|r,t.memoizedState=null):n=r,ue(cn,Ze),Ze|=n;return Ve(e,t,l,r),t.child}function qf(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function ja(e,t,r,n,l){var o=Je(r)?Mr:ze.current;return o=kn(t,o),yn(t,l),r=Vu(e,t,r,n,o,l),n=Hu(),e!==null&&!Ye?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,jt(e,t,l)):(de&&n&&bu(t),t.flags|=1,Ve(e,t,r,l),t.child)}function Ec(e,t,r,n,l){if(Je(r)){var o=!0;Io(t)}else o=!1;if(yn(t,l),t.stateNode===null)yo(e,t),Kf(t,r,n),Oa(t,r,n,l),n=!0;else if(e===null){var i=t.stateNode,u=t.memoizedProps;i.props=u;var a=i.context,s=r.contextType;typeof s=="object"&&s!==null?s=pt(s):(s=Je(r)?Mr:ze.current,s=kn(t,s));var f=r.getDerivedStateFromProps,y=typeof f=="function"||typeof i.getSnapshotBeforeUpdate=="function";y||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(u!==n||a!==s)&&gc(t,i,n,s),qt=!1;var h=t.memoizedState;i.state=h,$o(t,n,i,l),a=t.memoizedState,u!==n||h!==a||Ge.current||qt?(typeof f=="function"&&(za(t,r,f,n),a=t.memoizedState),(u=qt||yc(t,r,u,n,h,a,s))?(y||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=a),i.props=n,i.state=a,i.context=s,n=u):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{i=t.stateNode,Df(e,t),u=t.memoizedProps,s=t.type===t.elementType?u:vt(t.type,u),i.props=s,y=t.pendingProps,h=i.context,a=r.contextType,typeof a=="object"&&a!==null?a=pt(a):(a=Je(r)?Mr:ze.current,a=kn(t,a));var w=r.getDerivedStateFromProps;(f=typeof w=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(u!==y||h!==a)&&gc(t,i,n,a),qt=!1,h=t.memoizedState,i.state=h,$o(t,n,i,l);var k=t.memoizedState;u!==y||h!==k||Ge.current||qt?(typeof w=="function"&&(za(t,r,w,n),k=t.memoizedState),(s=qt||yc(t,r,s,n,h,k,a)||!1)?(f||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(n,k,a),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(n,k,a)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||u===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=k),i.props=n,i.state=k,i.context=a,n=s):(typeof i.componentDidUpdate!="function"||u===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),n=!1)}return Ua(e,t,r,n,o,l)}function Ua(e,t,r,n,l,o){qf(e,t);var i=(t.flags&128)!==0;if(!n&&!i)return l&&uc(t,r,!1),jt(e,t,o);n=t.stateNode,gm.current=t;var u=i&&typeof r.getDerivedStateFromError!="function"?null:n.render();return t.flags|=1,e!==null&&i?(t.child=En(t,e.child,null,o),t.child=En(t,null,u,o)):Ve(e,t,u,o),t.memoizedState=n.state,l&&uc(t,r,!0),t.child}function Zf(e){var t=e.stateNode;t.pendingContext?ac(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ac(e,t.context,!1),Ou(e,t.containerInfo)}function Cc(e,t,r,n,l){return Sn(),_u(l),t.flags|=256,Ve(e,t,r,n),t.child}var Va={dehydrated:null,treeContext:null,retryLane:0};function Ha(e){return{baseLanes:e,cachePool:null,transitions:null}}function eh(e,t,r){var n=t.pendingProps,l=fe.current,o=!1,i=(t.flags&128)!==0,u;if((u=i)||(u=e!==null&&e.memoizedState===null?!1:(l&2)!==0),u?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),ue(fe,l&1),e===null)return Ma(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(i=n.children,e=n.fallback,o?(n=t.mode,o=t.child,i={mode:"hidden",children:i},(n&1)===0&&o!==null?(o.childLanes=0,o.pendingProps=i):o=mi(i,n,0,null),e=Ir(e,n,r,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Ha(r),t.memoizedState=Va,e):Ku(t,i));if(l=e.memoizedState,l!==null&&(u=l.dehydrated,u!==null))return vm(e,t,i,n,u,l,r);if(o){o=n.fallback,i=t.mode,l=e.child,u=l.sibling;var a={mode:"hidden",children:n.children};return(i&1)===0&&t.child!==l?(n=t.child,n.childLanes=0,n.pendingProps=a,t.deletions=null):(n=dr(l,a),n.subtreeFlags=l.subtreeFlags&14680064),u!==null?o=dr(u,o):(o=Ir(o,i,r,null),o.flags|=2),o.return=t,n.return=t,n.sibling=o,t.child=n,n=o,o=t.child,i=e.child.memoizedState,i=i===null?Ha(r):{baseLanes:i.baseLanes|r,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~r,t.memoizedState=Va,n}return o=e.child,e=o.sibling,n=dr(o,{mode:"visible",children:n.children}),(t.mode&1)===0&&(n.lanes=r),n.return=t,n.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n}function Ku(e,t){return t=mi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function eo(e,t,r,n){return n!==null&&_u(n),En(t,e.child,null,r),e=Ku(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function vm(e,t,r,n,l,o,i){if(r)return t.flags&256?(t.flags&=-257,n=qi(Error(_(422))),eo(e,t,i,n)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=n.fallback,l=t.mode,n=mi({mode:"visible",children:n.children},l,0,null),o=Ir(o,l,i,null),o.flags|=2,n.return=t,o.return=t,n.sibling=o,t.child=n,(t.mode&1)!==0&&En(t,e.child,null,i),t.child.memoizedState=Ha(i),t.memoizedState=Va,o);if((t.mode&1)===0)return eo(e,t,i,null);if(l.data==="$!"){if(n=l.nextSibling&&l.nextSibling.dataset,n)var u=n.dgst;return n=u,o=Error(_(419)),n=qi(o,n,void 0),eo(e,t,i,n)}if(u=(i&e.childLanes)!==0,Ye||u){if(n=Re,n!==null){switch(i&-i){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=(l&(n.suspendedLanes|i))!==0?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,$t(e,l),St(n,e,l,-1))}return Zu(),n=qi(Error(_(421))),eo(e,t,i,n)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=bm.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,rt=ar(l.nextSibling),nt=t,de=!0,wt=null,e!==null&&(ct[dt++]=It,ct[dt++]=Mt,ct[dt++]=Tr,It=e.id,Mt=e.overflow,Tr=t),t=Ku(t,n.children),t.flags|=4096,t)}function Nc(e,t,r){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),Ta(e.return,t,r)}function Zi(e,t,r,n,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=n,o.tail=r,o.tailMode=l)}function th(e,t,r){var n=t.pendingProps,l=n.revealOrder,o=n.tail;if(Ve(e,t,n.children,r),n=fe.current,(n&2)!==0)n=n&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Nc(e,r,t);else if(e.tag===19)Nc(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}if(ue(fe,n),(t.mode&1)===0)t.memoizedState=null;else switch(l){case"forwards":for(r=t.child,l=null;r!==null;)e=r.alternate,e!==null&&jo(e)===null&&(l=r),r=r.sibling;r=l,r===null?(l=t.child,t.child=null):(l=r.sibling,r.sibling=null),Zi(t,!1,l,r,o);break;case"backwards":for(r=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&jo(e)===null){t.child=l;break}e=l.sibling,l.sibling=r,r=l,l=e}Zi(t,!0,r,null,o);break;case"together":Zi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function yo(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function jt(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),Or|=t.lanes,(r&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(_(153));if(t.child!==null){for(e=t.child,r=dr(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=dr(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function xm(e,t,r){switch(t.tag){case 3:Zf(t),Sn();break;case 5:Ff(t);break;case 1:Je(t.type)&&Io(t);break;case 4:Ou(t,t.stateNode.containerInfo);break;case 10:var n=t.type._context,l=t.memoizedProps.value;ue(zo,n._currentValue),n._currentValue=l;break;case 13:if(n=t.memoizedState,n!==null)return n.dehydrated!==null?(ue(fe,fe.current&1),t.flags|=128,null):(r&t.child.childLanes)!==0?eh(e,t,r):(ue(fe,fe.current&1),e=jt(e,t,r),e!==null?e.sibling:null);ue(fe,fe.current&1);break;case 19:if(n=(r&t.childLanes)!==0,(e.flags&128)!==0){if(n)return th(e,t,r);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),ue(fe,fe.current),n)break;return null;case 22:case 23:return t.lanes=0,Xf(e,t,r)}return jt(e,t,r)}var rh,Wa,nh,lh;rh=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};Wa=function(){};nh=function(e,t,r,n){var l=e.memoizedProps;if(l!==n){e=t.stateNode,Pr(At.current);var o=null;switch(r){case"input":l=ha(e,l),n=ha(e,n),o=[];break;case"select":l=pe({},l,{value:void 0}),n=pe({},n,{value:void 0}),o=[];break;case"textarea":l=ya(e,l),n=ya(e,n),o=[];break;default:typeof l.onClick!="function"&&typeof n.onClick=="function"&&(e.onclick=_o)}va(r,n);var i;r=null;for(s in l)if(!n.hasOwnProperty(s)&&l.hasOwnProperty(s)&&l[s]!=null)if(s==="style"){var u=l[s];for(i in u)u.hasOwnProperty(i)&&(r||(r={}),r[i]="")}else s!=="dangerouslySetInnerHTML"&&s!=="children"&&s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(ul.hasOwnProperty(s)?o||(o=[]):(o=o||[]).push(s,null));for(s in n){var a=n[s];if(u=l!=null?l[s]:void 0,n.hasOwnProperty(s)&&a!==u&&(a!=null||u!=null))if(s==="style")if(u){for(i in u)!u.hasOwnProperty(i)||a&&a.hasOwnProperty(i)||(r||(r={}),r[i]="");for(i in a)a.hasOwnProperty(i)&&u[i]!==a[i]&&(r||(r={}),r[i]=a[i])}else r||(o||(o=[]),o.push(s,r)),r=a;else s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,u=u?u.__html:void 0,a!=null&&u!==a&&(o=o||[]).push(s,a)):s==="children"?typeof a!="string"&&typeof a!="number"||(o=o||[]).push(s,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&(ul.hasOwnProperty(s)?(a!=null&&s==="onScroll"&&se("scroll",e),o||u===a||(o=[])):(o=o||[]).push(s,a))}r&&(o=o||[]).push("style",r);var s=o;(t.updateQueue=s)&&(t.flags|=4)}};lh=function(e,t,r,n){r!==n&&(t.flags|=4)};function Un(e,t){if(!de)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;r!==null;)r.alternate!==null&&(n=r),r=r.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function Me(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,n=0;if(t)for(var l=e.child;l!==null;)r|=l.lanes|l.childLanes,n|=l.subtreeFlags&14680064,n|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)r|=l.lanes|l.childLanes,n|=l.subtreeFlags,n|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=n,e.childLanes=r,t}function wm(e,t,r){var n=t.pendingProps;switch(Pu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Me(t),null;case 1:return Je(t.type)&&Lo(),Me(t),null;case 3:return n=t.stateNode,Cn(),ce(Ge),ce(ze),ju(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(ql(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,wt!==null&&(Za(wt),wt=null))),Wa(e,t),Me(t),null;case 5:$u(t);var l=Pr(wl.current);if(r=t.type,e!==null&&t.stateNode!=null)nh(e,t,r,n,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!n){if(t.stateNode===null)throw Error(_(166));return Me(t),null}if(e=Pr(At.current),ql(t)){n=t.stateNode,r=t.type;var o=t.memoizedProps;switch(n[Bt]=t,n[vl]=o,e=(t.mode&1)!==0,r){case"dialog":se("cancel",n),se("close",n);break;case"iframe":case"object":case"embed":se("load",n);break;case"video":case"audio":for(l=0;l<Jn.length;l++)se(Jn[l],n);break;case"source":se("error",n);break;case"img":case"image":case"link":se("error",n),se("load",n);break;case"details":se("toggle",n);break;case"input":Ls(n,o),se("invalid",n);break;case"select":n._wrapperState={wasMultiple:!!o.multiple},se("invalid",n);break;case"textarea":Ms(n,o),se("invalid",n)}va(r,o),l=null;for(var i in o)if(o.hasOwnProperty(i)){var u=o[i];i==="children"?typeof u=="string"?n.textContent!==u&&(o.suppressHydrationWarning!==!0&&Xl(n.textContent,u,e),l=["children",u]):typeof u=="number"&&n.textContent!==""+u&&(o.suppressHydrationWarning!==!0&&Xl(n.textContent,u,e),l=["children",""+u]):ul.hasOwnProperty(i)&&u!=null&&i==="onScroll"&&se("scroll",n)}switch(r){case"input":Vl(n),Is(n,o,!0);break;case"textarea":Vl(n),Ts(n);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(n.onclick=_o)}n=l,t.updateQueue=n,n!==null&&(t.flags|=4)}else{i=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=bd(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof n.is=="string"?e=i.createElement(r,{is:n.is}):(e=i.createElement(r),r==="select"&&(i=e,n.multiple?i.multiple=!0:n.size&&(i.size=n.size))):e=i.createElementNS(e,r),e[Bt]=t,e[vl]=n,rh(e,t,!1,!1),t.stateNode=e;e:{switch(i=xa(r,n),r){case"dialog":se("cancel",e),se("close",e),l=n;break;case"iframe":case"object":case"embed":se("load",e),l=n;break;case"video":case"audio":for(l=0;l<Jn.length;l++)se(Jn[l],e);l=n;break;case"source":se("error",e),l=n;break;case"img":case"image":case"link":se("error",e),se("load",e),l=n;break;case"details":se("toggle",e),l=n;break;case"input":Ls(e,n),l=ha(e,n),se("invalid",e);break;case"option":l=n;break;case"select":e._wrapperState={wasMultiple:!!n.multiple},l=pe({},n,{value:void 0}),se("invalid",e);break;case"textarea":Ms(e,n),l=ya(e,n),se("invalid",e);break;default:l=n}va(r,l),u=l;for(o in u)if(u.hasOwnProperty(o)){var a=u[o];o==="style"?Ld(e,a):o==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Pd(e,a)):o==="children"?typeof a=="string"?(r!=="textarea"||a!=="")&&sl(e,a):typeof a=="number"&&sl(e,""+a):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(ul.hasOwnProperty(o)?a!=null&&o==="onScroll"&&se("scroll",e):a!=null&&gu(e,o,a,i))}switch(r){case"input":Vl(e),Is(e,n,!1);break;case"textarea":Vl(e),Ts(e);break;case"option":n.value!=null&&e.setAttribute("value",""+fr(n.value));break;case"select":e.multiple=!!n.multiple,o=n.value,o!=null?fn(e,!!n.multiple,o,!1):n.defaultValue!=null&&fn(e,!!n.multiple,n.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=_o)}switch(r){case"button":case"input":case"select":case"textarea":n=!!n.autoFocus;break e;case"img":n=!0;break e;default:n=!1}}n&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Me(t),null;case 6:if(e&&t.stateNode!=null)lh(e,t,e.memoizedProps,n);else{if(typeof n!="string"&&t.stateNode===null)throw Error(_(166));if(r=Pr(wl.current),Pr(At.current),ql(t)){if(n=t.stateNode,r=t.memoizedProps,n[Bt]=t,(o=n.nodeValue!==r)&&(e=nt,e!==null))switch(e.tag){case 3:Xl(n.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Xl(n.nodeValue,r,(e.mode&1)!==0)}o&&(t.flags|=4)}else n=(r.nodeType===9?r:r.ownerDocument).createTextNode(n),n[Bt]=t,t.stateNode=n}return Me(t),null;case 13:if(ce(fe),n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(de&&rt!==null&&(t.mode&1)!==0&&(t.flags&128)===0)Sf(),Sn(),t.flags|=98560,o=!1;else if(o=ql(t),n!==null&&n.dehydrated!==null){if(e===null){if(!o)throw Error(_(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(_(317));o[Bt]=t}else Sn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Me(t),o=!1}else wt!==null&&(Za(wt),wt=null),o=!0;if(!o)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=r,t):(n=n!==null,n!==(e!==null&&e.memoizedState!==null)&&n&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(fe.current&1)!==0?Ne===0&&(Ne=3):Zu())),t.updateQueue!==null&&(t.flags|=4),Me(t),null);case 4:return Cn(),Wa(e,t),e===null&&yl(t.stateNode.containerInfo),Me(t),null;case 10:return Mu(t.type._context),Me(t),null;case 17:return Je(t.type)&&Lo(),Me(t),null;case 19:if(ce(fe),o=t.memoizedState,o===null)return Me(t),null;if(n=(t.flags&128)!==0,i=o.rendering,i===null)if(n)Un(o,!1);else{if(Ne!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(i=jo(e),i!==null){for(t.flags|=128,Un(o,!1),n=i.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),t.subtreeFlags=0,n=r,r=t.child;r!==null;)o=r,e=n,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return ue(fe,fe.current&1|2),t.child}e=e.sibling}o.tail!==null&&ge()>Dn&&(t.flags|=128,n=!0,Un(o,!1),t.lanes=4194304)}else{if(!n)if(e=jo(i),e!==null){if(t.flags|=128,n=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),Un(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!de)return Me(t),null}else 2*ge()-o.renderingStartTime>Dn&&r!==1073741824&&(t.flags|=128,n=!0,Un(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(r=o.last,r!==null?r.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=ge(),t.sibling=null,r=fe.current,ue(fe,n?r&1|2:r&1),t):(Me(t),null);case 22:case 23:return qu(),n=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==n&&(t.flags|=8192),n&&(t.mode&1)!==0?(Ze&1073741824)!==0&&(Me(t),t.subtreeFlags&6&&(t.flags|=8192)):Me(t),null;case 24:return null;case 25:return null}throw Error(_(156,t.tag))}function km(e,t){switch(Pu(t),t.tag){case 1:return Je(t.type)&&Lo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Cn(),ce(Ge),ce(ze),ju(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return $u(t),null;case 13:if(ce(fe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(_(340));Sn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ce(fe),null;case 4:return Cn(),null;case 10:return Mu(t.type._context),null;case 22:case 23:return qu(),null;case 24:return null;default:return null}}var to=!1,Te=!1,Sm=typeof WeakSet=="function"?WeakSet:Set,T=null;function sn(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(n){me(e,t,n)}else r.current=null}function Qa(e,t,r){try{r()}catch(n){me(e,t,n)}}var Dc=!1;function Em(e,t){if(Ra=Ao,e=sf(),Au(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var n=r.getSelection&&r.getSelection();if(n&&n.rangeCount!==0){r=n.anchorNode;var l=n.anchorOffset,o=n.focusNode;n=n.focusOffset;try{r.nodeType,o.nodeType}catch{r=null;break e}var i=0,u=-1,a=-1,s=0,f=0,y=e,h=null;t:for(;;){for(var w;y!==r||l!==0&&y.nodeType!==3||(u=i+l),y!==o||n!==0&&y.nodeType!==3||(a=i+n),y.nodeType===3&&(i+=y.nodeValue.length),(w=y.firstChild)!==null;)h=y,y=w;for(;;){if(y===e)break t;if(h===r&&++s===l&&(u=i),h===o&&++f===n&&(a=i),(w=y.nextSibling)!==null)break;y=h,h=y.parentNode}y=w}r=u===-1||a===-1?null:{start:u,end:a}}else r=null}r=r||{start:0,end:0}}else r=null;for(Aa={focusedElem:e,selectionRange:r},Ao=!1,T=t;T!==null;)if(t=T,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,T=e;else for(;T!==null;){t=T;try{var k=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(k!==null){var C=k.memoizedProps,A=k.memoizedState,m=t.stateNode,p=m.getSnapshotBeforeUpdate(t.elementType===t.type?C:vt(t.type,C),A);m.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var c=t.stateNode.containerInfo;c.nodeType===1?c.textContent="":c.nodeType===9&&c.documentElement&&c.removeChild(c.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(_(163))}}catch(g){me(t,t.return,g)}if(e=t.sibling,e!==null){e.return=t.return,T=e;break}T=t.return}return k=Dc,Dc=!1,k}function ol(e,t,r){var n=t.updateQueue;if(n=n!==null?n.lastEffect:null,n!==null){var l=n=n.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&Qa(t,r,o)}l=l.next}while(l!==n)}}function hi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var n=r.create;r.destroy=n()}r=r.next}while(r!==t)}}function Ka(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function oh(e){var t=e.alternate;t!==null&&(e.alternate=null,oh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Bt],delete t[vl],delete t[_a],delete t[om],delete t[im])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ih(e){return e.tag===5||e.tag===3||e.tag===4}function Fc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ih(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ya(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=_o));else if(n!==4&&(e=e.child,e!==null))for(Ya(e,t,r),e=e.sibling;e!==null;)Ya(e,t,r),e=e.sibling}function Ga(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(n!==4&&(e=e.child,e!==null))for(Ga(e,t,r),e=e.sibling;e!==null;)Ga(e,t,r),e=e.sibling}var Ae=null,xt=!1;function Gt(e,t,r){for(r=r.child;r!==null;)ah(e,t,r),r=r.sibling}function ah(e,t,r){if(Rt&&typeof Rt.onCommitFiberUnmount=="function")try{Rt.onCommitFiberUnmount(oi,r)}catch{}switch(r.tag){case 5:Te||sn(r,t);case 6:var n=Ae,l=xt;Ae=null,Gt(e,t,r),Ae=n,xt=l,Ae!==null&&(xt?(e=Ae,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):Ae.removeChild(r.stateNode));break;case 18:Ae!==null&&(xt?(e=Ae,r=r.stateNode,e.nodeType===8?Qi(e.parentNode,r):e.nodeType===1&&Qi(e,r),hl(e)):Qi(Ae,r.stateNode));break;case 4:n=Ae,l=xt,Ae=r.stateNode.containerInfo,xt=!0,Gt(e,t,r),Ae=n,xt=l;break;case 0:case 11:case 14:case 15:if(!Te&&(n=r.updateQueue,n!==null&&(n=n.lastEffect,n!==null))){l=n=n.next;do{var o=l,i=o.destroy;o=o.tag,i!==void 0&&((o&2)!==0||(o&4)!==0)&&Qa(r,t,i),l=l.next}while(l!==n)}Gt(e,t,r);break;case 1:if(!Te&&(sn(r,t),n=r.stateNode,typeof n.componentWillUnmount=="function"))try{n.props=r.memoizedProps,n.state=r.memoizedState,n.componentWillUnmount()}catch(u){me(r,t,u)}Gt(e,t,r);break;case 21:Gt(e,t,r);break;case 22:r.mode&1?(Te=(n=Te)||r.memoizedState!==null,Gt(e,t,r),Te=n):Gt(e,t,r);break;default:Gt(e,t,r)}}function Bc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new Sm),t.forEach(function(n){var l=Pm.bind(null,e,n);r.has(n)||(r.add(n),n.then(l,l))})}}function gt(e,t){var r=t.deletions;if(r!==null)for(var n=0;n<r.length;n++){var l=r[n];try{var o=e,i=t,u=i;e:for(;u!==null;){switch(u.tag){case 5:Ae=u.stateNode,xt=!1;break e;case 3:Ae=u.stateNode.containerInfo,xt=!0;break e;case 4:Ae=u.stateNode.containerInfo,xt=!0;break e}u=u.return}if(Ae===null)throw Error(_(160));ah(o,i,l),Ae=null,xt=!1;var a=l.alternate;a!==null&&(a.return=null),l.return=null}catch(s){me(l,t,s)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)uh(t,e),t=t.sibling}function uh(e,t){var r=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(gt(t,e),Dt(e),n&4){try{ol(3,e,e.return),hi(3,e)}catch(C){me(e,e.return,C)}try{ol(5,e,e.return)}catch(C){me(e,e.return,C)}}break;case 1:gt(t,e),Dt(e),n&512&&r!==null&&sn(r,r.return);break;case 5:if(gt(t,e),Dt(e),n&512&&r!==null&&sn(r,r.return),e.flags&32){var l=e.stateNode;try{sl(l,"")}catch(C){me(e,e.return,C)}}if(n&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,i=r!==null?r.memoizedProps:o,u=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{u==="input"&&o.type==="radio"&&o.name!=null&&Rd(l,o),xa(u,i);var s=xa(u,o);for(i=0;i<a.length;i+=2){var f=a[i],y=a[i+1];f==="style"?Ld(l,y):f==="dangerouslySetInnerHTML"?Pd(l,y):f==="children"?sl(l,y):gu(l,f,y,s)}switch(u){case"input":pa(l,o);break;case"textarea":Ad(l,o);break;case"select":var h=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var w=o.value;w!=null?fn(l,!!o.multiple,w,!1):h!==!!o.multiple&&(o.defaultValue!=null?fn(l,!!o.multiple,o.defaultValue,!0):fn(l,!!o.multiple,o.multiple?[]:"",!1))}l[vl]=o}catch(C){me(e,e.return,C)}}break;case 6:if(gt(t,e),Dt(e),n&4){if(e.stateNode===null)throw Error(_(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(C){me(e,e.return,C)}}break;case 3:if(gt(t,e),Dt(e),n&4&&r!==null&&r.memoizedState.isDehydrated)try{hl(t.containerInfo)}catch(C){me(e,e.return,C)}break;case 4:gt(t,e),Dt(e);break;case 13:gt(t,e),Dt(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(Ju=ge())),n&4&&Bc(e);break;case 22:if(f=r!==null&&r.memoizedState!==null,e.mode&1?(Te=(s=Te)||f,gt(t,e),Te=s):gt(t,e),Dt(e),n&8192){if(s=e.memoizedState!==null,(e.stateNode.isHidden=s)&&!f&&(e.mode&1)!==0)for(T=e,f=e.child;f!==null;){for(y=T=f;T!==null;){switch(h=T,w=h.child,h.tag){case 0:case 11:case 14:case 15:ol(4,h,h.return);break;case 1:sn(h,h.return);var k=h.stateNode;if(typeof k.componentWillUnmount=="function"){n=h,r=h.return;try{t=n,k.props=t.memoizedProps,k.state=t.memoizedState,k.componentWillUnmount()}catch(C){me(n,r,C)}}break;case 5:sn(h,h.return);break;case 22:if(h.memoizedState!==null){Ac(y);continue}}w!==null?(w.return=h,T=w):Ac(y)}f=f.sibling}e:for(f=null,y=e;;){if(y.tag===5){if(f===null){f=y;try{l=y.stateNode,s?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(u=y.stateNode,a=y.memoizedProps.style,i=a!=null&&a.hasOwnProperty("display")?a.display:null,u.style.display=_d("display",i))}catch(C){me(e,e.return,C)}}}else if(y.tag===6){if(f===null)try{y.stateNode.nodeValue=s?"":y.memoizedProps}catch(C){me(e,e.return,C)}}else if((y.tag!==22&&y.tag!==23||y.memoizedState===null||y===e)&&y.child!==null){y.child.return=y,y=y.child;continue}if(y===e)break e;for(;y.sibling===null;){if(y.return===null||y.return===e)break e;f===y&&(f=null),y=y.return}f===y&&(f=null),y.sibling.return=y.return,y=y.sibling}}break;case 19:gt(t,e),Dt(e),n&4&&Bc(e);break;case 21:break;default:gt(t,e),Dt(e)}}function Dt(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(ih(r)){var n=r;break e}r=r.return}throw Error(_(160))}switch(n.tag){case 5:var l=n.stateNode;n.flags&32&&(sl(l,""),n.flags&=-33);var o=Fc(e);Ga(e,o,l);break;case 3:case 4:var i=n.stateNode.containerInfo,u=Fc(e);Ya(e,u,i);break;default:throw Error(_(161))}}catch(a){me(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Cm(e,t,r){T=e,sh(e)}function sh(e,t,r){for(var n=(e.mode&1)!==0;T!==null;){var l=T,o=l.child;if(l.tag===22&&n){var i=l.memoizedState!==null||to;if(!i){var u=l.alternate,a=u!==null&&u.memoizedState!==null||Te;u=to;var s=Te;if(to=i,(Te=a)&&!s)for(T=l;T!==null;)i=T,a=i.child,i.tag===22&&i.memoizedState!==null?bc(l):a!==null?(a.return=i,T=a):bc(l);for(;o!==null;)T=o,sh(o),o=o.sibling;T=l,to=u,Te=s}Rc(e)}else(l.subtreeFlags&8772)!==0&&o!==null?(o.return=l,T=o):Rc(e)}}function Rc(e){for(;T!==null;){var t=T;if((t.flags&8772)!==0){var r=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:Te||hi(5,t);break;case 1:var n=t.stateNode;if(t.flags&4&&!Te)if(r===null)n.componentDidMount();else{var l=t.elementType===t.type?r.memoizedProps:vt(t.type,r.memoizedProps);n.componentDidUpdate(l,r.memoizedState,n.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&hc(t,o,n);break;case 3:var i=t.updateQueue;if(i!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}hc(t,i,r)}break;case 5:var u=t.stateNode;if(r===null&&t.flags&4){r=u;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&r.focus();break;case"img":a.src&&(r.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var s=t.alternate;if(s!==null){var f=s.memoizedState;if(f!==null){var y=f.dehydrated;y!==null&&hl(y)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(_(163))}Te||t.flags&512&&Ka(t)}catch(h){me(t,t.return,h)}}if(t===e){T=null;break}if(r=t.sibling,r!==null){r.return=t.return,T=r;break}T=t.return}}function Ac(e){for(;T!==null;){var t=T;if(t===e){T=null;break}var r=t.sibling;if(r!==null){r.return=t.return,T=r;break}T=t.return}}function bc(e){for(;T!==null;){var t=T;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{hi(4,t)}catch(a){me(t,r,a)}break;case 1:var n=t.stateNode;if(typeof n.componentDidMount=="function"){var l=t.return;try{n.componentDidMount()}catch(a){me(t,l,a)}}var o=t.return;try{Ka(t)}catch(a){me(t,o,a)}break;case 5:var i=t.return;try{Ka(t)}catch(a){me(t,i,a)}}}catch(a){me(t,t.return,a)}if(t===e){T=null;break}var u=t.sibling;if(u!==null){u.return=t.return,T=u;break}T=t.return}}var Nm=Math.ceil,Ho=Ut.ReactCurrentDispatcher,Yu=Ut.ReactCurrentOwner,ht=Ut.ReactCurrentBatchConfig,te=0,Re=null,ke=null,be=0,Ze=0,cn=yr(0),Ne=0,Cl=null,Or=0,pi=0,Gu=0,il=null,Ke=null,Ju=0,Dn=1/0,_t=null,Wo=!1,Ja=null,sr=null,ro=!1,nr=null,Qo=0,al=0,Xa=null,go=-1,vo=0;function He(){return(te&6)!==0?ge():go!==-1?go:go=ge()}function cr(e){return(e.mode&1)===0?1:(te&2)!==0&&be!==0?be&-be:um.transition!==null?(vo===0&&(vo=Qd()),vo):(e=oe,e!==0||(e=window.event,e=e===void 0?16:Zd(e.type)),e)}function St(e,t,r,n){if(50<al)throw al=0,Xa=null,Error(_(185));Rl(e,r,n),((te&2)===0||e!==Re)&&(e===Re&&((te&2)===0&&(pi|=r),Ne===4&&er(e,be)),Xe(e,n),r===1&&te===0&&(t.mode&1)===0&&(Dn=ge()+500,ci&&gr()))}function Xe(e,t){var r=e.callbackNode;up(e,t);var n=Ro(e,e===Re?be:0);if(n===0)r!==null&&$s(r),e.callbackNode=null,e.callbackPriority=0;else if(t=n&-n,e.callbackPriority!==t){if(r!=null&&$s(r),t===1)e.tag===0?am(Pc.bind(null,e)):xf(Pc.bind(null,e)),nm(function(){(te&6)===0&&gr()}),r=null;else{switch(Kd(n)){case 1:r=Su;break;case 4:r=Hd;break;case 16:r=Bo;break;case 536870912:r=Wd;break;default:r=Bo}r=gh(r,ch.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function ch(e,t){if(go=-1,vo=0,(te&6)!==0)throw Error(_(327));var r=e.callbackNode;if(gn()&&e.callbackNode!==r)return null;var n=Ro(e,e===Re?be:0);if(n===0)return null;if((n&30)!==0||(n&e.expiredLanes)!==0||t)t=Ko(e,n);else{t=n;var l=te;te|=2;var o=fh();(Re!==e||be!==t)&&(_t=null,Dn=ge()+500,Lr(e,t));do try{Bm();break}catch(u){dh(e,u)}while(1);Iu(),Ho.current=o,te=l,ke!==null?t=0:(Re=null,be=0,t=Ne)}if(t!==0){if(t===2&&(l=Ca(e),l!==0&&(n=l,t=qa(e,l))),t===1)throw r=Cl,Lr(e,0),er(e,n),Xe(e,ge()),r;if(t===6)er(e,n);else{if(l=e.current.alternate,(n&30)===0&&!Dm(l)&&(t=Ko(e,n),t===2&&(o=Ca(e),o!==0&&(n=o,t=qa(e,o))),t===1))throw r=Cl,Lr(e,0),er(e,n),Xe(e,ge()),r;switch(e.finishedWork=l,e.finishedLanes=n,t){case 0:case 1:throw Error(_(345));case 2:Br(e,Ke,_t);break;case 3:if(er(e,n),(n&130023424)===n&&(t=Ju+500-ge(),10<t)){if(Ro(e,0)!==0)break;if(l=e.suspendedLanes,(l&n)!==n){He(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Pa(Br.bind(null,e,Ke,_t),t);break}Br(e,Ke,_t);break;case 4:if(er(e,n),(n&4194240)===n)break;for(t=e.eventTimes,l=-1;0<n;){var i=31-kt(n);o=1<<i,i=t[i],i>l&&(l=i),n&=~o}if(n=l,n=ge()-n,n=(120>n?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*Nm(n/1960))-n,10<n){e.timeoutHandle=Pa(Br.bind(null,e,Ke,_t),n);break}Br(e,Ke,_t);break;case 5:Br(e,Ke,_t);break;default:throw Error(_(329))}}}return Xe(e,ge()),e.callbackNode===r?ch.bind(null,e):null}function qa(e,t){var r=il;return e.current.memoizedState.isDehydrated&&(Lr(e,t).flags|=256),e=Ko(e,t),e!==2&&(t=Ke,Ke=r,t!==null&&Za(t)),e}function Za(e){Ke===null?Ke=e:Ke.push.apply(Ke,e)}function Dm(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var n=0;n<r.length;n++){var l=r[n],o=l.getSnapshot;l=l.value;try{if(!Et(o(),l))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function er(e,t){for(t&=~Gu,t&=~pi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-kt(t),n=1<<r;e[r]=-1,t&=~n}}function Pc(e){if((te&6)!==0)throw Error(_(327));gn();var t=Ro(e,0);if((t&1)===0)return Xe(e,ge()),null;var r=Ko(e,t);if(e.tag!==0&&r===2){var n=Ca(e);n!==0&&(t=n,r=qa(e,n))}if(r===1)throw r=Cl,Lr(e,0),er(e,t),Xe(e,ge()),r;if(r===6)throw Error(_(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Br(e,Ke,_t),Xe(e,ge()),null}function Xu(e,t){var r=te;te|=1;try{return e(t)}finally{te=r,te===0&&(Dn=ge()+500,ci&&gr())}}function $r(e){nr!==null&&nr.tag===0&&(te&6)===0&&gn();var t=te;te|=1;var r=ht.transition,n=oe;try{if(ht.transition=null,oe=1,e)return e()}finally{oe=n,ht.transition=r,te=t,(te&6)===0&&gr()}}function qu(){Ze=cn.current,ce(cn)}function Lr(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,rm(r)),ke!==null)for(r=ke.return;r!==null;){var n=r;switch(Pu(n),n.tag){case 1:n=n.type.childContextTypes,n!=null&&Lo();break;case 3:Cn(),ce(Ge),ce(ze),ju();break;case 5:$u(n);break;case 4:Cn();break;case 13:ce(fe);break;case 19:ce(fe);break;case 10:Mu(n.type._context);break;case 22:case 23:qu()}r=r.return}if(Re=e,ke=e=dr(e.current,null),be=Ze=t,Ne=0,Cl=null,Gu=pi=Or=0,Ke=il=null,br!==null){for(t=0;t<br.length;t++)if(r=br[t],n=r.interleaved,n!==null){r.interleaved=null;var l=n.next,o=r.pending;if(o!==null){var i=o.next;o.next=l,n.next=i}r.pending=n}br=null}return e}function dh(e,t){do{var r=ke;try{if(Iu(),po.current=Vo,Uo){for(var n=he.memoizedState;n!==null;){var l=n.queue;l!==null&&(l.pending=null),n=n.next}Uo=!1}if(zr=0,Be=Ce=he=null,ll=!1,kl=0,Yu.current=null,r===null||r.return===null){Ne=1,Cl=t,ke=null;break}e:{var o=e,i=r.return,u=r,a=t;if(t=be,u.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var s=a,f=u,y=f.tag;if((f.mode&1)===0&&(y===0||y===11||y===15)){var h=f.alternate;h?(f.updateQueue=h.updateQueue,f.memoizedState=h.memoizedState,f.lanes=h.lanes):(f.updateQueue=null,f.memoizedState=null)}var w=xc(i);if(w!==null){w.flags&=-257,wc(w,i,u,o,t),w.mode&1&&vc(o,s,t),t=w,a=s;var k=t.updateQueue;if(k===null){var C=new Set;C.add(a),t.updateQueue=C}else k.add(a);break e}else{if((t&1)===0){vc(o,s,t),Zu();break e}a=Error(_(426))}}else if(de&&u.mode&1){var A=xc(i);if(A!==null){(A.flags&65536)===0&&(A.flags|=256),wc(A,i,u,o,t),_u(Nn(a,u));break e}}o=a=Nn(a,u),Ne!==4&&(Ne=2),il===null?il=[o]:il.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=Yf(o,a,t);fc(o,m);break e;case 1:u=a;var p=o.type,c=o.stateNode;if((o.flags&128)===0&&(typeof p.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(sr===null||!sr.has(c)))){o.flags|=65536,t&=-t,o.lanes|=t;var g=Gf(o,u,t);fc(o,g);break e}}o=o.return}while(o!==null)}ph(r)}catch(B){t=B,ke===r&&r!==null&&(ke=r=r.return);continue}break}while(1)}function fh(){var e=Ho.current;return Ho.current=Vo,e===null?Vo:e}function Zu(){(Ne===0||Ne===3||Ne===2)&&(Ne=4),Re===null||(Or&268435455)===0&&(pi&268435455)===0||er(Re,be)}function Ko(e,t){var r=te;te|=2;var n=fh();(Re!==e||be!==t)&&(_t=null,Lr(e,t));do try{Fm();break}catch(l){dh(e,l)}while(1);if(Iu(),te=r,Ho.current=n,ke!==null)throw Error(_(261));return Re=null,be=0,Ne}function Fm(){for(;ke!==null;)hh(ke)}function Bm(){for(;ke!==null&&!Z0();)hh(ke)}function hh(e){var t=yh(e.alternate,e,Ze);e.memoizedProps=e.pendingProps,t===null?ph(e):ke=t,Yu.current=null}function ph(e){var t=e;do{var r=t.alternate;if(e=t.return,(t.flags&32768)===0){if(r=wm(r,t,Ze),r!==null){ke=r;return}}else{if(r=km(r,t),r!==null){r.flags&=32767,ke=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ne=6,ke=null;return}}if(t=t.sibling,t!==null){ke=t;return}ke=t=e}while(t!==null);Ne===0&&(Ne=5)}function Br(e,t,r){var n=oe,l=ht.transition;try{ht.transition=null,oe=1,Rm(e,t,r,n)}finally{ht.transition=l,oe=n}return null}function Rm(e,t,r,n){do gn();while(nr!==null);if((te&6)!==0)throw Error(_(327));r=e.finishedWork;var l=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(_(177));e.callbackNode=null,e.callbackPriority=0;var o=r.lanes|r.childLanes;if(sp(e,o),e===Re&&(ke=Re=null,be=0),(r.subtreeFlags&2064)===0&&(r.flags&2064)===0||ro||(ro=!0,gh(Bo,function(){return gn(),null})),o=(r.flags&15990)!==0,(r.subtreeFlags&15990)!==0||o){o=ht.transition,ht.transition=null;var i=oe;oe=1;var u=te;te|=4,Yu.current=null,Em(e,r),uh(r,e),Gp(Aa),Ao=!!Ra,Aa=Ra=null,e.current=r,Cm(r),ep(),te=u,oe=i,ht.transition=o}else e.current=r;if(ro&&(ro=!1,nr=e,Qo=l),o=e.pendingLanes,o===0&&(sr=null),np(r.stateNode),Xe(e,ge()),t!==null)for(n=e.onRecoverableError,r=0;r<t.length;r++)l=t[r],n(l.value,{componentStack:l.stack,digest:l.digest});if(Wo)throw Wo=!1,e=Ja,Ja=null,e;return(Qo&1)!==0&&e.tag!==0&&gn(),o=e.pendingLanes,(o&1)!==0?e===Xa?al++:(al=0,Xa=e):al=0,gr(),null}function gn(){if(nr!==null){var e=Kd(Qo),t=ht.transition,r=oe;try{if(ht.transition=null,oe=16>e?16:e,nr===null)var n=!1;else{if(e=nr,nr=null,Qo=0,(te&6)!==0)throw Error(_(331));var l=te;for(te|=4,T=e.current;T!==null;){var o=T,i=o.child;if((T.flags&16)!==0){var u=o.deletions;if(u!==null){for(var a=0;a<u.length;a++){var s=u[a];for(T=s;T!==null;){var f=T;switch(f.tag){case 0:case 11:case 15:ol(8,f,o)}var y=f.child;if(y!==null)y.return=f,T=y;else for(;T!==null;){f=T;var h=f.sibling,w=f.return;if(oh(f),f===s){T=null;break}if(h!==null){h.return=w,T=h;break}T=w}}}var k=o.alternate;if(k!==null){var C=k.child;if(C!==null){k.child=null;do{var A=C.sibling;C.sibling=null,C=A}while(C!==null)}}T=o}}if((o.subtreeFlags&2064)!==0&&i!==null)i.return=o,T=i;else e:for(;T!==null;){if(o=T,(o.flags&2048)!==0)switch(o.tag){case 0:case 11:case 15:ol(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,T=m;break e}T=o.return}}var p=e.current;for(T=p;T!==null;){i=T;var c=i.child;if((i.subtreeFlags&2064)!==0&&c!==null)c.return=i,T=c;else e:for(i=p;T!==null;){if(u=T,(u.flags&2048)!==0)try{switch(u.tag){case 0:case 11:case 15:hi(9,u)}}catch(B){me(u,u.return,B)}if(u===i){T=null;break e}var g=u.sibling;if(g!==null){g.return=u.return,T=g;break e}T=u.return}}if(te=l,gr(),Rt&&typeof Rt.onPostCommitFiberRoot=="function")try{Rt.onPostCommitFiberRoot(oi,e)}catch{}n=!0}return n}finally{oe=r,ht.transition=t}}return!1}function _c(e,t,r){t=Nn(r,t),t=Yf(e,t,1),e=ur(e,t,1),t=He(),e!==null&&(Rl(e,1,t),Xe(e,t))}function me(e,t,r){if(e.tag===3)_c(e,e,r);else for(;t!==null;){if(t.tag===3){_c(t,e,r);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(sr===null||!sr.has(n))){e=Nn(r,e),e=Gf(t,e,1),t=ur(t,e,1),e=He(),t!==null&&(Rl(t,1,e),Xe(t,e));break}}t=t.return}}function Am(e,t,r){var n=e.pingCache;n!==null&&n.delete(t),t=He(),e.pingedLanes|=e.suspendedLanes&r,Re===e&&(be&r)===r&&(Ne===4||Ne===3&&(be&130023424)===be&&500>ge()-Ju?Lr(e,0):Gu|=r),Xe(e,t)}function mh(e,t){t===0&&((e.mode&1)===0?t=1:(t=Ql,Ql<<=1,(Ql&130023424)===0&&(Ql=4194304)));var r=He();e=$t(e,t),e!==null&&(Rl(e,t,r),Xe(e,r))}function bm(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),mh(e,r)}function Pm(e,t){var r=0;switch(e.tag){case 13:var n=e.stateNode,l=e.memoizedState;l!==null&&(r=l.retryLane);break;case 19:n=e.stateNode;break;default:throw Error(_(314))}n!==null&&n.delete(t),mh(e,r)}var yh;yh=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ge.current)Ye=!0;else{if((e.lanes&r)===0&&(t.flags&128)===0)return Ye=!1,xm(e,t,r);Ye=(e.flags&131072)!==0}else Ye=!1,de&&(t.flags&1048576)!==0&&wf(t,To,t.index);switch(t.lanes=0,t.tag){case 2:var n=t.type;yo(e,t),e=t.pendingProps;var l=kn(t,ze.current);yn(t,r),l=Vu(null,t,n,e,l,r);var o=Hu();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Je(n)?(o=!0,Io(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,zu(t),l.updater=fi,t.stateNode=l,l._reactInternals=t,Oa(t,n,e,r),t=Ua(null,t,n,!0,o,r)):(t.tag=0,de&&o&&bu(t),Ve(null,t,l,r),t=t.child),t;case 16:n=t.elementType;e:{switch(yo(e,t),e=t.pendingProps,l=n._init,n=l(n._payload),t.type=n,l=t.tag=Lm(n),e=vt(n,e),l){case 0:t=ja(null,t,n,e,r);break e;case 1:t=Ec(null,t,n,e,r);break e;case 11:t=kc(null,t,n,e,r);break e;case 14:t=Sc(null,t,n,vt(n.type,e),r);break e}throw Error(_(306,n,""))}return t;case 0:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:vt(n,l),ja(e,t,n,l,r);case 1:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:vt(n,l),Ec(e,t,n,l,r);case 3:e:{if(Zf(t),e===null)throw Error(_(387));n=t.pendingProps,o=t.memoizedState,l=o.element,Df(e,t),$o(t,n,null,r);var i=t.memoizedState;if(n=i.element,o.isDehydrated)if(o={element:n,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=Nn(Error(_(423)),t),t=Cc(e,t,n,r,l);break e}else if(n!==l){l=Nn(Error(_(424)),t),t=Cc(e,t,n,r,l);break e}else for(rt=ar(t.stateNode.containerInfo.firstChild),nt=t,de=!0,wt=null,r=Cf(t,null,n,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(Sn(),n===l){t=jt(e,t,r);break e}Ve(e,t,n,r)}t=t.child}return t;case 5:return Ff(t),e===null&&Ma(t),n=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,i=l.children,ba(n,l)?i=null:o!==null&&ba(n,o)&&(t.flags|=32),qf(e,t),Ve(e,t,i,r),t.child;case 6:return e===null&&Ma(t),null;case 13:return eh(e,t,r);case 4:return Ou(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=En(t,null,n,r):Ve(e,t,n,r),t.child;case 11:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:vt(n,l),kc(e,t,n,l,r);case 7:return Ve(e,t,t.pendingProps,r),t.child;case 8:return Ve(e,t,t.pendingProps.children,r),t.child;case 12:return Ve(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(n=t.type._context,l=t.pendingProps,o=t.memoizedProps,i=l.value,ue(zo,n._currentValue),n._currentValue=i,o!==null)if(Et(o.value,i)){if(o.children===l.children&&!Ge.current){t=jt(e,t,r);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var u=o.dependencies;if(u!==null){i=o.child;for(var a=u.firstContext;a!==null;){if(a.context===n){if(o.tag===1){a=Tt(-1,r&-r),a.tag=2;var s=o.updateQueue;if(s!==null){s=s.shared;var f=s.pending;f===null?a.next=a:(a.next=f.next,f.next=a),s.pending=a}}o.lanes|=r,a=o.alternate,a!==null&&(a.lanes|=r),Ta(o.return,r,t),u.lanes|=r;break}a=a.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(_(341));i.lanes|=r,u=i.alternate,u!==null&&(u.lanes|=r),Ta(i,r,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}Ve(e,t,l.children,r),t=t.child}return t;case 9:return l=t.type,n=t.pendingProps.children,yn(t,r),l=pt(l),n=n(l),t.flags|=1,Ve(e,t,n,r),t.child;case 14:return n=t.type,l=vt(n,t.pendingProps),l=vt(n.type,l),Sc(e,t,n,l,r);case 15:return Jf(e,t,t.type,t.pendingProps,r);case 17:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:vt(n,l),yo(e,t),t.tag=1,Je(n)?(e=!0,Io(t)):e=!1,yn(t,r),Kf(t,n,l),Oa(t,n,l,r),Ua(null,t,n,!0,e,r);case 19:return th(e,t,r);case 22:return Xf(e,t,r)}throw Error(_(156,t.tag))};function gh(e,t){return Vd(e,t)}function _m(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ft(e,t,r,n){return new _m(e,t,r,n)}function es(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Lm(e){if(typeof e=="function")return es(e)?1:0;if(e!=null){if(e=e.$$typeof,e===xu)return 11;if(e===wu)return 14}return 2}function dr(e,t){var r=e.alternate;return r===null?(r=ft(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function xo(e,t,r,n,l,o){var i=2;if(n=e,typeof e=="function")es(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case Zr:return Ir(r.children,l,o,t);case vu:i=8,l|=8;break;case sa:return e=ft(12,r,t,l|2),e.elementType=sa,e.lanes=o,e;case ca:return e=ft(13,r,t,l),e.elementType=ca,e.lanes=o,e;case da:return e=ft(19,r,t,l),e.elementType=da,e.lanes=o,e;case Dd:return mi(r,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Cd:i=10;break e;case Nd:i=9;break e;case xu:i=11;break e;case wu:i=14;break e;case Xt:i=16,n=null;break e}throw Error(_(130,e==null?e:typeof e,""))}return t=ft(i,r,t,l),t.elementType=e,t.type=n,t.lanes=o,t}function Ir(e,t,r,n){return e=ft(7,e,n,t),e.lanes=r,e}function mi(e,t,r,n){return e=ft(22,e,n,t),e.elementType=Dd,e.lanes=r,e.stateNode={isHidden:!1},e}function ea(e,t,r){return e=ft(6,e,null,t),e.lanes=r,e}function ta(e,t,r){return t=ft(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Im(e,t,r,n,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ii(0),this.expirationTimes=Ii(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ii(0),this.identifierPrefix=n,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function ts(e,t,r,n,l,o,i,u,a){return e=new Im(e,t,r,u,a),t===1?(t=1,o===!0&&(t|=8)):t=0,o=ft(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:n,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},zu(o),e}function Mm(e,t,r){var n=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:qr,key:n==null?null:""+n,children:e,containerInfo:t,implementation:r}}function vh(e){if(!e)return hr;e=e._reactInternals;e:{if(Ur(e)!==e||e.tag!==1)throw Error(_(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Je(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(_(171))}if(e.tag===1){var r=e.type;if(Je(r))return vf(e,r,t)}return t}function xh(e,t,r,n,l,o,i,u,a){return e=ts(r,n,!0,e,l,o,i,u,a),e.context=vh(null),r=e.current,n=He(),l=cr(r),o=Tt(n,l),o.callback=t!=null?t:null,ur(r,o,l),e.current.lanes=l,Rl(e,l,n),Xe(e,n),e}function yi(e,t,r,n){var l=t.current,o=He(),i=cr(l);return r=vh(r),t.context===null?t.context=r:t.pendingContext=r,t=Tt(o,i),t.payload={element:e},n=n===void 0?null:n,n!==null&&(t.callback=n),e=ur(l,t,i),e!==null&&(St(e,l,i,o),ho(e,l,i)),i}function Yo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Lc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function rs(e,t){Lc(e,t),(e=e.alternate)&&Lc(e,t)}function Tm(){return null}var wh=typeof reportError=="function"?reportError:function(e){console.error(e)};function ns(e){this._internalRoot=e}gi.prototype.render=ns.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(_(409));yi(e,t,null,null)};gi.prototype.unmount=ns.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;$r(function(){yi(null,e,null,null)}),t[Ot]=null}};function gi(e){this._internalRoot=e}gi.prototype.unstable_scheduleHydration=function(e){if(e){var t=Jd();e={blockedOn:null,target:e,priority:t};for(var r=0;r<Zt.length&&t!==0&&t<Zt[r].priority;r++);Zt.splice(r,0,e),r===0&&qd(e)}};function ls(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function vi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ic(){}function zm(e,t,r,n,l){if(l){if(typeof n=="function"){var o=n;n=function(){var s=Yo(i);o.call(s)}}var i=xh(t,n,e,0,null,!1,!1,"",Ic);return e._reactRootContainer=i,e[Ot]=i.current,yl(e.nodeType===8?e.parentNode:e),$r(),i}for(;l=e.lastChild;)e.removeChild(l);if(typeof n=="function"){var u=n;n=function(){var s=Yo(a);u.call(s)}}var a=ts(e,0,!1,null,null,!1,!1,"",Ic);return e._reactRootContainer=a,e[Ot]=a.current,yl(e.nodeType===8?e.parentNode:e),$r(function(){yi(t,a,r,n)}),a}function xi(e,t,r,n,l){var o=r._reactRootContainer;if(o){var i=o;if(typeof l=="function"){var u=l;l=function(){var a=Yo(i);u.call(a)}}yi(t,i,e,l)}else i=zm(r,t,e,l,n);return Yo(i)}Yd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=Gn(t.pendingLanes);r!==0&&(Eu(t,r|1),Xe(t,ge()),(te&6)===0&&(Dn=ge()+500,gr()))}break;case 13:$r(function(){var n=$t(e,1);if(n!==null){var l=He();St(n,e,1,l)}}),rs(e,1)}};Cu=function(e){if(e.tag===13){var t=$t(e,134217728);if(t!==null){var r=He();St(t,e,134217728,r)}rs(e,134217728)}};Gd=function(e){if(e.tag===13){var t=cr(e),r=$t(e,t);if(r!==null){var n=He();St(r,e,t,n)}rs(e,t)}};Jd=function(){return oe};Xd=function(e,t){var r=oe;try{return oe=e,t()}finally{oe=r}};ka=function(e,t,r){switch(t){case"input":if(pa(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var n=r[t];if(n!==e&&n.form===e.form){var l=si(n);if(!l)throw Error(_(90));Bd(n),pa(n,l)}}}break;case"textarea":Ad(e,r);break;case"select":t=r.value,t!=null&&fn(e,!!r.multiple,t,!1)}};Td=Xu;zd=$r;var Om={usingClientEntryPoint:!1,Events:[bl,nn,si,Id,Md,Xu]},Vn={findFiberByHostInstance:Ar,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},$m={bundleType:Vn.bundleType,version:Vn.version,rendererPackageName:Vn.rendererPackageName,rendererConfig:Vn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Ut.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=jd(e),e===null?null:e.stateNode},findFiberByHostInstance:Vn.findFiberByHostInstance||Tm,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var no=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!no.isDisabled&&no.supportsFiber)try{oi=no.inject($m),Rt=no}catch{}}ot.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Om;ot.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ls(t))throw Error(_(200));return Mm(e,t,null,r)};ot.createRoot=function(e,t){if(!ls(e))throw Error(_(299));var r=!1,n="",l=wh;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=ts(e,1,!1,null,null,r,!1,n,l),e[Ot]=t.current,yl(e.nodeType===8?e.parentNode:e),new ns(t)};ot.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=jd(t),e=e===null?null:e.stateNode,e};ot.flushSync=function(e){return $r(e)};ot.hydrate=function(e,t,r){if(!vi(t))throw Error(_(200));return xi(null,e,t,!0,r)};ot.hydrateRoot=function(e,t,r){if(!ls(e))throw Error(_(405));var n=r!=null&&r.hydratedSources||null,l=!1,o="",i=wh;if(r!=null&&(r.unstable_strictMode===!0&&(l=!0),r.identifierPrefix!==void 0&&(o=r.identifierPrefix),r.onRecoverableError!==void 0&&(i=r.onRecoverableError)),t=xh(t,null,e,1,r!=null?r:null,l,!1,o,i),e[Ot]=t.current,yl(e),n)for(e=0;e<n.length;e++)r=n[e],l=r._getVersion,l=l(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,l]:t.mutableSourceEagerHydrationData.push(r,l);return new gi(t)};ot.render=function(e,t,r){if(!vi(t))throw Error(_(200));return xi(null,e,t,!1,r)};ot.unmountComponentAtNode=function(e){if(!vi(e))throw Error(_(40));return e._reactRootContainer?($r(function(){xi(null,null,e,!1,function(){e._reactRootContainer=null,e[Ot]=null})}),!0):!1};ot.unstable_batchedUpdates=Xu;ot.unstable_renderSubtreeIntoContainer=function(e,t,r,n){if(!vi(r))throw Error(_(200));if(e==null||e._reactInternals===void 0)throw Error(_(38));return xi(e,t,r,!1,n)};ot.version="18.3.1-next-f1338f8080-20240426";(function(e){function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(r){console.error(r)}}t(),e.exports=ot})(pu);var kh,Mc=pu.exports;kh=Mc.createRoot,Mc.hydrateRoot;const jm="modulepreload",Um=function(e){return"/"+e},Tc={},Vm=function(t,r,n){if(!r||r.length===0)return t();const l=document.getElementsByTagName("link");return Promise.all(r.map(o=>{if(o=Um(o),o in Tc)return;Tc[o]=!0;const i=o.endsWith(".css"),u=i?'[rel="stylesheet"]':"";if(!!n)for(let f=l.length-1;f>=0;f--){const y=l[f];if(y.href===o&&(!i||y.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${u}`))return;const s=document.createElement("link");if(s.rel=i?"stylesheet":jm,i||(s.as="script",s.crossOrigin=""),s.href=o,document.head.appendChild(s),i)return new Promise((f,y)=>{s.addEventListener("load",f),s.addEventListener("error",()=>y(new Error(`Unable to preload CSS for ${o}`)))})})).then(()=>t())};var os={};Object.defineProperty(os,"__esModule",{value:!0});os.parse=Jm;os.serialize=Xm;const Hm=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,Wm=/^[\u0021-\u003A\u003C-\u007E]*$/,Qm=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,Km=/^[\u0020-\u003A\u003D-\u007E]*$/,Ym=Object.prototype.toString,Gm=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function Jm(e,t){const r=new Gm,n=e.length;if(n<2)return r;const l=(t==null?void 0:t.decode)||qm;let o=0;do{const i=e.indexOf("=",o);if(i===-1)break;const u=e.indexOf(";",o),a=u===-1?n:u;if(i>a){o=e.lastIndexOf(";",i-1)+1;continue}const s=zc(e,o,i),f=Oc(e,i,s),y=e.slice(s,f);if(r[y]===void 0){let h=zc(e,i+1,a),w=Oc(e,a,h);const k=l(e.slice(h,w));r[y]=k}o=a+1}while(o<n);return r}function zc(e,t,r){do{const n=e.charCodeAt(t);if(n!==32&&n!==9)return t}while(++t<r);return r}function Oc(e,t,r){for(;t>r;){const n=e.charCodeAt(--t);if(n!==32&&n!==9)return t+1}return r}function Xm(e,t,r){const n=(r==null?void 0:r.encode)||encodeURIComponent;if(!Hm.test(e))throw new TypeError(`argument name is invalid: ${e}`);const l=n(t);if(!Wm.test(l))throw new TypeError(`argument val is invalid: ${t}`);let o=e+"="+l;if(!r)return o;if(r.maxAge!==void 0){if(!Number.isInteger(r.maxAge))throw new TypeError(`option maxAge is invalid: ${r.maxAge}`);o+="; Max-Age="+r.maxAge}if(r.domain){if(!Qm.test(r.domain))throw new TypeError(`option domain is invalid: ${r.domain}`);o+="; Domain="+r.domain}if(r.path){if(!Km.test(r.path))throw new TypeError(`option path is invalid: ${r.path}`);o+="; Path="+r.path}if(r.expires){if(!Zm(r.expires)||!Number.isFinite(r.expires.valueOf()))throw new TypeError(`option expires is invalid: ${r.expires}`);o+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(o+="; HttpOnly"),r.secure&&(o+="; Secure"),r.partitioned&&(o+="; Partitioned"),r.priority)switch(typeof r.priority=="string"?r.priority.toLowerCase():void 0){case"low":o+="; Priority=Low";break;case"medium":o+="; Priority=Medium";break;case"high":o+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch(typeof r.sameSite=="string"?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":o+="; SameSite=Strict";break;case"lax":o+="; SameSite=Lax";break;case"none":o+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${r.sameSite}`)}return o}function qm(e){if(e.indexOf("%")===-1)return e;try{return decodeURIComponent(e)}catch{return e}}function Zm(e){return Ym.call(e)==="[object Date]"}var wi={exports:{}},ki={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ey=E.exports,ty=Symbol.for("react.element"),ry=Symbol.for("react.fragment"),ny=Object.prototype.hasOwnProperty,ly=ey.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,oy={key:!0,ref:!0,__self:!0,__source:!0};function Sh(e,t,r){var n,l={},o=null,i=null;r!==void 0&&(o=""+r),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(n in t)ny.call(t,n)&&!oy.hasOwnProperty(n)&&(l[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)l[n]===void 0&&(l[n]=t[n]);return{$$typeof:ty,type:e,key:o,ref:i,props:l,_owner:ly.current}}ki.Fragment=ry;ki.jsx=Sh;ki.jsxs=Sh;(function(e){e.exports=ki})(wi);const is=wi.exports.Fragment,d=wi.exports.jsx,S=wi.exports.jsxs;var Eh=e=>{throw TypeError(e)},iy=(e,t,r)=>t.has(e)||Eh("Cannot "+r),ra=(e,t,r)=>(iy(e,t,"read from private field"),r?r.call(e):t.get(e)),ay=(e,t,r)=>t.has(e)?Eh("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),$c="popstate";function uy(e={}){function t(n,l){let{pathname:o,search:i,hash:u}=n.location;return Nl("",{pathname:o,search:i,hash:u},l.state&&l.state.usr||null,l.state&&l.state.key||"default")}function r(n,l){return typeof l=="string"?l:pr(l)}return cy(t,r,null,e)}function J(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function ve(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function sy(){return Math.random().toString(36).substring(2,10)}function jc(e,t){return{usr:e.state,key:e.key,idx:t}}function Nl(e,t,r=null,n){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Vt(t):t,state:r,key:t&&t.key||n||sy()}}function pr({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Vt(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substring(n),e=e.substring(0,n)),e&&(t.pathname=e)}return t}function cy(e,t,r,n={}){let{window:l=document.defaultView,v5Compat:o=!1}=n,i=l.history,u="POP",a=null,s=f();s==null&&(s=0,i.replaceState({...i.state,idx:s},""));function f(){return(i.state||{idx:null}).idx}function y(){u="POP";let A=f(),m=A==null?null:A-s;s=A,a&&a({action:u,location:C.location,delta:m})}function h(A,m){u="PUSH";let p=Nl(C.location,A,m);r&&r(p,A),s=f()+1;let c=jc(p,s),g=C.createHref(p);try{i.pushState(c,"",g)}catch(B){if(B instanceof DOMException&&B.name==="DataCloneError")throw B;l.location.assign(g)}o&&a&&a({action:u,location:C.location,delta:1})}function w(A,m){u="REPLACE";let p=Nl(C.location,A,m);r&&r(p,A),s=f();let c=jc(p,s),g=C.createHref(p);i.replaceState(c,"",g),o&&a&&a({action:u,location:C.location,delta:0})}function k(A){return Ch(A)}let C={get action(){return u},get location(){return e(l,i)},listen(A){if(a)throw new Error("A history only accepts one active listener");return l.addEventListener($c,y),a=A,()=>{l.removeEventListener($c,y),a=null}},createHref(A){return t(l,A)},createURL:k,encodeLocation(A){let m=k(A);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:h,replace:w,go(A){return i.go(A)}};return C}function Ch(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),J(r,"No window.location.(origin|href) available to create URL");let n=typeof e=="string"?e:pr(e);return n=n.replace(/ $/,"%20"),!t&&n.startsWith("//")&&(n=r+n),new URL(n,r)}var Xn,Uc=class{constructor(e){if(ay(this,Xn,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(ra(this,Xn).has(e))return ra(this,Xn).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,t){ra(this,Xn).set(e,t)}};Xn=new WeakMap;var dy=new Set(["lazy","caseSensitive","path","id","index","children"]);function fy(e){return dy.has(e)}var hy=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function py(e){return hy.has(e)}function my(e){return e.index===!0}function Go(e,t,r=[],n={}){return e.map((l,o)=>{let i=[...r,String(o)],u=typeof l.id=="string"?l.id:i.join("-");if(J(l.index!==!0||!l.children,"Cannot specify children on an index route"),J(!n[u],`Found a route id collision on id "${u}".  Route id's must be globally unique within Data Router usages`),my(l)){let a={...l,...t(l),id:u};return n[u]=a,a}else{let a={...l,...t(l),id:u,children:void 0};return n[u]=a,l.children&&(a.children=Go(l.children,t,i,n)),a}})}function tr(e,t,r="/"){return wo(e,t,r,!1)}function wo(e,t,r,n){let l=typeof t=="string"?Vt(t):t,o=yt(l.pathname||"/",r);if(o==null)return null;let i=Nh(e);gy(i);let u=null;for(let a=0;u==null&&a<i.length;++a){let s=By(o);u=Dy(i[a],s,n)}return u}function yy(e,t){let{route:r,pathname:n,params:l}=e;return{id:r.id,pathname:n,params:l,data:t[r.id],handle:r.handle}}function Nh(e,t=[],r=[],n=""){let l=(o,i,u)=>{let a={relativePath:u===void 0?o.path||"":u,caseSensitive:o.caseSensitive===!0,childrenIndex:i,route:o};a.relativePath.startsWith("/")&&(J(a.relativePath.startsWith(n),`Absolute route path "${a.relativePath}" nested under path "${n}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),a.relativePath=a.relativePath.slice(n.length));let s=bt([n,a.relativePath]),f=r.concat(a);o.children&&o.children.length>0&&(J(o.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${s}".`),Nh(o.children,t,f,s)),!(o.path==null&&!o.index)&&t.push({path:s,score:Cy(s,o.index),routesMeta:f})};return e.forEach((o,i)=>{var u;if(o.path===""||!((u=o.path)!=null&&u.includes("?")))l(o,i);else for(let a of Dh(o.path))l(o,i,a)}),t}function Dh(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,l=r.endsWith("?"),o=r.replace(/\?$/,"");if(n.length===0)return l?[o,""]:[o];let i=Dh(n.join("/")),u=[];return u.push(...i.map(a=>a===""?o:[o,a].join("/"))),l&&u.push(...i),u.map(a=>e.startsWith("/")&&a===""?"/":a)}function gy(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Ny(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}var vy=/^:[\w-]+$/,xy=3,wy=2,ky=1,Sy=10,Ey=-2,Vc=e=>e==="*";function Cy(e,t){let r=e.split("/"),n=r.length;return r.some(Vc)&&(n+=Ey),t&&(n+=wy),r.filter(l=>!Vc(l)).reduce((l,o)=>l+(vy.test(o)?xy:o===""?ky:Sy),n)}function Ny(e,t){return e.length===t.length&&e.slice(0,-1).every((n,l)=>n===t[l])?e[e.length-1]-t[t.length-1]:0}function Dy(e,t,r=!1){let{routesMeta:n}=e,l={},o="/",i=[];for(let u=0;u<n.length;++u){let a=n[u],s=u===n.length-1,f=o==="/"?t:t.slice(o.length)||"/",y=Jo({path:a.relativePath,caseSensitive:a.caseSensitive,end:s},f),h=a.route;if(!y&&s&&r&&!n[n.length-1].route.index&&(y=Jo({path:a.relativePath,caseSensitive:a.caseSensitive,end:!1},f)),!y)return null;Object.assign(l,y.params),i.push({params:l,pathname:bt([o,y.pathname]),pathnameBase:by(bt([o,y.pathnameBase])),route:h}),y.pathnameBase!=="/"&&(o=bt([o,y.pathnameBase]))}return i}function Jo(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=Fy(e.path,e.caseSensitive,e.end),l=t.match(r);if(!l)return null;let o=l[0],i=o.replace(/(.)\/+$/,"$1"),u=l.slice(1);return{params:n.reduce((s,{paramName:f,isOptional:y},h)=>{if(f==="*"){let k=u[h]||"";i=o.slice(0,o.length-k.length).replace(/(.)\/+$/,"$1")}const w=u[h];return y&&!w?s[f]=void 0:s[f]=(w||"").replace(/%2F/g,"/"),s},{}),pathname:o,pathnameBase:i,pattern:e}}function Fy(e,t=!1,r=!0){ve(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let n=[],l="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,u,a)=>(n.push({paramName:u,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),l+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?l+="\\/*$":e!==""&&e!=="/"&&(l+="(?:(?=\\/|$))"),[new RegExp(l,t?void 0:"i"),n]}function By(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return ve(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function yt(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function Ry(e,t="/"){let{pathname:r,search:n="",hash:l=""}=typeof e=="string"?Vt(e):e;return{pathname:r?r.startsWith("/")?r:Ay(r,t):t,search:Py(n),hash:_y(l)}}function Ay(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(l=>{l===".."?r.length>1&&r.pop():l!=="."&&r.push(l)}),r.length>1?r.join("/"):"/"}function na(e,t,r,n){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(n)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Fh(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function Si(e){let t=Fh(e);return t.map((r,n)=>n===t.length-1?r.pathname:r.pathnameBase)}function Ei(e,t,r,n=!1){let l;typeof e=="string"?l=Vt(e):(l={...e},J(!l.pathname||!l.pathname.includes("?"),na("?","pathname","search",l)),J(!l.pathname||!l.pathname.includes("#"),na("#","pathname","hash",l)),J(!l.search||!l.search.includes("#"),na("#","search","hash",l)));let o=e===""||l.pathname==="",i=o?"/":l.pathname,u;if(i==null)u=r;else{let y=t.length-1;if(!n&&i.startsWith("..")){let h=i.split("/");for(;h[0]==="..";)h.shift(),y-=1;l.pathname=h.join("/")}u=y>=0?t[y]:"/"}let a=Ry(l,u),s=i&&i!=="/"&&i.endsWith("/"),f=(o||i===".")&&r.endsWith("/");return!a.pathname.endsWith("/")&&(s||f)&&(a.pathname+="/"),a}var bt=e=>e.join("/").replace(/\/\/+/g,"/"),by=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Py=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,_y=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,Xo=class{constructor(e,t,r,n=!1){this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function Dl(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var Bh=["POST","PUT","PATCH","DELETE"],Ly=new Set(Bh),Iy=["GET",...Bh],My=new Set(Iy),Ty=new Set([301,302,303,307,308]),zy=new Set([307,308]),la={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Oy={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Hn={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},as=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,$y=e=>({hasErrorBoundary:Boolean(e.hasErrorBoundary)}),Rh="remix-router-transitions",Ah=Symbol("ResetLoaderData");function jy(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u";J(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let n=e.hydrationRouteProperties||[],l=e.mapRouteProperties||$y,o={},i=Go(e.routes,l,void 0,o),u,a=e.basename||"/",s=e.dataStrategy||Qy,f={unstable_middleware:!1,...e.future},y=null,h=new Set,w=null,k=null,C=null,A=e.hydrationData!=null,m=tr(i,e.history.location,a),p=!1,c=null,g;if(m==null&&!e.patchRoutesOnNavigation){let x=st(404,{pathname:e.history.location.pathname}),{matches:N,route:F}=td(i);g=!0,m=N,c={[F.id]:x}}else if(m&&!e.hydrationData&&Tl(m,i,e.history.location.pathname).active&&(m=null),m)if(m.some(x=>x.route.lazy))g=!1;else if(!m.some(x=>x.route.loader))g=!0;else{let x=e.hydrationData?e.hydrationData.loaderData:null,N=e.hydrationData?e.hydrationData.errors:null;if(N){let F=m.findIndex(P=>N[P.route.id]!==void 0);g=m.slice(0,F+1).every(P=>!tu(P.route,x,N))}else g=m.every(F=>!tu(F.route,x,N))}else{g=!1,m=[];let x=Tl(null,i,e.history.location.pathname);x.active&&x.matches&&(p=!0,m=x.matches)}let B,v={historyAction:e.history.action,location:e.history.location,matches:m,initialized:g,navigation:la,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||c,fetchers:new Map,blockers:new Map},R="POP",b=!1,z,D=!1,j=new Map,Fe=null,_e=!1,re=!1,M=new Set,Z=new Map,Se=0,L=-1,$=new Map,V=new Set,ee=new Map,le=new Map,xe=new Set,$e=new Map,Wt,je=null;function Wr(){if(y=e.history.listen(({action:x,location:N,delta:F})=>{if(Wt){Wt(),Wt=void 0;return}ve($e.size===0||F!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let P=Cs({currentLocation:v.location,nextLocation:N,historyAction:x});if(P&&F!=null){let I=new Promise(U=>{Wt=U});e.history.go(F*-1),Ml(P,{state:"blocked",location:N,proceed(){Ml(P,{state:"proceeding",proceed:void 0,reset:void 0,location:N}),I.then(()=>e.history.go(F))},reset(){let U=new Map(v.blockers);U.set(P,Hn),Ue({blockers:U})}});return}return Er(x,N)}),r){ng(t,j);let x=()=>lg(t,j);t.addEventListener("pagehide",x),Fe=()=>t.removeEventListener("pagehide",x)}return v.initialized||Er("POP",v.location,{initialHydration:!0}),B}function n0(){y&&y(),Fe&&Fe(),h.clear(),z&&z.abort(),v.fetchers.forEach((x,N)=>Di(N)),v.blockers.forEach((x,N)=>Es(N))}function l0(x){return h.add(x),()=>h.delete(x)}function Ue(x,N={}){v={...v,...x};let F=[],P=[];v.fetchers.forEach((I,U)=>{I.state==="idle"&&(xe.has(U)?F.push(U):P.push(U))}),xe.forEach(I=>{!v.fetchers.has(I)&&!Z.has(I)&&F.push(I)}),[...h].forEach(I=>I(v,{deletedFetchers:F,viewTransitionOpts:N.viewTransitionOpts,flushSync:N.flushSync===!0})),F.forEach(I=>Di(I)),P.forEach(I=>v.fetchers.delete(I))}function Qr(x,N,{flushSync:F}={}){var Q,Y;let P=v.actionData!=null&&v.navigation.formMethod!=null&&tt(v.navigation.formMethod)&&v.navigation.state==="loading"&&((Q=x.state)==null?void 0:Q._isRedirect)!==!0,I;N.actionData?Object.keys(N.actionData).length>0?I=N.actionData:I=null:P?I=v.actionData:I=null;let U=N.loaderData?Zc(v.loaderData,N.loaderData,N.matches||[],N.errors):v.loaderData,K=v.blockers;K.size>0&&(K=new Map(K),K.forEach((W,G)=>K.set(G,Hn)));let O=b===!0||v.navigation.formMethod!=null&&tt(v.navigation.formMethod)&&((Y=x.state)==null?void 0:Y._isRedirect)!==!0;u&&(i=u,u=void 0),_e||R==="POP"||(R==="PUSH"?e.history.push(x,x.state):R==="REPLACE"&&e.history.replace(x,x.state));let H;if(R==="POP"){let W=j.get(v.location.pathname);W&&W.has(x.pathname)?H={currentLocation:v.location,nextLocation:x}:j.has(x.pathname)&&(H={currentLocation:x,nextLocation:v.location})}else if(D){let W=j.get(v.location.pathname);W?W.add(x.pathname):(W=new Set([x.pathname]),j.set(v.location.pathname,W)),H={currentLocation:v.location,nextLocation:x}}Ue({...N,actionData:I,loaderData:U,historyAction:R,location:x,initialized:!0,navigation:la,revalidation:"idle",restoreScrollPosition:Ds(x,N.matches||v.matches),preventScrollReset:O,blockers:K},{viewTransitionOpts:H,flushSync:F===!0}),R="POP",b=!1,D=!1,_e=!1,re=!1,je==null||je.resolve(),je=null}async function ys(x,N){if(typeof x=="number"){e.history.go(x);return}let F=eu(v.location,v.matches,a,x,N==null?void 0:N.fromRouteId,N==null?void 0:N.relative),{path:P,submission:I,error:U}=Hc(!1,F,N),K=v.location,O=Nl(v.location,P,N&&N.state);O={...O,...e.history.encodeLocation(O)};let H=N&&N.replace!=null?N.replace:void 0,Q="PUSH";H===!0?Q="REPLACE":H===!1||I!=null&&tt(I.formMethod)&&I.formAction===v.location.pathname+v.location.search&&(Q="REPLACE");let Y=N&&"preventScrollReset"in N?N.preventScrollReset===!0:void 0,W=(N&&N.flushSync)===!0,G=Cs({currentLocation:K,nextLocation:O,historyAction:Q});if(G){Ml(G,{state:"blocked",location:O,proceed(){Ml(G,{state:"proceeding",proceed:void 0,reset:void 0,location:O}),ys(x,N)},reset(){let ae=new Map(v.blockers);ae.set(G,Hn),Ue({blockers:ae})}});return}await Er(Q,O,{submission:I,pendingError:U,preventScrollReset:Y,replace:N&&N.replace,enableViewTransition:N&&N.viewTransition,flushSync:W})}function o0(){je||(je=og()),Ni(),Ue({revalidation:"loading"});let x=je.promise;return v.navigation.state==="submitting"?x:v.navigation.state==="idle"?(Er(v.historyAction,v.location,{startUninterruptedRevalidation:!0}),x):(Er(R||v.historyAction,v.navigation.location,{overrideNavigation:v.navigation,enableViewTransition:D===!0}),x)}async function Er(x,N,F){z&&z.abort(),z=null,R=x,_e=(F&&F.startUninterruptedRevalidation)===!0,m0(v.location,v.matches),b=(F&&F.preventScrollReset)===!0,D=(F&&F.enableViewTransition)===!0;let P=u||i,I=F&&F.overrideNavigation,U=(F==null?void 0:F.initialHydration)&&v.matches&&v.matches.length>0&&!p?v.matches:tr(P,N,a),K=(F&&F.flushSync)===!0;if(U&&v.initialized&&!re&&qy(v.location,N)&&!(F&&F.submission&&tt(F.submission.formMethod))){Qr(N,{matches:U},{flushSync:K});return}let O=Tl(U,P,N.pathname);if(O.active&&O.matches&&(U=O.matches),!U){let{error:Le,notFoundMatches:qe,route:ne}=Fi(N.pathname);Qr(N,{matches:qe,loaderData:{},errors:{[ne.id]:Le}},{flushSync:K});return}z=new AbortController;let H=Xr(e.history,N,z.signal,F&&F.submission),Q=new Uc(e.unstable_getContext?await e.unstable_getContext():void 0),Y;if(F&&F.pendingError)Y=[Rr(U).route.id,{type:"error",error:F.pendingError}];else if(F&&F.submission&&tt(F.submission.formMethod)){let Le=await i0(H,N,F.submission,U,Q,O.active,F&&F.initialHydration===!0,{replace:F.replace,flushSync:K});if(Le.shortCircuited)return;if(Le.pendingActionResult){let[qe,ne]=Le.pendingActionResult;if(et(ne)&&Dl(ne.error)&&ne.error.status===404){z=null,Qr(N,{matches:Le.matches,loaderData:{},errors:{[qe]:ne.error}});return}}U=Le.matches||U,Y=Le.pendingActionResult,I=oa(N,F.submission),K=!1,O.active=!1,H=Xr(e.history,H.url,H.signal)}let{shortCircuited:W,matches:G,loaderData:ae,errors:Ee}=await a0(H,N,U,Q,O.active,I,F&&F.submission,F&&F.fetcherSubmission,F&&F.replace,F&&F.initialHydration===!0,K,Y);W||(z=null,Qr(N,{matches:G||U,...ed(Y),loaderData:ae,errors:Ee}))}async function i0(x,N,F,P,I,U,K,O={}){Ni();let H=tg(N,F);if(Ue({navigation:H},{flushSync:O.flushSync===!0}),U){let W=await zl(P,N.pathname,x.signal);if(W.type==="aborted")return{shortCircuited:!0};if(W.type==="error"){let G=Rr(W.partialMatches).route.id;return{matches:W.partialMatches,pendingActionResult:[G,{type:"error",error:W.error}]}}else if(W.matches)P=W.matches;else{let{notFoundMatches:G,error:ae,route:Ee}=Fi(N.pathname);return{matches:G,pendingActionResult:[Ee.id,{type:"error",error:ae}]}}}let Q,Y=qn(P,N);if(!Y.route.action&&!Y.route.lazy)Q={type:"error",error:st(405,{method:x.method,pathname:N.pathname,routeId:Y.route.id})};else{let W=vn(l,o,x,P,Y,K?[]:n,I),G=await bn(x,W,I,null);if(Q=G[Y.route.id],!Q){for(let ae of P)if(G[ae.route.id]){Q=G[ae.route.id];break}}if(x.signal.aborted)return{shortCircuited:!0}}if(_r(Q)){let W;return O&&O.replace!=null?W=O.replace:W=Jc(Q.response.headers.get("Location"),new URL(x.url),a)===v.location.pathname+v.location.search,await Cr(x,Q,!0,{submission:F,replace:W}),{shortCircuited:!0}}if(et(Q)){let W=Rr(P,Y.route.id);return(O&&O.replace)!==!0&&(R="PUSH"),{matches:P,pendingActionResult:[W.route.id,Q,Y.route.id]}}return{matches:P,pendingActionResult:[Y.route.id,Q]}}async function a0(x,N,F,P,I,U,K,O,H,Q,Y,W){let G=U||oa(N,K),ae=K||O||nd(G),Ee=!_e&&!Q;if(I){if(Ee){let ut=gs(W);Ue({navigation:G,...ut!==void 0?{actionData:ut}:{}},{flushSync:Y})}let ie=await zl(F,N.pathname,x.signal);if(ie.type==="aborted")return{shortCircuited:!0};if(ie.type==="error"){let ut=Rr(ie.partialMatches).route.id;return{matches:ie.partialMatches,loaderData:{},errors:{[ut]:ie.error}}}else if(ie.matches)F=ie.matches;else{let{error:ut,notFoundMatches:Yt,route:$l}=Fi(N.pathname);return{matches:Yt,loaderData:{},errors:{[$l.id]:ut}}}}let Le=u||i,{dsMatches:qe,revalidatingFetchers:ne}=Wc(x,P,l,o,e.history,v,F,ae,N,Q?[]:n,Q===!0,re,M,xe,ee,V,Le,a,e.patchRoutesOnNavigation!=null,W);if(L=++Se,!e.dataStrategy&&!qe.some(ie=>ie.shouldLoad)&&ne.length===0){let ie=ks();return Qr(N,{matches:F,loaderData:{},errors:W&&et(W[1])?{[W[0]]:W[1].error}:null,...ed(W),...ie?{fetchers:new Map(v.fetchers)}:{}},{flushSync:Y}),{shortCircuited:!0}}if(Ee){let ie={};if(!I){ie.navigation=G;let ut=gs(W);ut!==void 0&&(ie.actionData=ut)}ne.length>0&&(ie.fetchers=u0(ne)),Ue(ie,{flushSync:Y})}ne.forEach(ie=>{Kt(ie.key),ie.controller&&Z.set(ie.key,ie.controller)});let Pn=()=>ne.forEach(ie=>Kt(ie.key));z&&z.signal.addEventListener("abort",Pn);let{loaderResults:Nr,fetcherResults:_n}=await vs(qe,ne,x,P);if(x.signal.aborted)return{shortCircuited:!0};z&&z.signal.removeEventListener("abort",Pn),ne.forEach(ie=>Z.delete(ie.key));let at=lo(Nr);if(at)return await Cr(x,at.result,!0,{replace:H}),{shortCircuited:!0};if(at=lo(_n),at)return V.add(at.key),await Cr(x,at.result,!0,{replace:H}),{shortCircuited:!0};let{loaderData:Ln,errors:In}=qc(v,F,Nr,W,ne,_n);Q&&v.errors&&(In={...v.errors,...In});let Bi=ks(),Dr=Ss(L),Ol=Bi||Dr||ne.length>0;return{matches:F,loaderData:Ln,errors:In,...Ol?{fetchers:new Map(v.fetchers)}:{}}}function gs(x){if(x&&!et(x[1]))return{[x[0]]:x[1].data};if(v.actionData)return Object.keys(v.actionData).length===0?null:v.actionData}function u0(x){return x.forEach(N=>{let F=v.fetchers.get(N.key),P=Wn(void 0,F?F.data:void 0);v.fetchers.set(N.key,P)}),new Map(v.fetchers)}async function s0(x,N,F,P){Kt(x);let I=(P&&P.flushSync)===!0,U=u||i,K=eu(v.location,v.matches,a,F,N,P==null?void 0:P.relative),O=tr(U,K,a),H=Tl(O,U,K);if(H.active&&H.matches&&(O=H.matches),!O){Pt(x,N,st(404,{pathname:K}),{flushSync:I});return}let{path:Q,submission:Y,error:W}=Hc(!0,K,P);if(W){Pt(x,N,W,{flushSync:I});return}let G=qn(O,Q),ae=new Uc(e.unstable_getContext?await e.unstable_getContext():void 0),Ee=(P&&P.preventScrollReset)===!0;if(Y&&tt(Y.formMethod)){await c0(x,N,Q,G,O,ae,H.active,I,Ee,Y);return}ee.set(x,{routeId:N,path:Q}),await d0(x,N,Q,G,O,ae,H.active,I,Ee,Y)}async function c0(x,N,F,P,I,U,K,O,H,Q){Ni(),ee.delete(x);function Y(we){if(!we.route.action&&!we.route.lazy){let Kr=st(405,{method:Q.formMethod,pathname:F,routeId:N});return Pt(x,N,Kr,{flushSync:O}),!0}return!1}if(!K&&Y(P))return;let W=v.fetchers.get(x);Qt(x,rg(Q,W),{flushSync:O});let G=new AbortController,ae=Xr(e.history,F,G.signal,Q);if(K){let we=await zl(I,F,ae.signal,x);if(we.type==="aborted")return;if(we.type==="error"){Pt(x,N,we.error,{flushSync:O});return}else if(we.matches){if(I=we.matches,P=qn(I,F),Y(P))return}else{Pt(x,N,st(404,{pathname:F}),{flushSync:O});return}}Z.set(x,G);let Ee=Se,Le=vn(l,o,ae,I,P,n,U),ne=(await bn(ae,Le,U,x))[P.route.id];if(ae.signal.aborted){Z.get(x)===G&&Z.delete(x);return}if(xe.has(x)){if(_r(ne)||et(ne)){Qt(x,Jt(void 0));return}}else{if(_r(ne))if(Z.delete(x),L>Ee){Qt(x,Jt(void 0));return}else return V.add(x),Qt(x,Wn(Q)),Cr(ae,ne,!1,{fetcherSubmission:Q,preventScrollReset:H});if(et(ne)){Pt(x,N,ne.error);return}}let Pn=v.navigation.location||v.location,Nr=Xr(e.history,Pn,G.signal),_n=u||i,at=v.navigation.state!=="idle"?tr(_n,v.navigation.location,a):v.matches;J(at,"Didn't find any matches after fetcher action");let Ln=++Se;$.set(x,Ln);let In=Wn(Q,ne.data);v.fetchers.set(x,In);let{dsMatches:Bi,revalidatingFetchers:Dr}=Wc(Nr,U,l,o,e.history,v,at,Q,Pn,n,!1,re,M,xe,ee,V,_n,a,e.patchRoutesOnNavigation!=null,[P.route.id,ne]);Dr.filter(we=>we.key!==x).forEach(we=>{let Kr=we.key,Fs=v.fetchers.get(Kr),v0=Wn(void 0,Fs?Fs.data:void 0);v.fetchers.set(Kr,v0),Kt(Kr),we.controller&&Z.set(Kr,we.controller)}),Ue({fetchers:new Map(v.fetchers)});let Ol=()=>Dr.forEach(we=>Kt(we.key));G.signal.addEventListener("abort",Ol);let{loaderResults:ie,fetcherResults:ut}=await vs(Bi,Dr,Nr,U);if(G.signal.aborted)return;if(G.signal.removeEventListener("abort",Ol),$.delete(x),Z.delete(x),Dr.forEach(we=>Z.delete(we.key)),v.fetchers.has(x)){let we=Jt(ne.data);v.fetchers.set(x,we)}let Yt=lo(ie);if(Yt)return Cr(Nr,Yt.result,!1,{preventScrollReset:H});if(Yt=lo(ut),Yt)return V.add(Yt.key),Cr(Nr,Yt.result,!1,{preventScrollReset:H});let{loaderData:$l,errors:Ri}=qc(v,at,ie,void 0,Dr,ut);Ss(Ln),v.navigation.state==="loading"&&Ln>L?(J(R,"Expected pending action"),z&&z.abort(),Qr(v.navigation.location,{matches:at,loaderData:$l,errors:Ri,fetchers:new Map(v.fetchers)})):(Ue({errors:Ri,loaderData:Zc(v.loaderData,$l,at,Ri),fetchers:new Map(v.fetchers)}),re=!1)}async function d0(x,N,F,P,I,U,K,O,H,Q){let Y=v.fetchers.get(x);Qt(x,Wn(Q,Y?Y.data:void 0),{flushSync:O});let W=new AbortController,G=Xr(e.history,F,W.signal);if(K){let ne=await zl(I,F,G.signal,x);if(ne.type==="aborted")return;if(ne.type==="error"){Pt(x,N,ne.error,{flushSync:O});return}else if(ne.matches)I=ne.matches,P=qn(I,F);else{Pt(x,N,st(404,{pathname:F}),{flushSync:O});return}}Z.set(x,W);let ae=Se,Ee=vn(l,o,G,I,P,n,U),qe=(await bn(G,Ee,U,x))[P.route.id];if(Z.get(x)===W&&Z.delete(x),!G.signal.aborted){if(xe.has(x)){Qt(x,Jt(void 0));return}if(_r(qe))if(L>ae){Qt(x,Jt(void 0));return}else{V.add(x),await Cr(G,qe,!1,{preventScrollReset:H});return}if(et(qe)){Pt(x,N,qe.error);return}Qt(x,Jt(qe.data))}}async function Cr(x,N,F,{submission:P,fetcherSubmission:I,preventScrollReset:U,replace:K}={}){N.response.headers.has("X-Remix-Revalidate")&&(re=!0);let O=N.response.headers.get("Location");J(O,"Expected a Location header on the redirect Response"),O=Jc(O,new URL(x.url),a);let H=Nl(v.location,O,{_isRedirect:!0});if(r){let Ee=!1;if(N.response.headers.has("X-Remix-Reload-Document"))Ee=!0;else if(as.test(O)){const Le=Ch(O,!0);Ee=Le.origin!==t.location.origin||yt(Le.pathname,a)==null}if(Ee){K?t.location.replace(O):t.location.assign(O);return}}z=null;let Q=K===!0||N.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:Y,formAction:W,formEncType:G}=v.navigation;!P&&!I&&Y&&W&&G&&(P=nd(v.navigation));let ae=P||I;if(zy.has(N.response.status)&&ae&&tt(ae.formMethod))await Er(Q,H,{submission:{...ae,formAction:O},preventScrollReset:U||b,enableViewTransition:F?D:void 0});else{let Ee=oa(H,P);await Er(Q,H,{overrideNavigation:Ee,fetcherSubmission:I,preventScrollReset:U||b,enableViewTransition:F?D:void 0})}}async function bn(x,N,F,P){let I,U={};try{I=await Ky(s,x,N,P,F,!1)}catch(K){return N.filter(O=>O.shouldLoad).forEach(O=>{U[O.route.id]={type:"error",error:K}}),U}if(x.signal.aborted)return U;for(let[K,O]of Object.entries(I))if(Zy(O)){let H=O.result;U[K]={type:"redirect",response:Jy(H,x,K,N,a)}}else U[K]=await Gy(O);return U}async function vs(x,N,F,P){let I=bn(F,x,P,null),U=Promise.all(N.map(async H=>{if(H.matches&&H.match&&H.request&&H.controller){let Y=(await bn(H.request,H.matches,P,H.key))[H.match.route.id];return{[H.key]:Y}}else return Promise.resolve({[H.key]:{type:"error",error:st(404,{pathname:H.path})}})})),K=await I,O=(await U).reduce((H,Q)=>Object.assign(H,Q),{});return{loaderResults:K,fetcherResults:O}}function Ni(){re=!0,ee.forEach((x,N)=>{Z.has(N)&&M.add(N),Kt(N)})}function Qt(x,N,F={}){v.fetchers.set(x,N),Ue({fetchers:new Map(v.fetchers)},{flushSync:(F&&F.flushSync)===!0})}function Pt(x,N,F,P={}){let I=Rr(v.matches,N);Di(x),Ue({errors:{[I.route.id]:F},fetchers:new Map(v.fetchers)},{flushSync:(P&&P.flushSync)===!0})}function xs(x){return le.set(x,(le.get(x)||0)+1),xe.has(x)&&xe.delete(x),v.fetchers.get(x)||Oy}function Di(x){let N=v.fetchers.get(x);Z.has(x)&&!(N&&N.state==="loading"&&$.has(x))&&Kt(x),ee.delete(x),$.delete(x),V.delete(x),xe.delete(x),M.delete(x),v.fetchers.delete(x)}function f0(x){let N=(le.get(x)||0)-1;N<=0?(le.delete(x),xe.add(x)):le.set(x,N),Ue({fetchers:new Map(v.fetchers)})}function Kt(x){let N=Z.get(x);N&&(N.abort(),Z.delete(x))}function ws(x){for(let N of x){let F=xs(N),P=Jt(F.data);v.fetchers.set(N,P)}}function ks(){let x=[],N=!1;for(let F of V){let P=v.fetchers.get(F);J(P,`Expected fetcher: ${F}`),P.state==="loading"&&(V.delete(F),x.push(F),N=!0)}return ws(x),N}function Ss(x){let N=[];for(let[F,P]of $)if(P<x){let I=v.fetchers.get(F);J(I,`Expected fetcher: ${F}`),I.state==="loading"&&(Kt(F),$.delete(F),N.push(F))}return ws(N),N.length>0}function h0(x,N){let F=v.blockers.get(x)||Hn;return $e.get(x)!==N&&$e.set(x,N),F}function Es(x){v.blockers.delete(x),$e.delete(x)}function Ml(x,N){let F=v.blockers.get(x)||Hn;J(F.state==="unblocked"&&N.state==="blocked"||F.state==="blocked"&&N.state==="blocked"||F.state==="blocked"&&N.state==="proceeding"||F.state==="blocked"&&N.state==="unblocked"||F.state==="proceeding"&&N.state==="unblocked",`Invalid blocker state transition: ${F.state} -> ${N.state}`);let P=new Map(v.blockers);P.set(x,N),Ue({blockers:P})}function Cs({currentLocation:x,nextLocation:N,historyAction:F}){if($e.size===0)return;$e.size>1&&ve(!1,"A router only supports one blocker at a time");let P=Array.from($e.entries()),[I,U]=P[P.length-1],K=v.blockers.get(I);if(!(K&&K.state==="proceeding")&&U({currentLocation:x,nextLocation:N,historyAction:F}))return I}function Fi(x){let N=st(404,{pathname:x}),F=u||i,{matches:P,route:I}=td(F);return{notFoundMatches:P,route:I,error:N}}function p0(x,N,F){if(w=x,C=N,k=F||null,!A&&v.navigation===la){A=!0;let P=Ds(v.location,v.matches);P!=null&&Ue({restoreScrollPosition:P})}return()=>{w=null,C=null,k=null}}function Ns(x,N){return k&&k(x,N.map(P=>yy(P,v.loaderData)))||x.key}function m0(x,N){if(w&&C){let F=Ns(x,N);w[F]=C()}}function Ds(x,N){if(w){let F=Ns(x,N),P=w[F];if(typeof P=="number")return P}return null}function Tl(x,N,F){if(e.patchRoutesOnNavigation)if(x){if(Object.keys(x[0].params).length>0)return{active:!0,matches:wo(N,F,a,!0)}}else return{active:!0,matches:wo(N,F,a,!0)||[]};return{active:!1,matches:null}}async function zl(x,N,F,P){if(!e.patchRoutesOnNavigation)return{type:"success",matches:x};let I=x;for(;;){let U=u==null,K=u||i,O=o;try{await e.patchRoutesOnNavigation({signal:F,path:N,matches:I,fetcherKey:P,patch:(Y,W)=>{F.aborted||Qc(Y,W,K,O,l)}})}catch(Y){return{type:"error",error:Y,partialMatches:I}}finally{U&&!F.aborted&&(i=[...i])}if(F.aborted)return{type:"aborted"};let H=tr(K,N,a);if(H)return{type:"success",matches:H};let Q=wo(K,N,a,!0);if(!Q||I.length===Q.length&&I.every((Y,W)=>Y.route.id===Q[W].route.id))return{type:"success",matches:null};I=Q}}function y0(x){o={},u=Go(x,l,void 0,o)}function g0(x,N){let F=u==null;Qc(x,N,u||i,o,l),F&&(i=[...i],Ue({}))}return B={get basename(){return a},get future(){return f},get state(){return v},get routes(){return i},get window(){return t},initialize:Wr,subscribe:l0,enableScrollRestoration:p0,navigate:ys,fetch:s0,revalidate:o0,createHref:x=>e.history.createHref(x),encodeLocation:x=>e.history.encodeLocation(x),getFetcher:xs,deleteFetcher:f0,dispose:n0,getBlocker:h0,deleteBlocker:Es,patchRoutes:g0,_internalFetchControllers:Z,_internalSetRoutes:y0},B}function Uy(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function eu(e,t,r,n,l,o){let i,u;if(l){i=[];for(let s of t)if(i.push(s),s.route.id===l){u=s;break}}else i=t,u=t[t.length-1];let a=Ei(n||".",Si(i),yt(e.pathname,r)||e.pathname,o==="path");if(n==null&&(a.search=e.search,a.hash=e.hash),(n==null||n===""||n===".")&&u){let s=us(a.search);if(u.route.index&&!s)a.search=a.search?a.search.replace(/^\?/,"?index&"):"?index";else if(!u.route.index&&s){let f=new URLSearchParams(a.search),y=f.getAll("index");f.delete("index"),y.filter(w=>w).forEach(w=>f.append("index",w));let h=f.toString();a.search=h?`?${h}`:""}}return r!=="/"&&(a.pathname=a.pathname==="/"?r:bt([r,a.pathname])),pr(a)}function Hc(e,t,r){if(!r||!Uy(r))return{path:t};if(r.formMethod&&!eg(r.formMethod))return{path:t,error:st(405,{method:r.formMethod})};let n=()=>({path:t,error:st(400,{type:"invalid-body"})}),o=(r.formMethod||"get").toUpperCase(),i=Mh(t);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!tt(o))return n();let y=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((h,[w,k])=>`${h}${w}=${k}
`,""):String(r.body);return{path:t,submission:{formMethod:o,formAction:i,formEncType:r.formEncType,formData:void 0,json:void 0,text:y}}}else if(r.formEncType==="application/json"){if(!tt(o))return n();try{let y=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:o,formAction:i,formEncType:r.formEncType,formData:void 0,json:y,text:void 0}}}catch{return n()}}}J(typeof FormData=="function","FormData is not available in this environment");let u,a;if(r.formData)u=nu(r.formData),a=r.formData;else if(r.body instanceof FormData)u=nu(r.body),a=r.body;else if(r.body instanceof URLSearchParams)u=r.body,a=Xc(u);else if(r.body==null)u=new URLSearchParams,a=new FormData;else try{u=new URLSearchParams(r.body),a=Xc(u)}catch{return n()}let s={formMethod:o,formAction:i,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:a,json:void 0,text:void 0};if(tt(s.formMethod))return{path:t,submission:s};let f=Vt(t);return e&&f.search&&us(f.search)&&u.append("index",""),f.search=`?${u}`,{path:pr(f),submission:s}}function Wc(e,t,r,n,l,o,i,u,a,s,f,y,h,w,k,C,A,m,p,c){var _e;let g=c?et(c[1])?c[1].error:c[1].data:void 0,B=l.createURL(o.location),v=l.createURL(a),R;if(f&&o.errors){let re=Object.keys(o.errors)[0];R=i.findIndex(M=>M.route.id===re)}else if(c&&et(c[1])){let re=c[0];R=i.findIndex(M=>M.route.id===re)-1}let b=c?c[1].statusCode:void 0,z=b&&b>=400,D={currentUrl:B,currentParams:((_e=o.matches[0])==null?void 0:_e.params)||{},nextUrl:v,nextParams:i[0].params,...u,actionResult:g,actionStatus:b},j=i.map((re,M)=>{let{route:Z}=re,Se=null;if(R!=null&&M>R?Se=!1:Z.lazy?Se=!0:Z.loader==null?Se=!1:f?Se=tu(Z,o.loaderData,o.errors):Vy(o.loaderData,o.matches[M],re)&&(Se=!0),Se!==null)return ru(r,n,e,re,s,t,Se);let L=z?!1:y||B.pathname+B.search===v.pathname+v.search||B.search!==v.search||Hy(o.matches[M],re),$={...D,defaultShouldRevalidate:L},V=qo(re,$);return ru(r,n,e,re,s,t,V,$)}),Fe=[];return k.forEach((re,M)=>{if(f||!i.some(xe=>xe.route.id===re.routeId)||w.has(M))return;let Z=o.fetchers.get(M),Se=Z&&Z.state!=="idle"&&Z.data===void 0,L=tr(A,re.path,m);if(!L){if(p&&Se)return;Fe.push({key:M,routeId:re.routeId,path:re.path,matches:null,match:null,request:null,controller:null});return}if(C.has(M))return;let $=qn(L,re.path),V=new AbortController,ee=Xr(l,re.path,V.signal),le=null;if(h.has(M))h.delete(M),le=vn(r,n,ee,L,$,s,t);else if(Se)y&&(le=vn(r,n,ee,L,$,s,t));else{let xe={...D,defaultShouldRevalidate:z?!1:y};qo($,xe)&&(le=vn(r,n,ee,L,$,s,t,xe))}le&&Fe.push({key:M,routeId:re.routeId,path:re.path,matches:le,match:$,request:ee,controller:V})}),{dsMatches:j,revalidatingFetchers:Fe}}function tu(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=t!=null&&e.id in t,l=r!=null&&r[e.id]!==void 0;return!n&&l?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!n&&!l}function Vy(e,t,r){let n=!t||r.route.id!==t.route.id,l=!e.hasOwnProperty(r.route.id);return n||l}function Hy(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function qo(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function Qc(e,t,r,n,l){let o;if(e){let a=n[e];J(a,`No route found to patch children into: routeId = ${e}`),a.children||(a.children=[]),o=a.children}else o=r;let i=t.filter(a=>!o.some(s=>bh(a,s))),u=Go(i,l,[e||"_","patch",String((o==null?void 0:o.length)||"0")],n);o.push(...u)}function bh(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,n)=>{var l;return(l=t.children)==null?void 0:l.some(o=>bh(r,o))}):!1}var Kc=new WeakMap,Ph=({key:e,route:t,manifest:r,mapRouteProperties:n})=>{let l=r[t.id];if(J(l,"No route found in manifest"),!l.lazy||typeof l.lazy!="object")return;let o=l.lazy[e];if(!o)return;let i=Kc.get(l);i||(i={},Kc.set(l,i));let u=i[e];if(u)return u;let a=(async()=>{let s=fy(e),y=l[e]!==void 0&&e!=="hasErrorBoundary";if(s)ve(!s,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),i[e]=Promise.resolve();else if(y)ve(!1,`Route "${l.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let h=await o();h!=null&&(Object.assign(l,{[e]:h}),Object.assign(l,n(l)))}typeof l.lazy=="object"&&(l.lazy[e]=void 0,Object.values(l.lazy).every(h=>h===void 0)&&(l.lazy=void 0))})();return i[e]=a,a},Yc=new WeakMap;function Wy(e,t,r,n,l){let o=r[e.id];if(J(o,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let f=Yc.get(o);if(f)return{lazyRoutePromise:f,lazyHandlerPromise:f};let y=(async()=>{J(typeof e.lazy=="function","No lazy route function found");let h=await e.lazy(),w={};for(let k in h){let C=h[k];if(C===void 0)continue;let A=py(k),p=o[k]!==void 0&&k!=="hasErrorBoundary";A?ve(!A,"Route property "+k+" is not a supported property to be returned from a lazy route function. This property will be ignored."):p?ve(!p,`Route "${o.id}" has a static property "${k}" defined but its lazy function is also returning a value for this property. The lazy route property "${k}" will be ignored.`):w[k]=C}Object.assign(o,w),Object.assign(o,{...n(o),lazy:void 0})})();return Yc.set(o,y),y.catch(()=>{}),{lazyRoutePromise:y,lazyHandlerPromise:y}}let i=Object.keys(e.lazy),u=[],a;for(let f of i){if(l&&l.includes(f))continue;let y=Ph({key:f,route:e,manifest:r,mapRouteProperties:n});y&&(u.push(y),f===t&&(a=y))}let s=u.length>0?Promise.all(u).then(()=>{}):void 0;return s==null||s.catch(()=>{}),a==null||a.catch(()=>{}),{lazyRoutePromise:s,lazyHandlerPromise:a}}async function Gc(e){let t=e.matches.filter(l=>l.shouldLoad),r={};return(await Promise.all(t.map(l=>l.resolve()))).forEach((l,o)=>{r[t[o].route.id]=l}),r}async function Qy(e){return e.matches.some(t=>t.route.unstable_middleware)?_h(e,!1,()=>Gc(e),(t,r)=>({[r]:{type:"error",result:t}})):Gc(e)}async function _h(e,t,r,n){let{matches:l,request:o,params:i,context:u}=e,a={handlerResult:void 0};try{let s=l.flatMap(y=>y.route.unstable_middleware?y.route.unstable_middleware.map(h=>[y.route.id,h]):[]),f=await Lh({request:o,params:i,context:u},s,t,a,r);return t?f:a.handlerResult}catch(s){if(!a.middlewareError)throw s;let f=await n(a.middlewareError.error,a.middlewareError.routeId);return t||!a.handlerResult?f:Object.assign(a.handlerResult,f)}}async function Lh(e,t,r,n,l,o=0){let{request:i}=e;if(i.signal.aborted)throw i.signal.reason?i.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${i.method} ${i.url}`);let u=t[o];if(!u)return n.handlerResult=await l(),n.handlerResult;let[a,s]=u,f=!1,y,h=async()=>{if(f)throw new Error("You may only call `next()` once per middleware");f=!0;let w=await Lh(e,t,r,n,l,o+1);if(r)return y=w,y};try{let w=await s({request:e.request,params:e.params,context:e.context},h);return f?w===void 0?y:w:h()}catch(w){throw n.middlewareError?n.middlewareError.error!==w&&(n.middlewareError={routeId:a,error:w}):n.middlewareError={routeId:a,error:w},w}}function Ih(e,t,r,n,l){let o=Ph({key:"unstable_middleware",route:n.route,manifest:t,mapRouteProperties:e}),i=Wy(n.route,tt(r.method)?"action":"loader",t,e,l);return{middleware:o,route:i.lazyRoutePromise,handler:i.lazyHandlerPromise}}function ru(e,t,r,n,l,o,i,u=null){let a=!1,s=Ih(e,t,r,n,l);return{...n,_lazyPromises:s,shouldLoad:i,unstable_shouldRevalidateArgs:u,unstable_shouldCallHandler(f){return a=!0,u?typeof f=="boolean"?qo(n,{...u,defaultShouldRevalidate:f}):qo(n,u):i},resolve(f){return a||i||f&&r.method==="GET"&&(n.route.lazy||n.route.loader)?Yy({request:r,match:n,lazyHandlerPromise:s==null?void 0:s.handler,lazyRoutePromise:s==null?void 0:s.route,handlerOverride:f,scopedContext:o}):Promise.resolve({type:"data",result:void 0})}}}function vn(e,t,r,n,l,o,i,u=null){return n.map(a=>a.route.id!==l.route.id?{...a,shouldLoad:!1,unstable_shouldRevalidateArgs:u,unstable_shouldCallHandler:()=>!1,_lazyPromises:Ih(e,t,r,a,o),resolve:()=>Promise.resolve({type:"data",result:void 0})}:ru(e,t,r,a,o,i,!0,u))}async function Ky(e,t,r,n,l,o){r.some(s=>{var f;return(f=s._lazyPromises)==null?void 0:f.middleware})&&await Promise.all(r.map(s=>{var f;return(f=s._lazyPromises)==null?void 0:f.middleware}));let i={request:t,params:r[0].params,context:l,matches:r},a=await e({...i,fetcherKey:n,unstable_runClientMiddleware:o?()=>{throw new Error("You cannot call `unstable_runClientMiddleware()` from a static handler `dataStrategy`. Middleware is run outside of `dataStrategy` during SSR in order to bubble up the Response.  You can enable middleware via the `respond` API in `query`/`queryRoute`")}:s=>{let f=i;return _h(f,!1,()=>s({...f,fetcherKey:n,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(y,h)=>({[h]:{type:"error",result:y}}))}});try{await Promise.all(r.flatMap(s=>{var f,y;return[(f=s._lazyPromises)==null?void 0:f.handler,(y=s._lazyPromises)==null?void 0:y.route]}))}catch{}return a}async function Yy({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:n,handlerOverride:l,scopedContext:o}){let i,u,a=tt(e.method),s=a?"action":"loader",f=y=>{let h,w=new Promise((A,m)=>h=m);u=()=>h(),e.signal.addEventListener("abort",u);let k=A=>typeof y!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${s}" [routeId: ${t.route.id}]`)):y({request:e,params:t.params,context:o},...A!==void 0?[A]:[]),C=(async()=>{try{return{type:"data",result:await(l?l(m=>k(m)):k())}}catch(A){return{type:"error",result:A}}})();return Promise.race([C,w])};try{let y=a?t.route.action:t.route.loader;if(r||n)if(y){let h,[w]=await Promise.all([f(y).catch(k=>{h=k}),r,n]);if(h!==void 0)throw h;i=w}else{await r;let h=a?t.route.action:t.route.loader;if(h)[i]=await Promise.all([f(h),n]);else if(s==="action"){let w=new URL(e.url),k=w.pathname+w.search;throw st(405,{method:e.method,pathname:k,routeId:t.route.id})}else return{type:"data",result:void 0}}else if(y)i=await f(y);else{let h=new URL(e.url),w=h.pathname+h.search;throw st(404,{pathname:w})}}catch(y){return{type:"error",result:y}}finally{u&&e.signal.removeEventListener("abort",u)}return i}async function Gy(e){var n,l,o,i,u,a;let{result:t,type:r}=e;if(Th(t)){let s;try{let f=t.headers.get("Content-Type");f&&/\bapplication\/json\b/.test(f)?t.body==null?s=null:s=await t.json():s=await t.text()}catch(f){return{type:"error",error:f}}return r==="error"?{type:"error",error:new Xo(t.status,t.statusText,s),statusCode:t.status,headers:t.headers}:{type:"data",data:s,statusCode:t.status,headers:t.headers}}return r==="error"?rd(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:(n=t.init)==null?void 0:n.status,headers:(l=t.init)!=null&&l.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new Xo(((o=t.init)==null?void 0:o.status)||500,void 0,t.data),statusCode:Dl(t)?t.status:void 0,headers:(i=t.init)!=null&&i.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:Dl(t)?t.status:void 0}:rd(t)?{type:"data",data:t.data,statusCode:(u=t.init)==null?void 0:u.status,headers:(a=t.init)!=null&&a.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function Jy(e,t,r,n,l){let o=e.headers.get("Location");if(J(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!as.test(o)){let i=n.slice(0,n.findIndex(u=>u.route.id===r)+1);o=eu(new URL(t.url),i,l,o),e.headers.set("Location",o)}return e}function Jc(e,t,r){if(as.test(e)){let n=e,l=n.startsWith("//")?new URL(t.protocol+n):new URL(n),o=yt(l.pathname,r)!=null;if(l.origin===t.origin&&o)return l.pathname+l.search+l.hash}return e}function Xr(e,t,r,n){let l=e.createURL(Mh(t)).toString(),o={signal:r};if(n&&tt(n.formMethod)){let{formMethod:i,formEncType:u}=n;o.method=i.toUpperCase(),u==="application/json"?(o.headers=new Headers({"Content-Type":u}),o.body=JSON.stringify(n.json)):u==="text/plain"?o.body=n.text:u==="application/x-www-form-urlencoded"&&n.formData?o.body=nu(n.formData):o.body=n.formData}return new Request(l,o)}function nu(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function Xc(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function Xy(e,t,r,n=!1,l=!1){let o={},i=null,u,a=!1,s={},f=r&&et(r[1])?r[1].error:void 0;return e.forEach(y=>{if(!(y.route.id in t))return;let h=y.route.id,w=t[h];if(J(!_r(w),"Cannot handle redirect results in processLoaderData"),et(w)){let k=w.error;if(f!==void 0&&(k=f,f=void 0),i=i||{},l)i[h]=k;else{let C=Rr(e,h);i[C.route.id]==null&&(i[C.route.id]=k)}n||(o[h]=Ah),a||(a=!0,u=Dl(w.error)?w.error.status:500),w.headers&&(s[h]=w.headers)}else o[h]=w.data,w.statusCode&&w.statusCode!==200&&!a&&(u=w.statusCode),w.headers&&(s[h]=w.headers)}),f!==void 0&&r&&(i={[r[0]]:f},r[2]&&(o[r[2]]=void 0)),{loaderData:o,errors:i,statusCode:u||200,loaderHeaders:s}}function qc(e,t,r,n,l,o){let{loaderData:i,errors:u}=Xy(t,r,n);return l.filter(a=>!a.matches||a.matches.some(s=>s.shouldLoad)).forEach(a=>{let{key:s,match:f,controller:y}=a,h=o[s];if(J(h,"Did not find corresponding fetcher result"),!(y&&y.signal.aborted))if(et(h)){let w=Rr(e.matches,f==null?void 0:f.route.id);u&&u[w.route.id]||(u={...u,[w.route.id]:h.error}),e.fetchers.delete(s)}else if(_r(h))J(!1,"Unhandled fetcher revalidation redirect");else{let w=Jt(h.data);e.fetchers.set(s,w)}}),{loaderData:i,errors:u}}function Zc(e,t,r,n){let l=Object.entries(t).filter(([,o])=>o!==Ah).reduce((o,[i,u])=>(o[i]=u,o),{});for(let o of r){let i=o.route.id;if(!t.hasOwnProperty(i)&&e.hasOwnProperty(i)&&o.route.loader&&(l[i]=e[i]),n&&n.hasOwnProperty(i))break}return l}function ed(e){return e?et(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function Rr(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function td(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function st(e,{pathname:t,routeId:r,method:n,type:l,message:o}={}){let i="Unknown Server Error",u="Unknown @remix-run/router error";return e===400?(i="Bad Request",n&&t&&r?u=`You made a ${n} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:l==="invalid-body"&&(u="Unable to encode submission body")):e===403?(i="Forbidden",u=`Route "${r}" does not match URL "${t}"`):e===404?(i="Not Found",u=`No route matches URL "${t}"`):e===405&&(i="Method Not Allowed",n&&t&&r?u=`You made a ${n.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:n&&(u=`Invalid request method "${n.toUpperCase()}"`)),new Xo(e||500,i,new Error(u),!0)}function lo(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[n,l]=t[r];if(_r(l))return{key:n,result:l}}}function Mh(e){let t=typeof e=="string"?Vt(e):e;return pr({...t,hash:""})}function qy(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function Zy(e){return Th(e.result)&&Ty.has(e.result.status)}function et(e){return e.type==="error"}function _r(e){return(e&&e.type)==="redirect"}function rd(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function Th(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function eg(e){return My.has(e.toUpperCase())}function tt(e){return Ly.has(e.toUpperCase())}function us(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function qn(e,t){let r=typeof t=="string"?Vt(t).search:t.search;if(e[e.length-1].route.index&&us(r||""))return e[e.length-1];let n=Fh(e);return n[n.length-1]}function nd(e){let{formMethod:t,formAction:r,formEncType:n,text:l,formData:o,json:i}=e;if(!(!t||!r||!n)){if(l!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:l};if(o!=null)return{formMethod:t,formAction:r,formEncType:n,formData:o,json:void 0,text:void 0};if(i!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:i,text:void 0}}}function oa(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function tg(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function Wn(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function rg(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Jt(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function ng(e,t){try{let r=e.sessionStorage.getItem(Rh);if(r){let n=JSON.parse(r);for(let[l,o]of Object.entries(n||{}))o&&Array.isArray(o)&&t.set(l,new Set(o||[]))}}catch{}}function lg(e,t){if(t.size>0){let r={};for(let[n,l]of t)r[n]=[...l];try{e.sessionStorage.setItem(Rh,JSON.stringify(r))}catch(n){ve(!1,`Failed to save applied view transitions in sessionStorage (${n}).`)}}}function og(){let e,t,r=new Promise((n,l)=>{e=async o=>{n(o);try{await r}catch{}},t=async o=>{l(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var Vr=E.exports.createContext(null);Vr.displayName="DataRouter";var _l=E.exports.createContext(null);_l.displayName="DataRouterState";var ss=E.exports.createContext({isTransitioning:!1});ss.displayName="ViewTransition";var zh=E.exports.createContext(new Map);zh.displayName="Fetchers";var ig=E.exports.createContext(null);ig.displayName="Await";var Ct=E.exports.createContext(null);Ct.displayName="Navigation";var Ll=E.exports.createContext(null);Ll.displayName="Location";var Nt=E.exports.createContext({outlet:null,matches:[],isDataRoute:!1});Nt.displayName="Route";var cs=E.exports.createContext(null);cs.displayName="RouteError";function ag(e,{relative:t}={}){J(An(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:n}=E.exports.useContext(Ct),{hash:l,pathname:o,search:i}=Il(e,{relative:t}),u=o;return r!=="/"&&(u=o==="/"?r:bt([r,o])),n.createHref({pathname:u,search:i,hash:l})}function An(){return E.exports.useContext(Ll)!=null}function vr(){return J(An(),"useLocation() may be used only in the context of a <Router> component."),E.exports.useContext(Ll).location}var Oh="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function $h(e){E.exports.useContext(Ct).static||E.exports.useLayoutEffect(e)}function jh(){let{isDataRoute:e}=E.exports.useContext(Nt);return e?Sg():ug()}function ug(){J(An(),"useNavigate() may be used only in the context of a <Router> component.");let e=E.exports.useContext(Vr),{basename:t,navigator:r}=E.exports.useContext(Ct),{matches:n}=E.exports.useContext(Nt),{pathname:l}=vr(),o=JSON.stringify(Si(n)),i=E.exports.useRef(!1);return $h(()=>{i.current=!0}),E.exports.useCallback((a,s={})=>{if(ve(i.current,Oh),!i.current)return;if(typeof a=="number"){r.go(a);return}let f=Ei(a,JSON.parse(o),l,s.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:bt([t,f.pathname])),(s.replace?r.replace:r.push)(f,s.state,s)},[t,r,o,l,e])}var sg=E.exports.createContext(null);function cg(e){let t=E.exports.useContext(Nt).outlet;return t&&E.exports.createElement(sg.Provider,{value:e},t)}function Il(e,{relative:t}={}){let{matches:r}=E.exports.useContext(Nt),{pathname:n}=vr(),l=JSON.stringify(Si(r));return E.exports.useMemo(()=>Ei(e,JSON.parse(l),n,t==="path"),[e,l,n,t])}function dg(e,t,r,n){var m;J(An(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:l}=E.exports.useContext(Ct),{matches:o}=E.exports.useContext(Nt),i=o[o.length-1],u=i?i.params:{},a=i?i.pathname:"/",s=i?i.pathnameBase:"/",f=i&&i.route;{let p=f&&f.path||"";Uh(a,!f||p.endsWith("*")||p.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${a}" (under <Route path="${p}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${p}"> to <Route path="${p==="/"?"*":`${p}/*`}">.`)}let y=vr(),h;if(t){let p=typeof t=="string"?Vt(t):t;J(s==="/"||((m=p.pathname)==null?void 0:m.startsWith(s)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${s}" but pathname "${p.pathname}" was given in the \`location\` prop.`),h=p}else h=y;let w=h.pathname||"/",k=w;if(s!=="/"){let p=s.replace(/^\//,"").split("/");k="/"+w.replace(/^\//,"").split("/").slice(p.length).join("/")}let C=tr(e,{pathname:k});ve(f||C!=null,`No routes matched location "${h.pathname}${h.search}${h.hash}" `),ve(C==null||C[C.length-1].route.element!==void 0||C[C.length-1].route.Component!==void 0||C[C.length-1].route.lazy!==void 0,`Matched leaf route at location "${h.pathname}${h.search}${h.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let A=yg(C&&C.map(p=>Object.assign({},p,{params:Object.assign({},u,p.params),pathname:bt([s,l.encodeLocation?l.encodeLocation(p.pathname).pathname:p.pathname]),pathnameBase:p.pathnameBase==="/"?s:bt([s,l.encodeLocation?l.encodeLocation(p.pathnameBase).pathname:p.pathnameBase])})),o,r,n);return t&&A?E.exports.createElement(Ll.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...h},navigationType:"POP"}},A):A}function fg(){let e=kg(),t=Dl(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",l={padding:"0.5rem",backgroundColor:n},o={padding:"2px 4px",backgroundColor:n},i=null;return console.error("Error handled by React Router default ErrorBoundary:",e),i=E.exports.createElement(E.exports.Fragment,null,E.exports.createElement("p",null,"\u{1F4BF} Hey developer \u{1F44B}"),E.exports.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",E.exports.createElement("code",{style:o},"ErrorBoundary")," or"," ",E.exports.createElement("code",{style:o},"errorElement")," prop on your route.")),E.exports.createElement(E.exports.Fragment,null,E.exports.createElement("h2",null,"Unexpected Application Error!"),E.exports.createElement("h3",{style:{fontStyle:"italic"}},t),r?E.exports.createElement("pre",{style:l},r):null,i)}var hg=E.exports.createElement(fg,null),pg=class extends E.exports.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?E.exports.createElement(Nt.Provider,{value:this.props.routeContext},E.exports.createElement(cs.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function mg({routeContext:e,match:t,children:r}){let n=E.exports.useContext(Vr);return n&&n.static&&n.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=t.route.id),E.exports.createElement(Nt.Provider,{value:e},r)}function yg(e,t=[],r=null,n=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let l=e,o=r==null?void 0:r.errors;if(o!=null){let a=l.findIndex(s=>s.route.id&&(o==null?void 0:o[s.route.id])!==void 0);J(a>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),l=l.slice(0,Math.min(l.length,a+1))}let i=!1,u=-1;if(r)for(let a=0;a<l.length;a++){let s=l[a];if((s.route.HydrateFallback||s.route.hydrateFallbackElement)&&(u=a),s.route.id){let{loaderData:f,errors:y}=r,h=s.route.loader&&!f.hasOwnProperty(s.route.id)&&(!y||y[s.route.id]===void 0);if(s.route.lazy||h){i=!0,u>=0?l=l.slice(0,u+1):l=[l[0]];break}}}return l.reduceRight((a,s,f)=>{let y,h=!1,w=null,k=null;r&&(y=o&&s.route.id?o[s.route.id]:void 0,w=s.route.errorElement||hg,i&&(u<0&&f===0?(Uh("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),h=!0,k=null):u===f&&(h=!0,k=s.route.hydrateFallbackElement||null)));let C=t.concat(l.slice(0,f+1)),A=()=>{let m;return y?m=w:h?m=k:s.route.Component?m=E.exports.createElement(s.route.Component,null):s.route.element?m=s.route.element:m=a,E.exports.createElement(mg,{match:s,routeContext:{outlet:a,matches:C,isDataRoute:r!=null},children:m})};return r&&(s.route.ErrorBoundary||s.route.errorElement||f===0)?E.exports.createElement(pg,{location:r.location,revalidation:r.revalidation,component:w,error:y,children:A(),routeContext:{outlet:null,matches:C,isDataRoute:!0}}):A()},null)}function ds(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function gg(e){let t=E.exports.useContext(Vr);return J(t,ds(e)),t}function vg(e){let t=E.exports.useContext(_l);return J(t,ds(e)),t}function xg(e){let t=E.exports.useContext(Nt);return J(t,ds(e)),t}function fs(e){let t=xg(e),r=t.matches[t.matches.length-1];return J(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function wg(){return fs("useRouteId")}function kg(){var n;let e=E.exports.useContext(cs),t=vg("useRouteError"),r=fs("useRouteError");return e!==void 0?e:(n=t.errors)==null?void 0:n[r]}function Sg(){let{router:e}=gg("useNavigate"),t=fs("useNavigate"),r=E.exports.useRef(!1);return $h(()=>{r.current=!0}),E.exports.useCallback(async(l,o={})=>{ve(r.current,Oh),r.current&&(typeof l=="number"?e.navigate(l):await e.navigate(l,{fromRouteId:t,...o}))},[e,t])}var ld={};function Uh(e,t,r){!t&&!ld[e]&&(ld[e]=!0,ve(!1,r))}var od={};function id(e,t){!e&&!od[t]&&(od[t]=!0,console.warn(t))}function Eg(e){let t={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&ve(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:E.exports.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&ve(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:E.exports.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&ve(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:E.exports.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var Cg=["HydrateFallback","hydrateFallbackElement"],Ng=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",e(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",t(r))}})}};function Dg({router:e,flushSync:t}){let[r,n]=E.exports.useState(e.state),[l,o]=E.exports.useState(),[i,u]=E.exports.useState({isTransitioning:!1}),[a,s]=E.exports.useState(),[f,y]=E.exports.useState(),[h,w]=E.exports.useState(),k=E.exports.useRef(new Map),C=E.exports.useCallback((c,{deletedFetchers:g,flushSync:B,viewTransitionOpts:v})=>{c.fetchers.forEach((b,z)=>{b.data!==void 0&&k.current.set(z,b.data)}),g.forEach(b=>k.current.delete(b)),id(B===!1||t!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let R=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(id(v==null||R,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!v||!R){t&&B?t(()=>n(c)):E.exports.startTransition(()=>n(c));return}if(t&&B){t(()=>{f&&(a&&a.resolve(),f.skipTransition()),u({isTransitioning:!0,flushSync:!0,currentLocation:v.currentLocation,nextLocation:v.nextLocation})});let b=e.window.document.startViewTransition(()=>{t(()=>n(c))});b.finished.finally(()=>{t(()=>{s(void 0),y(void 0),o(void 0),u({isTransitioning:!1})})}),t(()=>y(b));return}f?(a&&a.resolve(),f.skipTransition(),w({state:c,currentLocation:v.currentLocation,nextLocation:v.nextLocation})):(o(c),u({isTransitioning:!0,flushSync:!1,currentLocation:v.currentLocation,nextLocation:v.nextLocation}))},[e.window,t,f,a]);E.exports.useLayoutEffect(()=>e.subscribe(C),[e,C]),E.exports.useEffect(()=>{i.isTransitioning&&!i.flushSync&&s(new Ng)},[i]),E.exports.useEffect(()=>{if(a&&l&&e.window){let c=l,g=a.promise,B=e.window.document.startViewTransition(async()=>{E.exports.startTransition(()=>n(c)),await g});B.finished.finally(()=>{s(void 0),y(void 0),o(void 0),u({isTransitioning:!1})}),y(B)}},[l,a,e.window]),E.exports.useEffect(()=>{a&&l&&r.location.key===l.location.key&&a.resolve()},[a,f,r.location,l]),E.exports.useEffect(()=>{!i.isTransitioning&&h&&(o(h.state),u({isTransitioning:!0,flushSync:!1,currentLocation:h.currentLocation,nextLocation:h.nextLocation}),w(void 0))},[i.isTransitioning,h]);let A=E.exports.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:c=>e.navigate(c),push:(c,g,B)=>e.navigate(c,{state:g,preventScrollReset:B==null?void 0:B.preventScrollReset}),replace:(c,g,B)=>e.navigate(c,{replace:!0,state:g,preventScrollReset:B==null?void 0:B.preventScrollReset})}),[e]),m=e.basename||"/",p=E.exports.useMemo(()=>({router:e,navigator:A,static:!1,basename:m}),[e,A,m]);return d(is,{children:d(Vr.Provider,{value:p,children:d(_l.Provider,{value:r,children:d(zh.Provider,{value:k.current,children:d(ss.Provider,{value:i,children:d(bg,{basename:m,location:r.location,navigationType:r.historyAction,navigator:A,children:d(Fg,{routes:e.routes,future:e.future,state:r})})})})})})})}var Fg=E.exports.memo(Bg);function Bg({routes:e,future:t,state:r}){return dg(e,void 0,r,t)}function Rg({to:e,replace:t,state:r,relative:n}){J(An(),"<Navigate> may be used only in the context of a <Router> component.");let{static:l}=E.exports.useContext(Ct);ve(!l,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:o}=E.exports.useContext(Nt),{pathname:i}=vr(),u=jh(),a=Ei(e,Si(o),i,n==="path"),s=JSON.stringify(a);return E.exports.useEffect(()=>{u(JSON.parse(s),{replace:t,state:r,relative:n})},[u,s,n,t,r]),null}function Ag(e){return cg(e.context)}function bg({basename:e="/",children:t=null,location:r,navigationType:n="POP",navigator:l,static:o=!1}){J(!An(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let i=e.replace(/^\/*/,"/"),u=E.exports.useMemo(()=>({basename:i,navigator:l,static:o,future:{}}),[i,l,o]);typeof r=="string"&&(r=Vt(r));let{pathname:a="/",search:s="",hash:f="",state:y=null,key:h="default"}=r,w=E.exports.useMemo(()=>{let k=yt(a,i);return k==null?null:{location:{pathname:k,search:s,hash:f,state:y,key:h},navigationType:n}},[i,a,s,f,y,h,n]);return ve(w!=null,`<Router basename="${i}"> is not able to match the URL "${a}${s}${f}" because it does not start with the basename, so the <Router> won't render anything.`),w==null?null:d(Ct.Provider,{value:u,children:d(Ll.Provider,{children:t,value:w})})}var ko="get",So="application/x-www-form-urlencoded";function Ci(e){return e!=null&&typeof e.tagName=="string"}function Pg(e){return Ci(e)&&e.tagName.toLowerCase()==="button"}function _g(e){return Ci(e)&&e.tagName.toLowerCase()==="form"}function Lg(e){return Ci(e)&&e.tagName.toLowerCase()==="input"}function Ig(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Mg(e,t){return e.button===0&&(!t||t==="_self")&&!Ig(e)}var oo=null;function Tg(){if(oo===null)try{new FormData(document.createElement("form"),0),oo=!1}catch{oo=!0}return oo}var zg=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function ia(e){return e!=null&&!zg.has(e)?(ve(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${So}"`),null):e}function Og(e,t){let r,n,l,o,i;if(_g(e)){let u=e.getAttribute("action");n=u?yt(u,t):null,r=e.getAttribute("method")||ko,l=ia(e.getAttribute("enctype"))||So,o=new FormData(e)}else if(Pg(e)||Lg(e)&&(e.type==="submit"||e.type==="image")){let u=e.form;if(u==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let a=e.getAttribute("formaction")||u.getAttribute("action");if(n=a?yt(a,t):null,r=e.getAttribute("formmethod")||u.getAttribute("method")||ko,l=ia(e.getAttribute("formenctype"))||ia(u.getAttribute("enctype"))||So,o=new FormData(u,e),!Tg()){let{name:s,type:f,value:y}=e;if(f==="image"){let h=s?`${s}.`:"";o.append(`${h}x`,"0"),o.append(`${h}y`,"0")}else s&&o.append(s,y)}}else{if(Ci(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=ko,n=null,l=So,i=e}return o&&l==="text/plain"&&(i=o,o=void 0),{action:n,method:r.toLowerCase(),encType:l,formData:o,body:i}}function hs(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function $g(e,t){if(e.id in t)return t[e.id];try{let r=await Vm(()=>import(e.module),[]);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function jg(e){return e!=null&&typeof e.page=="string"}function Ug(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Vg(e,t,r){let n=await Promise.all(e.map(async l=>{let o=t.routes[l.route.id];if(o){let i=await $g(o,r);return i.links?i.links():[]}return[]}));return Kg(n.flat(1).filter(Ug).filter(l=>l.rel==="stylesheet"||l.rel==="preload").map(l=>l.rel==="stylesheet"?{...l,rel:"prefetch",as:"style"}:{...l,rel:"prefetch"}))}function ad(e,t,r,n,l,o){let i=(a,s)=>r[s]?a.route.id!==r[s].route.id:!0,u=(a,s)=>{var f;return r[s].pathname!==a.pathname||((f=r[s].route.path)==null?void 0:f.endsWith("*"))&&r[s].params["*"]!==a.params["*"]};return o==="assets"?t.filter((a,s)=>i(a,s)||u(a,s)):o==="data"?t.filter((a,s)=>{var y;let f=n.routes[a.route.id];if(!f||!f.hasLoader)return!1;if(i(a,s)||u(a,s))return!0;if(a.route.shouldRevalidate){let h=a.route.shouldRevalidate({currentUrl:new URL(l.pathname+l.search+l.hash,window.origin),currentParams:((y=r[0])==null?void 0:y.params)||{},nextUrl:new URL(e,window.origin),nextParams:a.params,defaultShouldRevalidate:!0});if(typeof h=="boolean")return h}return!0}):[]}function Hg(e,t,{includeHydrateFallback:r}={}){return Wg(e.map(n=>{let l=t.routes[n.route.id];if(!l)return[];let o=[l.module];return l.clientActionModule&&(o=o.concat(l.clientActionModule)),l.clientLoaderModule&&(o=o.concat(l.clientLoaderModule)),r&&l.hydrateFallbackModule&&(o=o.concat(l.hydrateFallbackModule)),l.imports&&(o=o.concat(l.imports)),o}).flat(1))}function Wg(e){return[...new Set(e)]}function Qg(e){let t={},r=Object.keys(e).sort();for(let n of r)t[n]=e[n];return t}function Kg(e,t){let r=new Set,n=new Set(t);return e.reduce((l,o)=>{if(t&&!jg(o)&&o.as==="script"&&o.href&&n.has(o.href))return l;let u=JSON.stringify(Qg(o));return r.has(u)||(r.add(u),l.push({key:u,link:o})),l},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Yg=new Set([100,101,204,205]);function Gg(e,t){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname="_root.data":t&&yt(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function Vh(){let e=E.exports.useContext(Vr);return hs(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Jg(){let e=E.exports.useContext(_l);return hs(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var ps=E.exports.createContext(void 0);ps.displayName="FrameworkContext";function Hh(){let e=E.exports.useContext(ps);return hs(e,"You must render this element inside a <HydratedRouter> element"),e}function Xg(e,t){let r=E.exports.useContext(ps),[n,l]=E.exports.useState(!1),[o,i]=E.exports.useState(!1),{onFocus:u,onBlur:a,onMouseEnter:s,onMouseLeave:f,onTouchStart:y}=t,h=E.exports.useRef(null);E.exports.useEffect(()=>{if(e==="render"&&i(!0),e==="viewport"){let C=m=>{m.forEach(p=>{i(p.isIntersecting)})},A=new IntersectionObserver(C,{threshold:.5});return h.current&&A.observe(h.current),()=>{A.disconnect()}}},[e]),E.exports.useEffect(()=>{if(n){let C=setTimeout(()=>{i(!0)},100);return()=>{clearTimeout(C)}}},[n]);let w=()=>{l(!0)},k=()=>{l(!1),i(!1)};return r?e!=="intent"?[o,h,{}]:[o,h,{onFocus:Qn(u,w),onBlur:Qn(a,k),onMouseEnter:Qn(s,w),onMouseLeave:Qn(f,k),onTouchStart:Qn(y,w)}]:[!1,h,{}]}function Qn(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function qg({page:e,...t}){let{router:r}=Vh(),n=E.exports.useMemo(()=>tr(r.routes,e,r.basename),[r.routes,e,r.basename]);return n?E.exports.createElement(ev,{page:e,matches:n,...t}):null}function Zg(e){let{manifest:t,routeModules:r}=Hh(),[n,l]=E.exports.useState([]);return E.exports.useEffect(()=>{let o=!1;return Vg(e,t,r).then(i=>{o||l(i)}),()=>{o=!0}},[e,t,r]),n}function ev({page:e,matches:t,...r}){let n=vr(),{manifest:l,routeModules:o}=Hh(),{basename:i}=Vh(),{loaderData:u,matches:a}=Jg(),s=E.exports.useMemo(()=>ad(e,t,a,l,n,"data"),[e,t,a,l,n]),f=E.exports.useMemo(()=>ad(e,t,a,l,n,"assets"),[e,t,a,l,n]),y=E.exports.useMemo(()=>{if(e===n.pathname+n.search+n.hash)return[];let k=new Set,C=!1;if(t.forEach(m=>{var c;let p=l.routes[m.route.id];!p||!p.hasLoader||(!s.some(g=>g.route.id===m.route.id)&&m.route.id in u&&((c=o[m.route.id])==null?void 0:c.shouldRevalidate)||p.hasClientLoader?C=!0:k.add(m.route.id))}),k.size===0)return[];let A=Gg(e,i);return C&&k.size>0&&A.searchParams.set("_routes",t.filter(m=>k.has(m.route.id)).map(m=>m.route.id).join(",")),[A.pathname+A.search]},[i,u,n,l,s,t,e,o]),h=E.exports.useMemo(()=>Hg(f,l),[f,l]),w=Zg(f);return E.exports.createElement(E.exports.Fragment,null,y.map(k=>E.exports.createElement("link",{key:k,rel:"prefetch",as:"fetch",href:k,...r})),h.map(k=>E.exports.createElement("link",{key:k,rel:"modulepreload",href:k,...r})),w.map(({key:k,link:C})=>E.exports.createElement("link",{key:k,...C})))}function tv(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var Wh=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Wh&&(window.__reactRouterVersion="7.6.3")}catch{}function rv(e,t){return jy({basename:t==null?void 0:t.basename,unstable_getContext:t==null?void 0:t.unstable_getContext,future:t==null?void 0:t.future,history:uy({window:t==null?void 0:t.window}),hydrationData:(t==null?void 0:t.hydrationData)||nv(),routes:e,mapRouteProperties:Eg,hydrationRouteProperties:Cg,dataStrategy:t==null?void 0:t.dataStrategy,patchRoutesOnNavigation:t==null?void 0:t.patchRoutesOnNavigation,window:t==null?void 0:t.window}).initialize()}function nv(){let e=window==null?void 0:window.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:lv(e.errors)}),e}function lv(e){if(!e)return null;let t=Object.entries(e),r={};for(let[n,l]of t)if(l&&l.__type==="RouteErrorResponse")r[n]=new Xo(l.status,l.statusText,l.data,l.internal===!0);else if(l&&l.__type==="Error"){if(l.__subType){let o=window[l.__subType];if(typeof o=="function")try{let i=new o(l.message);i.stack="",r[n]=i}catch{}}if(r[n]==null){let o=new Error(l.message);o.stack="",r[n]=o}}else r[n]=l;return r}var Qh=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Kh=E.exports.forwardRef(function({onClick:t,discover:r="render",prefetch:n="none",relative:l,reloadDocument:o,replace:i,state:u,target:a,to:s,preventScrollReset:f,viewTransition:y,...h},w){let{basename:k}=E.exports.useContext(Ct),C=typeof s=="string"&&Qh.test(s),A,m=!1;if(typeof s=="string"&&C&&(A=s,Wh))try{let z=new URL(window.location.href),D=s.startsWith("//")?new URL(z.protocol+s):new URL(s),j=yt(D.pathname,k);D.origin===z.origin&&j!=null?s=j+D.search+D.hash:m=!0}catch{ve(!1,`<Link to="${s}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let p=ag(s,{relative:l}),[c,g,B]=Xg(n,h),v=av(s,{replace:i,state:u,target:a,preventScrollReset:f,relative:l,viewTransition:y});function R(z){t&&t(z),z.defaultPrevented||v(z)}let b=E.exports.createElement("a",{...h,...B,href:A||p,onClick:m||o?t:R,ref:tv(w,g),target:a,"data-discover":!C&&r==="render"?"true":void 0});return c&&!C?E.exports.createElement(E.exports.Fragment,null,b,E.exports.createElement(qg,{page:p})):b});Kh.displayName="Link";var Yh=E.exports.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:n="",end:l=!1,style:o,to:i,viewTransition:u,children:a,...s},f){let y=Il(i,{relative:s.relative}),h=vr(),w=E.exports.useContext(_l),{navigator:k,basename:C}=E.exports.useContext(Ct),A=w!=null&&fv(y)&&u===!0,m=k.encodeLocation?k.encodeLocation(y).pathname:y.pathname,p=h.pathname,c=w&&w.navigation&&w.navigation.location?w.navigation.location.pathname:null;r||(p=p.toLowerCase(),c=c?c.toLowerCase():null,m=m.toLowerCase()),c&&C&&(c=yt(c,C)||c);const g=m!=="/"&&m.endsWith("/")?m.length-1:m.length;let B=p===m||!l&&p.startsWith(m)&&p.charAt(g)==="/",v=c!=null&&(c===m||!l&&c.startsWith(m)&&c.charAt(m.length)==="/"),R={isActive:B,isPending:v,isTransitioning:A},b=B?t:void 0,z;typeof n=="function"?z=n(R):z=[n,B?"active":null,v?"pending":null,A?"transitioning":null].filter(Boolean).join(" ");let D=typeof o=="function"?o(R):o;return E.exports.createElement(Kh,{...s,"aria-current":b,className:z,ref:f,style:D,to:i,viewTransition:u},typeof a=="function"?a(R):a)});Yh.displayName="NavLink";var ov=E.exports.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:n,replace:l,state:o,method:i=ko,action:u,onSubmit:a,relative:s,preventScrollReset:f,viewTransition:y,...h},w)=>{let k=cv(),C=dv(u,{relative:s}),A=i.toLowerCase()==="get"?"get":"post",m=typeof u=="string"&&Qh.test(u),p=c=>{if(a&&a(c),c.defaultPrevented)return;c.preventDefault();let g=c.nativeEvent.submitter,B=(g==null?void 0:g.getAttribute("formmethod"))||i;k(g||c.currentTarget,{fetcherKey:t,method:B,navigate:r,replace:l,state:o,relative:s,preventScrollReset:f,viewTransition:y})};return E.exports.createElement("form",{ref:w,method:A,action:C,onSubmit:n?a:p,...h,"data-discover":!m&&e==="render"?"true":void 0})});ov.displayName="Form";function iv(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Gh(e){let t=E.exports.useContext(Vr);return J(t,iv(e)),t}function av(e,{target:t,replace:r,state:n,preventScrollReset:l,relative:o,viewTransition:i}={}){let u=jh(),a=vr(),s=Il(e,{relative:o});return E.exports.useCallback(f=>{if(Mg(f,t)){f.preventDefault();let y=r!==void 0?r:pr(a)===pr(s);u(e,{replace:y,state:n,preventScrollReset:l,relative:o,viewTransition:i})}},[a,u,s,r,n,t,e,l,o,i])}var uv=0,sv=()=>`__${String(++uv)}__`;function cv(){let{router:e}=Gh("useSubmit"),{basename:t}=E.exports.useContext(Ct),r=wg();return E.exports.useCallback(async(n,l={})=>{let{action:o,method:i,encType:u,formData:a,body:s}=Og(n,t);if(l.navigate===!1){let f=l.fetcherKey||sv();await e.fetch(f,r,l.action||o,{preventScrollReset:l.preventScrollReset,formData:a,body:s,formMethod:l.method||i,formEncType:l.encType||u,flushSync:l.flushSync})}else await e.navigate(l.action||o,{preventScrollReset:l.preventScrollReset,formData:a,body:s,formMethod:l.method||i,formEncType:l.encType||u,replace:l.replace,state:l.state,fromRouteId:r,flushSync:l.flushSync,viewTransition:l.viewTransition})},[e,t,r])}function dv(e,{relative:t}={}){let{basename:r}=E.exports.useContext(Ct),n=E.exports.useContext(Nt);J(n,"useFormAction must be used inside a RouteContext");let[l]=n.matches.slice(-1),o={...Il(e||".",{relative:t})},i=vr();if(e==null){o.search=i.search;let u=new URLSearchParams(o.search),a=u.getAll("index");if(a.some(f=>f==="")){u.delete("index"),a.filter(y=>y).forEach(y=>u.append("index",y));let f=u.toString();o.search=f?`?${f}`:""}}return(!e||e===".")&&l.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(o.pathname=o.pathname==="/"?r:bt([r,o.pathname])),pr(o)}function fv(e,t={}){let r=E.exports.useContext(ss);J(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:n}=Gh("useViewTransitionState"),l=Il(e,{relative:t.relative});if(!r.isTransitioning)return!1;let o=yt(r.currentLocation.pathname,n)||r.currentLocation.pathname,i=yt(r.nextLocation.pathname,n)||r.nextLocation.pathname;return Jo(l.pathname,i)!=null||Jo(l.pathname,o)!=null}[...Yg];/**
 * react-router v7.6.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function hv(e){return d(Dg,{flushSync:pu.exports.flushSync,...e})}function Jh(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var l=e.length;for(t=0;t<l;t++)e[t]&&(r=Jh(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function ye(){for(var e,t,r=0,n="",l=arguments.length;r<l;r++)(e=arguments[r])&&(t=Jh(e))&&(n&&(n+=" "),n+=t);return n}/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pv=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),mv=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,r,n)=>n?n.toUpperCase():r.toLowerCase()),ud=e=>{const t=mv(e);return t.charAt(0).toUpperCase()+t.slice(1)},Xh=(...e)=>e.filter((t,r,n)=>Boolean(t)&&t.trim()!==""&&n.indexOf(t)===r).join(" ").trim(),yv=e=>{for(const t in e)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var gv={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vv=E.exports.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:l="",children:o,iconNode:i,...u},a)=>E.exports.createElement("svg",{ref:a,...gv,width:t,height:t,stroke:e,strokeWidth:n?Number(r)*24/Number(t):r,className:Xh("lucide",l),...!o&&!yv(u)&&{"aria-hidden":"true"},...u},[...i.map(([s,f])=>E.exports.createElement(s,f)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=(e,t)=>{const r=E.exports.forwardRef(({className:n,...l},o)=>E.exports.createElement(vv,{ref:o,iconNode:t,className:Xh(`lucide-${pv(ud(e))}`,`lucide-${e}`,n),...l}));return r.displayName=ud(e),r};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xv=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],sd=X("activity",xv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wv=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],Zo=X("building",wv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kv=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],ei=X("calendar",kv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sv=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],qh=X("chevron-down",Sv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ev=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],Cv=X("chevron-left",Ev);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nv=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Dv=X("chevron-right",Nv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fv=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],cd=X("chevron-up",Fv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bv=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],Rv=X("circle-alert",Bv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Av=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],dd=X("clock",Av);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bv=[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]],Pv=X("coins",bv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _v=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],Lv=X("copy",_v);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Iv=[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]],ti=X("database",Iv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mv=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],lu=X("dollar-sign",Mv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tv=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],ou=X("download",Tv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zv=[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]],Ov=X("external-link",zv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $v=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],jv=X("file-text",$v);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uv=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],Ht=X("funnel",Uv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vv=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]],iu=X("grid-3x3",Vv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hv=[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]],ri=X("hash",Hv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wv=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],Qv=X("info",Wv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kv=[["path",{d:"M9 17H7A5 5 0 0 1 7 7h2",key:"8i5ue5"}],["path",{d:"M15 7h2a5 5 0 1 1 0 10h-2",key:"1b9ql8"}],["line",{x1:"8",x2:"16",y1:"12",y2:"12",key:"1jonct"}]],aa=X("link-2",Kv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yv=[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]],Gv=X("link",Yv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jv=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Xv=X("menu",Jv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qv=[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]],Zv=X("pause",qv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e1=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],t1=X("play",e1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r1=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],n1=X("plus",r1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l1=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],xr=X("refresh-cw",l1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const o1=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],i1=X("rotate-ccw",o1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a1=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],ni=X("search",a1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u1=[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]],s1=X("server",u1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c1=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Fl=X("settings",c1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d1=[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]],li=X("shopping-cart",d1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f1=[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]],h1=X("tag",f1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p1=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],au=X("user",p1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m1=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],uu=X("users",m1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y1=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],g1=X("wifi-off",y1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v1=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]],x1=X("wifi",v1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w1=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],su=X("x",w1),k1=[{name:"\u7528\u6237\u670D\u52A1",href:"/user-services",icon:uu,description:"\u7528\u6237\u670D\u52A1\u8BB0\u5F55\u7BA1\u7406"},{name:"\u8BA2\u5355",href:"/orders",icon:li,description:"\u8BA2\u5355\u4FE1\u606F\u7BA1\u7406"},{name:"\u8BA2\u5355\u670D\u52A1",href:"/order-services",icon:Gv,description:"\u8BA2\u5355\u670D\u52A1\u5173\u8054"},{name:"CLI\u7248\u672C",href:"/cli-versions",icon:ou,description:"CLI\u7248\u672C\u7BA1\u7406"},{name:"\u8D27\u5E01",href:"/currencies",icon:lu,description:"\u8D27\u5E01\u4FE1\u606F\u7BA1\u7406"},{name:"\u670D\u52A1\u5206\u7C7B",href:"/service-categories",icon:iu,description:"\u670D\u52A1\u5206\u7C7B\u7BA1\u7406"},{name:"\u670D\u52A1\u63D0\u4F9B\u5546",href:"/providers",icon:Zo,description:"\u670D\u52A1\u63D0\u4F9B\u5546\u7BA1\u7406"},{name:"\u670D\u52A1\u7C7B\u578B",href:"/service-types",icon:Fl,description:"\u670D\u52A1\u7C7B\u578B\u7BA1\u7406"}],S1=({isOpen:e,onToggle:t})=>S("div",{className:ye("fixed left-0 top-0 h-full bg-white shadow-lg transition-all duration-300 z-30",e?"w-64":"w-16"),children:[S("div",{className:"flex items-center justify-between p-4 border-b",children:[S("div",{className:ye("flex items-center space-x-2",!e&&"justify-center"),children:[d(ti,{className:"h-8 w-8 text-blue-600"}),e&&d("span",{className:"text-xl font-bold text-gray-900",children:"VCloud DB"})]}),d("button",{onClick:t,className:"p-1 rounded-md hover:bg-gray-100 transition-colors",children:e?d(Cv,{className:"h-5 w-5 text-gray-500"}):d(Dv,{className:"h-5 w-5 text-gray-500"})})]}),d("nav",{className:"mt-4 px-2",children:d("ul",{className:"space-y-1",children:k1.map(r=>{const n=r.icon;return d("li",{children:S(Yh,{to:r.href,className:({isActive:l})=>ye("flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors","hover:bg-gray-100 hover:text-gray-900",l?"bg-blue-100 text-blue-700":"text-gray-600",!e&&"justify-center"),title:e?void 0:r.name,children:[d(n,{className:ye("h-5 w-5",e?"mr-3":"mx-auto")}),e&&S("div",{className:"flex-1",children:[d("div",{className:"font-medium",children:r.name}),d("div",{className:"text-xs text-gray-500 mt-0.5",children:r.description})]})]})},r.href)})})})]});function E1(e){return window.go.backend.VCloudDBService.CountOrders(e)}function C1(e,t){return window.go.backend.VCloudDBService.CountRecords(e,t)}function N1(e){return window.go.backend.VCloudDBService.CountUserServices(e)}function D1(e){return window.go.backend.VCloudDBService.FindCliVersions(e)}function F1(e){return window.go.backend.VCloudDBService.FindCurrencies(e)}function B1(e){return window.go.backend.VCloudDBService.FindOrderServices(e)}function R1(e){return window.go.backend.VCloudDBService.FindOrders(e)}function A1(e,t){return window.go.backend.VCloudDBService.FindRecords(e,t)}function b1(e){return window.go.backend.VCloudDBService.FindUserServices(e)}function Zh(){return window.go.backend.VCloudDBService.GetConnectionStatus()}function P1(e,t){return window.go.backend.VCloudDBService.GetRecordById(e,t)}function _1(){return window.go.backend.VCloudDBService.GetTableNames()}function L1(e){return window.go.backend.VCloudDBService.SetRPCURL(e)}const Jr={USER_SERVICE:"user_service",ORDER:"order",ORDER_SERVICE:"order_service",CLI_VERSION:"cli_version",CURRENCY:"currency",SERVICE_CATEGORY:"service_category",PROVIDER:"provider",SERVICE_TYPE:"service_type"},dn=class{constructor(){Yr(this,"cache",new Map);Yr(this,"CACHE_TTL",5*60*1e3)}static getInstance(){return dn.instance||(dn.instance=new dn),dn.instance}getCacheKey(t,r){return`${t}_${JSON.stringify(r)}`}getFromCache(t){const r=this.cache.get(t);return r&&Date.now()-r.timestamp<this.CACHE_TTL?r.data:(this.cache.delete(t),null)}setCache(t,r){this.cache.set(t,{data:r,timestamp:Date.now()})}clearCache(){this.cache.clear()}async handleApiCall(t,r,n=!1,l){try{if(n&&l){const i=this.getFromCache(l);if(i)return{data:i,success:!0}}const o=await t();return n&&l&&this.setCache(l,o),{data:o,success:!0}}catch(o){return console.error(`${r}:`,o),{data:null,success:!1,error:o instanceof Error?o.message:String(o)}}}async getConnectionStatus(){return this.handleApiCall(async()=>await Zh(),"\u83B7\u53D6\u8FDE\u63A5\u72B6\u6001\u5931\u8D25")}async setRPCURL(t){return this.clearCache(),this.handleApiCall(()=>L1(t),"\u8BBE\u7F6ERPC URL\u5931\u8D25")}async findRecords(t,r={}){const n=this.getCacheKey("findRecords",{tableName:t,filter:r});return this.handleApiCall(async()=>(await A1(t,r)).map(o=>JSON.parse(o)),`\u67E5\u8BE2${t}\u8868\u8BB0\u5F55\u5931\u8D25`,!0,n)}async getRecordById(t,r){const n=this.getCacheKey("getRecordById",{tableName:t,id:r});return this.handleApiCall(async()=>{const l=await P1(t,r);return JSON.parse(l)},`\u83B7\u53D6${t}\u8868\u8BB0\u5F55\u5931\u8D25`,!0,n)}async countRecords(t,r={}){const n=this.getCacheKey("countRecords",{tableName:t,filter:r});return this.handleApiCall(()=>C1(t,r),`\u7EDF\u8BA1${t}\u8868\u8BB0\u5F55\u5931\u8D25`,!0,n)}async findUserServices(t={}){const r=this.getCacheKey("findUserServices",t);try{const n=this.getFromCache(r);if(n)return n;const[l,o]=await Promise.all([this.handleApiCall(async()=>(await b1(t)).map(a=>JSON.parse(a)),"\u67E5\u8BE2\u7528\u6237\u670D\u52A1\u5931\u8D25"),this.handleApiCall(()=>N1(t),"\u7EDF\u8BA1\u7528\u6237\u670D\u52A1\u5931\u8D25")]);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const i={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,i),i}catch(n){return console.error("\u67E5\u8BE2\u7528\u6237\u670D\u52A1\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findOrders(t={}){const r=this.getCacheKey("findOrders",t);try{const n=this.getFromCache(r);if(n)return n;const[l,o]=await Promise.all([this.handleApiCall(async()=>(await R1(t)).map(a=>JSON.parse(a)),"\u67E5\u8BE2\u8BA2\u5355\u5931\u8D25"),this.handleApiCall(()=>E1(t),"\u7EDF\u8BA1\u8BA2\u5355\u5931\u8D25")]);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const i={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,i),i}catch(n){return console.error("\u67E5\u8BE2\u8BA2\u5355\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findOrderServices(t={}){const r=this.getCacheKey("findOrderServices",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await B1(t)).map(a=>JSON.parse(a)),"\u67E5\u8BE2\u8BA2\u5355\u670D\u52A1\u5931\u8D25"),o=await this.countRecords(Jr.ORDER_SERVICE,t);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const i={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,i),i}catch(n){return console.error("\u67E5\u8BE2\u8BA2\u5355\u670D\u52A1\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findCliVersions(t={}){const r=this.getCacheKey("findCliVersions",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await D1(t)).map(a=>JSON.parse(a)),"\u67E5\u8BE2CLI\u7248\u672C\u5931\u8D25"),o=await this.countRecords(Jr.CLI_VERSION,t);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const i={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,i),i}catch(n){return console.error("\u67E5\u8BE2CLI\u7248\u672C\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findCurrencies(t={}){const r=this.getCacheKey("findCurrencies",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await F1(t)).map(a=>JSON.parse(a)),"\u67E5\u8BE2\u8D27\u5E01\u5931\u8D25"),o=await this.countRecords(Jr.CURRENCY,t);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const i={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,i),i}catch(n){return console.error("\u67E5\u8BE2\u8D27\u5E01\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findServiceCategories(t){const r=this.getCacheKey("findServiceCategories",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await this.findRecords(Jr.SERVICE_CATEGORY,t)).data,"\u67E5\u8BE2\u670D\u52A1\u5206\u7C7B\u5931\u8D25");if(!l.success)return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:l.error};const o={data:l.data,total:l.data.length,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,o),o}catch(n){return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findProviders(t){const r=this.getCacheKey("findProviders",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await this.findRecords(Jr.PROVIDER,t)).data,"\u67E5\u8BE2\u670D\u52A1\u63D0\u4F9B\u5546\u5931\u8D25");if(!l.success)return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:l.error};const o={data:l.data,total:l.data.length,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,o),o}catch(n){return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findServiceTypes(t){const r=this.getCacheKey("findServiceTypes",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await this.findRecords(Jr.SERVICE_TYPE,t)).data,"\u67E5\u8BE2\u670D\u52A1\u7C7B\u578B\u5931\u8D25");if(!l.success)return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:l.error};const o={data:l.data,total:l.data.length,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,o),o}catch(n){return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async getTableNames(){return this.handleApiCall(()=>_1(),"\u83B7\u53D6\u8868\u540D\u5217\u8868\u5931\u8D25",!0,"tableNames")}};let Eo=dn;Yr(Eo,"instance");const Oe=Eo.getInstance();class I1{constructor(){Yr(this,"listeners",new Set);Yr(this,"globalAutoRefresh",{enabled:!1,interval:30})}subscribe(t){return this.listeners.add(t),()=>this.listeners.delete(t)}triggerGlobalRefresh(){this.listeners.forEach(t=>t())}setGlobalAutoRefresh(t){this.globalAutoRefresh=t}getGlobalAutoRefresh(){return this.globalAutoRefresh}}const Co=new I1;function wr(e,t,r=[],n){const[l,o]=E.exports.useState({data:[],total:0,loading:!1,error:null,page:1,pageSize:20,lastRefresh:void 0,autoRefresh:(n==null?void 0:n.enabled)||!1,refreshInterval:(n==null?void 0:n.interval)||30}),[i,u]=E.exports.useState(t),a=E.exports.useRef(),s=E.exports.useRef(),f=E.exports.useRef(null),y=E.exports.useCallback(async(A,m=!1)=>{a.current&&a.current.abort(),a.current=new AbortController,o(p=>({...p,loading:!m,error:null}));try{const p=await e(A);p.success?o(c=>({...c,data:p.data,total:p.total,page:p.page,pageSize:p.pageSize,loading:!1,error:null,lastRefresh:new Date})):o(c=>({...c,data:[],total:0,loading:!1,error:p.error||"\u67E5\u8BE2\u5931\u8D25"}))}catch(p){p instanceof Error&&p.name!=="AbortError"&&o(c=>({...c,data:[],total:0,loading:!1,error:p instanceof Error?p.message:String(p)}))}},[e]),h=E.exports.useCallback((A=!1)=>{y(i,A)},[y,i]),w=E.exports.useCallback(A=>{const m={...i,...A};u(m),y(m)},[i,y]),k=E.exports.useCallback((A,m)=>{const p=m||l.pageSize,c=(A-1)*p;w({offset:c,limit:p})},[l.pageSize,w]),C=E.exports.useCallback((A,m)=>{o(p=>({...p,autoRefresh:A,refreshInterval:m||p.refreshInterval}))},[]);return E.exports.useEffect(()=>(l.autoRefresh&&l.refreshInterval&&l.refreshInterval>0?s.current=setInterval(()=>{h(!0)},l.refreshInterval*1e3):s.current&&(clearInterval(s.current),s.current=void 0),()=>{s.current&&clearInterval(s.current)}),[l.autoRefresh,l.refreshInterval,h]),E.exports.useEffect(()=>(f.current=Co.subscribe(()=>{h()}),()=>{f.current&&f.current()}),[h]),E.exports.useEffect(()=>{y(i)},r),E.exports.useEffect(()=>()=>{a.current&&a.current.abort(),s.current&&clearInterval(s.current)},[]),{...l,params:i,refetch:h,updateParams:w,changePage:k,setAutoRefresh:C}}function M1(e={},t){return wr(Oe.findUserServices.bind(Oe),{limit:20,offset:0,...e},[],t)}function T1(e={},t){return wr(Oe.findOrders.bind(Oe),{limit:20,offset:0,...e},[],t)}function z1(e={},t){return wr(Oe.findOrderServices.bind(Oe),{limit:20,offset:0,...e},[],t)}function O1(e={},t){return wr(Oe.findCliVersions.bind(Oe),{limit:20,offset:0,...e},[],t)}function $1(e={},t){return wr(Oe.findCurrencies.bind(Oe),{limit:20,offset:0,...e},[],t)}function j1(e={},t){return wr(Oe.findServiceCategories.bind(Oe),{limit:20,offset:0,...e},[],t)}function U1(e={},t){return wr(Oe.findProviders.bind(Oe),{limit:20,offset:0,...e},[],t)}function V1(e={},t){return wr(Oe.findServiceTypes.bind(Oe),{limit:20,offset:0,...e},[],t)}function H1(){const[e,t]=E.exports.useState(Co.getGlobalAutoRefresh()),r=E.exports.useCallback(()=>{Co.triggerGlobalRefresh()},[]),n=E.exports.useCallback(l=>{t(l),Co.setGlobalAutoRefresh(l)},[]);return{globalConfig:e,triggerGlobalRefresh:r,setGlobalAutoRefresh:n}}const W1=({onMenuClick:e,sidebarOpen:t})=>{const[r,n]=E.exports.useState({connected:!1,rpcUrl:""}),[l,o]=E.exports.useState(!1),{triggerGlobalRefresh:i}=H1(),u=async()=>{o(!0);try{const a=await Zh();n(a)}catch(a){n({connected:!1,rpcUrl:"",error:a instanceof Error?a.message:"\u8FDE\u63A5\u68C0\u67E5\u5931\u8D25"})}finally{o(!1)}};return E.exports.useEffect(()=>{u();const a=setInterval(u,3e4);return()=>clearInterval(a)},[]),d("header",{className:"bg-white shadow-sm border-b border-gray-200",children:S("div",{className:"flex items-center justify-between px-6 py-4",children:[S("div",{className:"flex items-center space-x-4",children:[d("button",{onClick:e,className:"p-2 rounded-md hover:bg-gray-100 transition-colors",children:d(Xv,{className:"h-5 w-5 text-gray-600"})}),S("nav",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[d("span",{children:"VCloud DB"}),d("span",{children:"/"}),d("span",{className:"text-gray-900 font-medium",children:"\u6570\u636E\u7BA1\u7406"})]})]}),S("div",{className:"flex items-center space-x-4",children:[S("button",{onClick:i,className:"flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-medium bg-blue-50 text-blue-700 hover:bg-blue-100 transition-colors",title:"\u5237\u65B0\u6240\u6709\u6570\u636E",children:[d(i1,{className:"h-4 w-4"}),d("span",{children:"\u5168\u5C40\u5237\u65B0"})]}),S("div",{className:"flex items-center space-x-2",children:[S("button",{onClick:u,disabled:l,className:ye("flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors",r.connected?"bg-green-100 text-green-700 hover:bg-green-200":"bg-red-100 text-red-700 hover:bg-red-200"),title:r.rpcUrl||"\u70B9\u51FB\u68C0\u67E5\u8FDE\u63A5\u72B6\u6001",children:[l?d(xr,{className:"h-4 w-4 animate-spin"}):r.connected?d(x1,{className:"h-4 w-4"}):d(g1,{className:"h-4 w-4"}),d("span",{children:r.connected?"\u5DF2\u8FDE\u63A5":"\u672A\u8FDE\u63A5"})]}),r.error&&S("div",{className:"flex items-center space-x-1 text-red-600",children:[d(Rv,{className:"h-4 w-4"}),d("span",{className:"text-xs",title:r.error,children:"\u9519\u8BEF"})]})]}),d("button",{className:"p-2 rounded-md hover:bg-gray-100 transition-colors",title:"\u8BBE\u7F6E",children:d(Fl,{className:"h-5 w-5 text-gray-600"})})]})]})})},Q1=({children:e})=>{const[t,r]=E.exports.useState(!0);return S("div",{className:"min-h-screen bg-gray-50 flex",children:[d(S1,{isOpen:t,onToggle:()=>r(!t)}),S("div",{className:ye("flex-1 flex flex-col transition-all duration-300",t?"ml-64":"ml-16"),children:[d(W1,{onMenuClick:()=>r(!t),sidebarOpen:t}),d("main",{className:"flex-1 p-6 overflow-auto",children:e||d(Ag,{})})]})]})},io=({title:e,value:t,icon:r,description:n,trend:l})=>d("div",{className:"bg-white rounded-lg shadow p-6",children:S("div",{className:"flex items-center justify-between",children:[S("div",{children:[d("p",{className:"text-sm font-medium text-gray-600",children:e}),d("p",{className:"text-3xl font-bold text-gray-900",children:t}),n&&d("p",{className:"text-sm text-gray-500 mt-1",children:n}),l&&S("div",{className:`flex items-center mt-2 text-sm ${l.isPositive?"text-green-600":"text-red-600"}`,children:[S("span",{children:[l.isPositive?"+":"",l.value,"%"]}),d("span",{className:"text-gray-500 ml-1",children:"vs \u4E0A\u6708"})]})]}),d("div",{className:"p-3 bg-blue-50 rounded-full",children:d(r,{className:"h-8 w-8 text-blue-600"})})]})}),K1=()=>S("div",{className:"space-y-6",children:[S("div",{children:[d("h1",{className:"text-2xl font-bold text-gray-900",children:"\u4EEA\u8868\u677F"}),d("p",{className:"text-gray-600 mt-1",children:"VCloud DB \u6570\u636E\u6982\u89C8"})]}),S("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[d(io,{title:"\u7528\u6237\u670D\u52A1",value:"1,234",icon:uu,description:"\u6D3B\u8DC3\u670D\u52A1\u6570\u91CF",trend:{value:12,isPositive:!0}}),d(io,{title:"\u8BA2\u5355\u603B\u6570",value:"5,678",icon:li,description:"\u7D2F\u8BA1\u8BA2\u5355\u6570\u91CF",trend:{value:8,isPositive:!0}}),d(io,{title:"\u6570\u636E\u8868",value:"8",icon:ti,description:"\u53EF\u7528\u6570\u636E\u8868"}),d(io,{title:"\u7CFB\u7EDF\u72B6\u6001",value:"\u6B63\u5E38",icon:sd,description:"\u670D\u52A1\u8FD0\u884C\u72B6\u6001"})]}),S("div",{className:"bg-white rounded-lg shadow p-6",children:[d("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"\u5FEB\u901F\u64CD\u4F5C"}),S("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[S("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[d(uu,{className:"h-6 w-6 text-blue-600 mb-2"}),d("div",{className:"font-medium text-gray-900",children:"\u67E5\u770B\u7528\u6237\u670D\u52A1"}),d("div",{className:"text-sm text-gray-500",children:"\u7BA1\u7406\u7528\u6237\u670D\u52A1\u8BB0\u5F55"})]}),S("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[d(li,{className:"h-6 w-6 text-green-600 mb-2"}),d("div",{className:"font-medium text-gray-900",children:"\u67E5\u770B\u8BA2\u5355"}),d("div",{className:"text-sm text-gray-500",children:"\u7BA1\u7406\u8BA2\u5355\u4FE1\u606F"})]}),S("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[d(ti,{className:"h-6 w-6 text-purple-600 mb-2"}),d("div",{className:"font-medium text-gray-900",children:"\u6570\u636E\u67E5\u8BE2"}),d("div",{className:"text-sm text-gray-500",children:"\u6267\u884C\u81EA\u5B9A\u4E49\u67E5\u8BE2"})]}),S("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[d(sd,{className:"h-6 w-6 text-orange-600 mb-2"}),d("div",{className:"font-medium text-gray-900",children:"\u7CFB\u7EDF\u76D1\u63A7"}),d("div",{className:"text-sm text-gray-500",children:"\u67E5\u770B\u7CFB\u7EDF\u72B6\u6001"})]})]})]}),S("div",{className:"bg-white rounded-lg shadow p-6",children:[d("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"\u6700\u8FD1\u6D3B\u52A8"}),S("div",{className:"space-y-3",children:[S("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[d("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),S("div",{className:"flex-1",children:[d("p",{className:"text-sm font-medium text-gray-900",children:"\u65B0\u7528\u6237\u670D\u52A1\u521B\u5EFA"}),d("p",{className:"text-xs text-gray-500",children:"2\u5206\u949F\u524D"})]})]}),S("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[d("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),S("div",{className:"flex-1",children:[d("p",{className:"text-sm font-medium text-gray-900",children:"\u8BA2\u5355\u72B6\u6001\u66F4\u65B0"}),d("p",{className:"text-xs text-gray-500",children:"5\u5206\u949F\u524D"})]})]}),S("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[d("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),S("div",{className:"flex-1",children:[d("p",{className:"text-sm font-medium text-gray-900",children:"\u7CFB\u7EDF\u914D\u7F6E\u66F4\u65B0"}),d("p",{className:"text-xs text-gray-500",children:"10\u5206\u949F\u524D"})]})]})]})]})]});function kr({columns:e,data:t,loading:r=!1,pagination:n,sortConfig:l,onSort:o,rowKey:i="_id",onRowClick:u,emptyText:a="\u6682\u65E0\u6570\u636E",className:s}){const f=(k,C)=>typeof i=="function"?i(k):k[i]||C.toString(),y=k=>{if(!o)return;let C="asc";(l==null?void 0:l.key)===k&&l.direction==="asc"&&(C="desc"),o(k,C)},h=k=>(l==null?void 0:l.key)!==k?d(cd,{className:"h-4 w-4 text-gray-300"}):l.direction==="asc"?d(cd,{className:"h-4 w-4 text-gray-600"}):d(qh,{className:"h-4 w-4 text-gray-600"}),w=(k,C)=>{if(C.render){const A=t.indexOf(k),m=C.dataIndex?k[C.dataIndex]:k;return C.render(m,k,A)}return C.dataIndex?k[C.dataIndex]:""};return r?d("div",{className:ye("bg-white rounded-lg shadow",s),children:S("div",{className:"p-8 text-center",children:[d("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),d("p",{className:"text-gray-500 mt-2",children:"\u52A0\u8F7D\u4E2D..."})]})}):S("div",{className:ye("bg-white rounded-lg shadow overflow-hidden",s),children:[d("div",{className:"overflow-x-auto",children:S("table",{className:"min-w-full divide-y divide-gray-200",children:[d("thead",{className:"bg-gray-50",children:d("tr",{children:e.map(k=>d("th",{className:ye("px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",k.align==="center"&&"text-center",k.align==="right"&&"text-right",k.sortable&&"cursor-pointer hover:bg-gray-100"),style:{width:k.width},onClick:()=>k.sortable&&y(k.key),children:S("div",{className:"flex items-center space-x-1",children:[d("span",{children:k.title}),k.sortable&&h(k.key)]})},k.key))})}),d("tbody",{className:"bg-white divide-y divide-gray-200",children:t.length===0?d("tr",{children:d("td",{colSpan:e.length,className:"px-6 py-8 text-center text-gray-500",children:a})}):t.map((k,C)=>d("tr",{className:ye("hover:bg-gray-50 transition-colors",u&&"cursor-pointer"),onClick:()=>u==null?void 0:u(k,C),children:e.map(A=>d("td",{className:ye("px-6 py-4 whitespace-nowrap text-sm text-gray-900",A.align==="center"&&"text-center",A.align==="right"&&"text-right"),children:w(k,A)},A.key))},f(k,C)))})]})}),n&&d("div",{className:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6",children:S("div",{className:"flex items-center justify-between",children:[S("div",{className:"flex-1 flex justify-between sm:hidden",children:[d("button",{disabled:n.current<=1,onClick:()=>n.onChange(n.current-1,n.pageSize),className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"\u4E0A\u4E00\u9875"}),d("button",{disabled:n.current*n.pageSize>=n.total,onClick:()=>n.onChange(n.current+1,n.pageSize),className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"\u4E0B\u4E00\u9875"})]}),S("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[d("div",{children:S("p",{className:"text-sm text-gray-700",children:["\u663E\u793A\u7B2C"," ",d("span",{className:"font-medium",children:(n.current-1)*n.pageSize+1})," ","\u5230"," ",d("span",{className:"font-medium",children:Math.min(n.current*n.pageSize,n.total)})," ","\u6761\uFF0C\u5171"," ",d("span",{className:"font-medium",children:n.total})," \u6761\u8BB0\u5F55"]})}),d("div",{children:S("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px",children:[d("button",{disabled:n.current<=1,onClick:()=>n.onChange(n.current-1,n.pageSize),className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"\u4E0A\u4E00\u9875"}),d("button",{disabled:n.current*n.pageSize>=n.total,onClick:()=>n.onChange(n.current+1,n.pageSize),className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"\u4E0B\u4E00\u9875"})]})})]})]})})]})}function Y1(e){const{filename:t="export",columns:r,data:n}=e;if(!n||n.length===0){alert("\u6CA1\u6709\u6570\u636E\u53EF\u5BFC\u51FA");return}const l=r||Object.keys(n[0]).map(f=>({key:f,title:f})),o=l.map(f=>`"${f.title}"`).join(","),i=n.map(f=>l.map(y=>{let h=f[y.key];return y.render?h=y.render(h,f):h==null?h="":typeof h=="object"?h=JSON.stringify(h):typeof h=="boolean"?h=h?"\u662F":"\u5426":typeof h=="number"&&h>1e9&&(h=new Date(h*1e3).toLocaleString()),`"${String(h).replace(/"/g,'""')}"`}).join(",")),u=[o,...i].join(`
`),a="\uFEFF",s=new Blob([a+u],{type:"text/csv;charset=utf-8;"});e0(s,`${t}.csv`)}function G1(e){const{filename:t="export",columns:r,data:n}=e;if(!n||n.length===0){alert("\u6CA1\u6709\u6570\u636E\u53EF\u5BFC\u51FA");return}let l=n;r&&(l=n.map(u=>{const a={};return r.forEach(s=>{let f=u[s.key];s.render&&(f=s.render(f,u)),a[s.title]=f}),a}));const o=JSON.stringify(l,null,2),i=new Blob([o],{type:"application/json;charset=utf-8;"});e0(i,`${t}.json`)}function e0(e,t){const r=window.URL.createObjectURL(e),n=document.createElement("a");n.href=r,n.download=t,n.style.display="none",document.body.appendChild(n),n.click(),document.body.removeChild(n),window.URL.revokeObjectURL(r)}function De(e){return!e||e===0?"-":new Date(e*1e3).toLocaleString()}function t0(e){return e?"\u662F":"\u5426"}function xn(e){return e?typeof e=="object"?JSON.stringify(e):String(e):"-"}const Sr=({data:e,columns:t,filename:r="export",disabled:n=!1,className:l=""})=>{const[o,i]=E.exports.useState(!1),u=a=>{const s={data:e,columns:t,filename:`${r}_${new Date().toISOString().split("T")[0]}`};a==="csv"?Y1(s):G1(s),i(!1)};return n||!e||e.length===0?S("button",{disabled:!0,className:`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-400 bg-gray-100 cursor-not-allowed ${l}`,children:[d(ou,{className:"h-4 w-4 mr-2"}),"\u5BFC\u51FA\u6570\u636E"]}):S("div",{className:"relative",children:[S("button",{onClick:()=>i(!o),className:`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${l}`,children:[d(ou,{className:"h-4 w-4 mr-2"}),"\u5BFC\u51FA\u6570\u636E",d("svg",{className:"ml-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),o&&S(is,{children:[d("div",{className:"fixed inset-0 z-10",onClick:()=>i(!1)}),S("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-20",children:[S("div",{className:"py-1",children:[S("button",{onClick:()=>u("csv"),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900",children:[d(jv,{className:"h-4 w-4 mr-3 text-green-500"}),"\u5BFC\u51FA\u4E3A CSV",d("span",{className:"ml-auto text-xs text-gray-500",children:"Excel\u517C\u5BB9"})]}),S("button",{onClick:()=>u("json"),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900",children:[d(ti,{className:"h-4 w-4 mr-3 text-blue-500"}),"\u5BFC\u51FA\u4E3A JSON",d("span",{className:"ml-auto text-xs text-gray-500",children:"\u7ED3\u6784\u5316\u6570\u636E"})]})]}),d("div",{className:"border-t border-gray-100 px-4 py-2",children:S("div",{className:"text-xs text-gray-500",children:["\u5171 ",e.length," \u6761\u8BB0\u5F55"]})})]})]})]})},ms=({isOpen:e,onClose:t,title:r,data:n,fields:l,icon:o})=>{if(!e||!n)return null;const i=s=>{navigator.clipboard.writeText(s).then(()=>{console.log("\u5DF2\u590D\u5236\u5230\u526A\u8D34\u677F")})},u=(s,f)=>{if(f==null)return d("span",{className:"text-gray-400",children:"-"});if(s.render)return s.render(f,n);switch(s.type){case"boolean":return d("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${f?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:f?"\u662F":"\u5426"});case"timestamp":return S("div",{className:"flex items-center",children:[d(ei,{className:"h-4 w-4 text-gray-400 mr-2"}),d("span",{children:f?new Date(f*1e3).toLocaleString():"-"})]});case"json":return d("pre",{className:"bg-gray-50 p-2 rounded text-xs overflow-x-auto max-w-md",children:JSON.stringify(f,null,2)});case"address":return S("div",{className:"flex items-center",children:[d(au,{className:"h-4 w-4 text-blue-500 mr-2"}),d("span",{className:"font-mono text-sm",children:f})]});case"id":return S("div",{className:"flex items-center",children:[d(ri,{className:"h-4 w-4 text-gray-500 mr-2"}),d("span",{className:"font-mono text-sm",children:f})]});case"amount":return S("div",{className:"flex items-center",children:[d(lu,{className:"h-4 w-4 text-green-500 mr-2"}),d("span",{className:"font-medium",children:Number(f).toLocaleString()})]});case"number":return d("span",{className:"font-medium",children:Number(f).toLocaleString()});default:return d("span",{children:String(f)})}},a=s=>{switch(s.type){case"timestamp":return d(ei,{className:"h-4 w-4 text-gray-400"});case"address":return d(au,{className:"h-4 w-4 text-blue-500"});case"id":return d(ri,{className:"h-4 w-4 text-gray-500"});case"amount":return d(lu,{className:"h-4 w-4 text-green-500"});case"json":return d(Fl,{className:"h-4 w-4 text-purple-500"});default:return d(Qv,{className:"h-4 w-4 text-gray-400"})}};return S("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[d("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:t}),d("div",{className:"flex min-h-full items-center justify-center p-4",children:S("div",{className:"relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[S("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[S("div",{className:"flex items-center",children:[o&&d("div",{className:"mr-3",children:o}),d("h3",{className:"text-lg font-medium text-gray-900",children:r})]}),d("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:d(su,{className:"h-6 w-6"})})]}),d("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:d("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:l.map(s=>{const f=n[s.key],y=u(s,f);return S("div",{className:"space-y-2",children:[S("div",{className:"flex items-center justify-between",children:[S("label",{className:"flex items-center text-sm font-medium text-gray-700",children:[a(s),d("span",{className:"ml-2",children:s.label})]}),S("div",{className:"flex space-x-1",children:[s.copyable&&f&&d("button",{onClick:()=>i(String(f)),className:"p-1 text-gray-400 hover:text-gray-600 transition-colors",title:"\u590D\u5236",children:d(Lv,{className:"h-4 w-4"})}),s.linkable&&f&&d("button",{onClick:()=>window.open(String(f),"_blank"),className:"p-1 text-gray-400 hover:text-gray-600 transition-colors",title:"\u6253\u5F00\u94FE\u63A5",children:d(Ov,{className:"h-4 w-4"})})]})]}),d("div",{className:"bg-gray-50 rounded-md p-3 min-h-[2.5rem] flex items-center",children:y})]},s.key)})})}),d("div",{className:"flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50",children:d("button",{onClick:t,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"\u5173\u95ED"})})]})})]})},fd=[{label:"10\u79D2",value:10},{label:"30\u79D2",value:30},{label:"1\u5206\u949F",value:60},{label:"5\u5206\u949F",value:300},{label:"10\u5206\u949F",value:600}],r0=({onRefresh:e,loading:t=!1,lastRefresh:r,autoRefresh:n=!1,refreshInterval:l=30,onAutoRefreshChange:o,className:i})=>{var h;const[u,a]=E.exports.useState(!1),s=w=>{const k=new Date,C=Math.floor((k.getTime()-w.getTime())/1e3);return C<60?`${C}\u79D2\u524D`:C<3600?`${Math.floor(C/60)}\u5206\u949F\u524D`:`${Math.floor(C/3600)}\u5C0F\u65F6\u524D`},f=()=>{o&&o(!n,l)},y=w=>{o&&o(n,w),a(!1)};return S("div",{className:ye("flex items-center space-x-2",i),children:[S("button",{onClick:e,disabled:t,className:ye("flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors","bg-blue-50 text-blue-700 hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed"),title:"\u624B\u52A8\u5237\u65B0\u6570\u636E",children:[d(xr,{className:ye("h-4 w-4",t&&"animate-spin")}),d("span",{children:"\u5237\u65B0"})]}),o&&S("button",{onClick:f,className:ye("flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors",n?"bg-green-50 text-green-700 hover:bg-green-100":"bg-gray-50 text-gray-700 hover:bg-gray-100"),title:n?"\u505C\u6B62\u81EA\u52A8\u5237\u65B0":"\u5F00\u542F\u81EA\u52A8\u5237\u65B0",children:[n?d(Zv,{className:"h-4 w-4"}):d(t1,{className:"h-4 w-4"}),d("span",{children:n?"\u81EA\u52A8":"\u624B\u52A8"})]}),o&&S("div",{className:"relative",children:[S("button",{onClick:()=>a(!u),className:ye("flex items-center space-x-1 px-2 py-1.5 rounded-md text-sm text-gray-600 hover:bg-gray-100 transition-colors"),title:"\u8BBE\u7F6E\u5237\u65B0\u95F4\u9694",children:[d(dd,{className:"h-4 w-4"}),d("span",{children:(h=fd.find(w=>w.value===l))==null?void 0:h.label}),d(qh,{className:"h-3 w-3"})]}),u&&d("div",{className:"absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]",children:d("div",{className:"py-1",children:fd.map(w=>d("button",{onClick:()=>y(w.value),className:ye("w-full text-left px-3 py-2 text-sm hover:bg-gray-100 transition-colors",l===w.value&&"bg-blue-50 text-blue-700"),children:w.label},w.value))})})]}),r&&S("div",{className:"flex items-center space-x-1 text-xs text-gray-500",children:[d(dd,{className:"h-3 w-3"}),S("span",{children:["\u66F4\u65B0\u4E8E ",s(r)]})]}),u&&d("div",{className:"fixed inset-0 z-0",onClick:()=>a(!1)})]})},J1=()=>{const[e,t]=E.exports.useState(""),[r,n]=E.exports.useState({}),[l,o]=E.exports.useState(null),[i,u]=E.exports.useState(!1),{data:a,total:s,loading:f,error:y,page:h,pageSize:w,params:k,refetch:C,updateParams:A,changePage:m,lastRefresh:p,autoRefresh:c,refreshInterval:g,setAutoRefresh:B}=M1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),v=[{key:"serviceID",title:"\u670D\u52A1ID",dataIndex:"serviceID",width:200,sortable:!0,render:M=>d("span",{className:"font-mono text-sm text-blue-600",children:M})},{key:"address",title:"\u7528\u6237\u5730\u5740",dataIndex:"address",width:180,render:M=>d("span",{className:"font-mono text-xs text-gray-600",children:M?`${M.slice(0,8)}...${M.slice(-6)}`:"-"})},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546",dataIndex:"provider",width:120,sortable:!0},{key:"status",title:"\u72B6\u6001",dataIndex:"status",width:100,render:M=>d("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${{active:"bg-green-100 text-green-800",inactive:"bg-gray-100 text-gray-800",pending:"bg-yellow-100 text-yellow-800",error:"bg-red-100 text-red-800"}[M]||"bg-gray-100 text-gray-800"}`,children:M})},{key:"serviceActivated",title:"\u670D\u52A1\u6FC0\u6D3B",dataIndex:"serviceActivated",width:100,align:"center",render:M=>d("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${M?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:M?"\u5DF2\u6FC0\u6D3B":"\u672A\u6FC0\u6D3B"})},{key:"amount",title:"\u91D1\u989D",dataIndex:"amount",width:120,align:"right",sortable:!0,render:M=>d("span",{className:"font-medium",children:(M==null?void 0:M.toLocaleString())||0})},{key:"duration",title:"\u6301\u7EED\u65F6\u95F4",dataIndex:"duration",width:100,align:"right",render:M=>d("span",{children:M?`${M}\u5929`:"-"})},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:M=>d("span",{className:"text-sm text-gray-600",children:M?new Date(M*1e3).toLocaleString():"-"})}],R=(M,Z)=>{A({sortBy:M,sortDesc:Z==="desc",offset:0})},b=(M,Z)=>{m(M,Z)},z=M=>{o(M),u(!0)},D=()=>{u(!1),o(null)},j=[{key:"_id",label:"ID",type:"id",copyable:!0},{key:"serviceID",label:"\u670D\u52A1ID",type:"id",copyable:!0},{key:"address",label:"\u7528\u6237\u5730\u5740",type:"address",copyable:!0},{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text"},{key:"providerAddress",label:"\u63D0\u4F9B\u5546\u5730\u5740",type:"address",copyable:!0},{key:"status",label:"\u72B6\u6001",type:"text"},{key:"serviceActivated",label:"\u670D\u52A1\u5DF2\u6FC0\u6D3B",type:"boolean"},{key:"duration",label:"\u6301\u7EED\u65F6\u95F4\uFF08\u79D2\uFF09",type:"number"},{key:"amount",label:"\u91D1\u989D",type:"amount"},{key:"service",label:"\u670D\u52A1\u540D\u79F0",type:"text"},{key:"createdAt",label:"\u521B\u5EFA\u65F6\u95F4",type:"timestamp"},{key:"updatedAt",label:"\u66F4\u65B0\u65F6\u95F4",type:"timestamp"},{key:"endAt",label:"\u7ED3\u675F\u65F6\u95F4",type:"timestamp"},{key:"deletedAt",label:"\u5220\u9664\u65F6\u95F4",type:"timestamp"}],Fe=()=>{const M={...r,offset:0};e.trim()&&(e.startsWith("0x")||e.length===42?M.address=e:M.serviceID=e),A(M)},_e=()=>{C()},re=()=>{t(""),n({}),A({offset:0,limit:20,sortBy:"createdAt",sortDesc:!0})};return S("div",{className:"space-y-6",children:[S("div",{className:"flex items-center justify-between",children:[S("div",{children:[d("h1",{className:"text-2xl font-bold text-gray-900",children:"\u7528\u6237\u670D\u52A1"}),d("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u548C\u67E5\u770B\u7528\u6237\u670D\u52A1\u8BB0\u5F55"})]}),S("div",{className:"flex items-center space-x-3",children:[d(r0,{onRefresh:_e,loading:f,lastRefresh:p,autoRefresh:c,refreshInterval:g,onAutoRefreshChange:B}),d(Sr,{data:a,columns:[{key:"_id",title:"ID"},{key:"serviceID",title:"\u670D\u52A1ID"},{key:"address",title:"\u7528\u6237\u5730\u5740"},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546"},{key:"providerAddress",title:"\u63D0\u4F9B\u5546\u5730\u5740"},{key:"status",title:"\u72B6\u6001"},{key:"serviceActivated",title:"\u670D\u52A1\u5DF2\u6FC0\u6D3B",render:M=>t0(M)},{key:"duration",title:"\u6301\u7EED\u65F6\u95F4\uFF08\u79D2\uFF09"},{key:"amount",title:"\u91D1\u989D"},{key:"service",title:"\u670D\u52A1\u540D\u79F0"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:M=>De(M)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:M=>De(M)},{key:"endAt",title:"\u7ED3\u675F\u65F6\u95F4",render:M=>De(M)}],filename:"user_services",disabled:f||a.length===0}),S("button",{className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:[d(n1,{className:"h-4 w-4 mr-2"}),"\u65B0\u5EFA"]})]})]}),d("div",{className:"bg-white rounded-lg shadow p-6",children:S("div",{className:"flex items-center space-x-4",children:[d("div",{className:"flex-1",children:S("div",{className:"relative",children:[d(ni,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),d("input",{type:"text",placeholder:"\u641C\u7D22\u670D\u52A1ID\u3001\u7528\u6237\u5730\u5740...",value:e,onChange:M=>t(M.target.value),onKeyPress:M=>M.key==="Enter"&&Fe(),className:"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]})}),S("button",{onClick:Fe,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:[d(ni,{className:"h-4 w-4 mr-2"}),"\u641C\u7D22"]}),S("button",{onClick:re,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[d(Ht,{className:"h-4 w-4 mr-2"}),"\u6E05\u9664\u7B5B\u9009"]})]})}),y&&d("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:d("div",{className:"flex",children:S("div",{className:"ml-3",children:[d("h3",{className:"text-sm font-medium text-red-800",children:"\u52A0\u8F7D\u6570\u636E\u65F6\u51FA\u9519"}),d("div",{className:"mt-2 text-sm text-red-700",children:d("p",{children:y})}),d("div",{className:"mt-4",children:d("button",{onClick:_e,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"\u91CD\u8BD5"})})]})})}),d(kr,{columns:v,data:a,loading:f,pagination:{current:h,pageSize:w,total:s,onChange:b},sortConfig:{key:k.sortBy||"createdAt",direction:k.sortDesc?"desc":"asc"},onSort:R,rowKey:"_id",onRowClick:z,emptyText:y?"\u52A0\u8F7D\u5931\u8D25":"\u6682\u65E0\u6570\u636E"}),d(ms,{isOpen:i,onClose:D,title:"\u7528\u6237\u670D\u52A1\u8BE6\u60C5",data:l,fields:j,icon:d(s1,{className:"h-6 w-6 text-blue-600"})})]})},Hr=({fields:e,onSubmit:t,onReset:r,loading:n=!1,className:l,showAdvanced:o=!1,initialValues:i={}})=>{const[u,a]=E.exports.useState(()=>{const g={};return e.forEach(B=>{B.defaultValue!==void 0&&(g[B.key]=B.defaultValue)}),{...g,...i}}),[s,f]=E.exports.useState({}),[y,h]=E.exports.useState(o),w=(g,B)=>{a(v=>({...v,[g]:B})),s[g]&&f(v=>{const R={...v};return delete R[g],R})},k=()=>{const g={};return e.forEach(B=>{if(B.validation){const v=B.validation(u[B.key]);v&&(g[B.key]=v)}}),f(g),Object.keys(g).length===0},C=g=>{if(g.preventDefault(),k()){const B=Object.entries(u).reduce((v,[R,b])=>(b!=null&&b!==""&&(v[R]=b),v),{});t(B)}},A=()=>{const g={};e.forEach(B=>{B.defaultValue!==void 0&&(g[B.key]=B.defaultValue)}),a(g),f({}),r==null||r()},m=g=>{var D;const B=u[g.key]||"",v=s[g.key],R=!!v,b=ye("block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 sm:text-sm",R?"border-red-300 focus:border-red-500 focus:ring-red-500":"border-gray-300 focus:border-blue-500 focus:ring-blue-500"),z=()=>{switch(g.type){case"text":return g.key.includes("address")?d(au,{className:"h-4 w-4"}):g.key.includes("provider")?d(Zo,{className:"h-4 w-4"}):d(ri,{className:"h-4 w-4"});case"date":case"dateRange":return d(ei,{className:"h-4 w-4"});case"number":return d(ri,{className:"h-4 w-4"});default:return d(ni,{className:"h-4 w-4"})}};switch(g.type){case"text":case"number":return S("div",{className:"relative",children:[d("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:g.label}),S("div",{className:"relative",children:[d("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400",children:z()}),d("input",{type:g.type,value:B,onChange:j=>w(g.key,j.target.value),placeholder:g.placeholder,className:ye(b,"pl-10")})]}),R&&d("p",{className:"mt-1 text-sm text-red-600",children:v})]},g.key);case"select":return S("div",{children:[d("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:g.label}),S("select",{value:B,onChange:j=>w(g.key,j.target.value),className:b,children:[d("option",{value:"",children:"\u8BF7\u9009\u62E9..."}),(D=g.options)==null?void 0:D.map(j=>d("option",{value:String(j.value),children:j.label},String(j.value)))]}),R&&d("p",{className:"mt-1 text-sm text-red-600",children:v})]},g.key);case"boolean":return S("div",{children:[S("label",{className:"flex items-center",children:[d("input",{type:"checkbox",checked:B||!1,onChange:j=>w(g.key,j.target.checked),className:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"}),d("span",{className:"ml-2 text-sm font-medium text-gray-700",children:g.label})]}),R&&d("p",{className:"mt-1 text-sm text-red-600",children:v})]},g.key);case"date":return S("div",{children:[d("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:g.label}),S("div",{className:"relative",children:[d("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400",children:d(ei,{className:"h-4 w-4"})}),d("input",{type:"date",value:B,onChange:j=>w(g.key,j.target.value),className:ye(b,"pl-10")})]}),R&&d("p",{className:"mt-1 text-sm text-red-600",children:v})]},g.key);default:return null}},p=e.slice(0,3),c=e.slice(3);return d("div",{className:ye("bg-white rounded-lg shadow p-6",l),children:S("form",{onSubmit:C,className:"space-y-4",children:[d("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:p.map(m)}),c.length>0&&S(is,{children:[d("div",{className:"flex items-center justify-between pt-2",children:S("button",{type:"button",onClick:()=>h(!y),className:"flex items-center text-sm text-blue-600 hover:text-blue-800",children:[d(Ht,{className:"h-4 w-4 mr-1"}),"\u9AD8\u7EA7\u7B5B\u9009",y?d(su,{className:"h-4 w-4 ml-1"}):S("span",{className:"ml-1",children:["(",c.length,")"]})]})}),y&&d("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t border-gray-200",children:c.map(m)})]}),S("div",{className:"flex items-center justify-between pt-4 border-t border-gray-200",children:[S("button",{type:"button",onClick:A,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[d(su,{className:"h-4 w-4 mr-2"}),"\u91CD\u7F6E"]}),S("button",{type:"submit",disabled:n,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:[d(ni,{className:ye("h-4 w-4 mr-2",n&&"animate-spin")}),n?"\u67E5\u8BE2\u4E2D...":"\u67E5\u8BE2"]})]})]})})},X1=()=>{const[e,t]=E.exports.useState(!1),[r,n]=E.exports.useState(null),[l,o]=E.exports.useState(!1),{data:i,total:u,loading:a,error:s,page:f,pageSize:y,params:h,refetch:w,updateParams:k,changePage:C}=T1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),A=[{key:"type",title:"\u8BA2\u5355\u7C7B\u578B",dataIndex:"type",width:120,sortable:!0,render:D=>d("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${D==="purchase"?"bg-green-100 text-green-800":D==="refund"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:D==="purchase"?"\u8D2D\u4E70":D==="refund"?"\u9000\u6B3E":D})},{key:"address",title:"\u7528\u6237\u5730\u5740",dataIndex:"address",width:180,render:D=>d("span",{className:"font-mono text-xs text-gray-600",children:D?`${D.slice(0,8)}...${D.slice(-6)}`:"-"})},{key:"recipient",title:"\u63A5\u6536\u5730\u5740",dataIndex:"recipient",width:180,render:D=>d("span",{className:"font-mono text-xs text-gray-600",children:D?`${D.slice(0,8)}...${D.slice(-6)}`:"-"})},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546",dataIndex:"provider",width:120,sortable:!0},{key:"status",title:"\u8BA2\u5355\u72B6\u6001",dataIndex:"status",width:100,render:D=>{const j={pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800",refunded:"bg-purple-100 text-purple-800"},Fe={pending:"\u5F85\u652F\u4ED8",paid:"\u5DF2\u652F\u4ED8",completed:"\u5DF2\u5B8C\u6210",cancelled:"\u5DF2\u53D6\u6D88",refunded:"\u5DF2\u9000\u6B3E"};return d("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${j[D]||"bg-gray-100 text-gray-800"}`,children:Fe[D]||D})}},{key:"amount",title:"\u8BA2\u5355\u91D1\u989D",dataIndex:"amount",width:120,align:"right",sortable:!0,render:D=>d("span",{className:"font-medium",children:(D==null?void 0:D.toLocaleString())||0})},{key:"amountPaid",title:"\u5DF2\u652F\u4ED8\u91D1\u989D",dataIndex:"amountPaid",width:120,align:"right",render:D=>d("span",{className:"font-medium text-green-600",children:(D==null?void 0:D.toLocaleString())||0})},{key:"userServiceIDs",title:"\u5173\u8054\u670D\u52A1",dataIndex:"userServiceIDs",width:100,align:"center",render:D=>S("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:[(D==null?void 0:D.length)||0," \u4E2A"]})},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:D=>d("span",{className:"text-sm text-gray-600",children:D?new Date(D*1e3).toLocaleString():"-"})},{key:"paidTS",title:"\u652F\u4ED8\u65F6\u95F4",dataIndex:"paidTS",width:160,render:D=>d("span",{className:"text-sm text-gray-600",children:D?new Date(D*1e3).toLocaleString():"-"})}],m=[{key:"address",label:"\u7528\u6237\u5730\u5740",type:"text",placeholder:"\u8F93\u5165\u7528\u6237\u5730\u5740..."},{key:"recipient",label:"\u63A5\u6536\u5730\u5740",type:"text",placeholder:"\u8F93\u5165\u63A5\u6536\u5730\u5740..."},{key:"type",label:"\u8BA2\u5355\u7C7B\u578B",type:"select",options:[{label:"\u8D2D\u4E70",value:"purchase"},{label:"\u9000\u6B3E",value:"refund"}]},{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u63D0\u4F9B\u5546..."},{key:"status",label:"\u8BA2\u5355\u72B6\u6001",type:"select",options:[{label:"\u5F85\u652F\u4ED8",value:"pending"},{label:"\u5DF2\u652F\u4ED8",value:"paid"},{label:"\u5DF2\u5B8C\u6210",value:"completed"},{label:"\u5DF2\u53D6\u6D88",value:"cancelled"},{label:"\u5DF2\u9000\u6B3E",value:"refunded"}]},{key:"minAmount",label:"\u6700\u5C0F\u91D1\u989D",type:"number",placeholder:"\u8F93\u5165\u6700\u5C0F\u91D1\u989D..."},{key:"maxAmount",label:"\u6700\u5927\u91D1\u989D",type:"number",placeholder:"\u8F93\u5165\u6700\u5927\u91D1\u989D..."}],p=(D,j)=>{k({sortBy:D,sortDesc:j==="desc",offset:0})},c=(D,j)=>{C(D,j)},g=D=>{n(D),o(!0)},B=()=>{o(!1),n(null)},v=[{key:"_id",label:"ID",type:"id",copyable:!0},{key:"type",label:"\u8BA2\u5355\u7C7B\u578B",type:"text"},{key:"address",label:"\u7528\u6237\u5730\u5740",type:"address",copyable:!0},{key:"recipient",label:"\u63A5\u6536\u5730\u5740",type:"address",copyable:!0},{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text"},{key:"status",label:"\u8BA2\u5355\u72B6\u6001",type:"text"},{key:"amount",label:"\u8BA2\u5355\u91D1\u989D",type:"amount"},{key:"amountPaid",label:"\u5DF2\u652F\u4ED8\u91D1\u989D",type:"amount"},{key:"createdAt",label:"\u521B\u5EFA\u65F6\u95F4",type:"timestamp"},{key:"updatedAt",label:"\u66F4\u65B0\u65F6\u95F4",type:"timestamp"},{key:"paidTS",label:"\u652F\u4ED8\u65F6\u95F4",type:"timestamp"},{key:"filedTS",label:"\u63D0\u4EA4\u65F6\u95F4",type:"timestamp"},{key:"deletedAt",label:"\u5220\u9664\u65F6\u95F4",type:"timestamp"}],R=D=>{const j={offset:0,...D};D.minAmount&&(j.tsStart=D.minAmount),D.maxAmount&&(j.tsEnd=D.maxAmount),k(j),t(!1)},b=()=>{w()},z=()=>{k({offset:0,limit:20,sortBy:"createdAt",sortDesc:!0}),t(!1)};return S("div",{className:"space-y-6",children:[S("div",{className:"flex items-center justify-between",children:[S("div",{children:[d("h1",{className:"text-2xl font-bold text-gray-900",children:"\u8BA2\u5355\u7BA1\u7406"}),d("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u548C\u67E5\u770B\u8BA2\u5355\u4FE1\u606F"})]}),S("div",{className:"flex space-x-3",children:[S("button",{onClick:b,disabled:a,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:[d(xr,{className:`h-4 w-4 mr-2 ${a?"animate-spin":""}`}),"\u5237\u65B0"]}),S("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[d(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u9AD8\u7EA7\u7B5B\u9009"]}),d(Sr,{data:i,columns:[{key:"_id",title:"ID"},{key:"type",title:"\u8BA2\u5355\u7C7B\u578B"},{key:"address",title:"\u7528\u6237\u5730\u5740"},{key:"recipient",title:"\u63A5\u6536\u5730\u5740"},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546"},{key:"status",title:"\u8BA2\u5355\u72B6\u6001"},{key:"amount",title:"\u8BA2\u5355\u91D1\u989D"},{key:"amountPaid",title:"\u5DF2\u652F\u4ED8\u91D1\u989D"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:D=>De(D)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:D=>De(D)},{key:"paidTS",title:"\u652F\u4ED8\u65F6\u95F4",render:D=>De(D)},{key:"filedTS",title:"\u63D0\u4EA4\u65F6\u95F4",render:D=>De(D)}],filename:"orders",disabled:a||i.length===0})]})]}),e&&d(Hr,{fields:m,onSubmit:R,onReset:z,loading:a,showAdvanced:!0}),s&&d("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:d("div",{className:"flex",children:S("div",{className:"ml-3",children:[d("h3",{className:"text-sm font-medium text-red-800",children:"\u52A0\u8F7D\u6570\u636E\u65F6\u51FA\u9519"}),d("div",{className:"mt-2 text-sm text-red-700",children:d("p",{children:s})}),d("div",{className:"mt-4",children:d("button",{onClick:b,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"\u91CD\u8BD5"})})]})})}),d(kr,{columns:A,data:i,loading:a,pagination:{current:f,pageSize:y,total:u,onChange:c},sortConfig:{key:h.sortBy||"createdAt",direction:h.sortDesc?"desc":"asc"},onSort:p,rowKey:"_id",onRowClick:g,emptyText:s?"\u52A0\u8F7D\u5931\u8D25":"\u6682\u65E0\u8BA2\u5355\u6570\u636E"}),d(ms,{isOpen:l,onClose:B,title:"\u8BA2\u5355\u8BE6\u60C5",data:r,fields:v,icon:d(li,{className:"h-6 w-6 text-blue-600"})})]})},q1=()=>{const[e,t]=E.exports.useState(!1),[r,n]=E.exports.useState(null),[l,o]=E.exports.useState(!1),{data:i,total:u,loading:a,error:s,page:f,pageSize:y,params:h,refetch:w,updateParams:k,changePage:C}=z1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),A=[{key:"_id",title:"ID",dataIndex:"_id",width:120,render:D=>S("span",{className:"font-mono text-xs text-gray-600",children:[D.substring(0,8),"..."]})},{key:"orderID",title:"\u8BA2\u5355ID",dataIndex:"orderID",width:150,sortable:!0,render:D=>S("span",{className:"font-mono text-xs text-blue-600",children:[D.substring(0,12),"..."]})},{key:"userServiceID",title:"\u7528\u6237\u670D\u52A1ID",dataIndex:"userServiceID",width:150,sortable:!0,render:D=>S("span",{className:"font-mono text-xs text-green-600",children:[D.substring(0,12),"..."]})},{key:"orderStatus",title:"\u8BA2\u5355\u72B6\u6001",dataIndex:"orderStatus",width:120,sortable:!0,render:D=>d("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${D==="paid"?"bg-green-100 text-green-800":D==="pending"?"bg-yellow-100 text-yellow-800":D==="cancelled"?"bg-red-100 text-red-800":D==="completed"?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:D==="paid"?"\u5DF2\u652F\u4ED8":D==="pending"?"\u5F85\u652F\u4ED8":D==="cancelled"?"\u5DF2\u53D6\u6D88":D==="completed"?"\u5DF2\u5B8C\u6210":D})},{key:"orderType",title:"\u8BA2\u5355\u7C7B\u578B",dataIndex:"orderType",width:120,sortable:!0,render:D=>d("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${D==="purchase"?"bg-green-100 text-green-800":D==="refund"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:D==="purchase"?"\u8D2D\u4E70":D==="refund"?"\u9000\u6B3E":D})},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:D=>d("span",{className:"text-sm text-gray-600",children:new Date(D*1e3).toLocaleString()})},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",dataIndex:"updatedAt",width:160,sortable:!0,render:D=>d("span",{className:"text-sm text-gray-600",children:new Date(D*1e3).toLocaleString()})}],m=[{key:"orderID",label:"\u8BA2\u5355ID",type:"text",placeholder:"\u8F93\u5165\u8BA2\u5355ID..."},{key:"userServiceID",label:"\u7528\u6237\u670D\u52A1ID",type:"text",placeholder:"\u8F93\u5165\u7528\u6237\u670D\u52A1ID..."},{key:"orderStatus",label:"\u8BA2\u5355\u72B6\u6001",type:"select",options:[{label:"\u5F85\u652F\u4ED8",value:"pending"},{label:"\u5DF2\u652F\u4ED8",value:"paid"},{label:"\u5DF2\u5B8C\u6210",value:"completed"},{label:"\u5DF2\u53D6\u6D88",value:"cancelled"},{label:"\u5DF2\u9000\u6B3E",value:"refunded"}]},{key:"orderType",label:"\u8BA2\u5355\u7C7B\u578B",type:"select",options:[{label:"\u8D2D\u4E70",value:"purchase"},{label:"\u9000\u6B3E",value:"refund"}]}],p=(D,j)=>{k({sortBy:D,sortDesc:j==="desc",offset:0})},c=(D,j)=>{C(D,j)},g=D=>{n(D),o(!0)},B=()=>{o(!1),n(null)},v=[{key:"_id",label:"ID",type:"id",copyable:!0},{key:"orderID",label:"\u8BA2\u5355ID",type:"id",copyable:!0},{key:"userServiceID",label:"\u7528\u6237\u670D\u52A1ID",type:"id",copyable:!0},{key:"orderStatus",label:"\u8BA2\u5355\u72B6\u6001",type:"text"},{key:"orderType",label:"\u8BA2\u5355\u7C7B\u578B",type:"text"},{key:"createdAt",label:"\u521B\u5EFA\u65F6\u95F4",type:"timestamp"},{key:"updatedAt",label:"\u66F4\u65B0\u65F6\u95F4",type:"timestamp"},{key:"deletedAt",label:"\u5220\u9664\u65F6\u95F4",type:"timestamp"}],R=D=>{const j={offset:0,...D};k(j),t(!1)},b=()=>{w()},z=()=>{k({orderID:void 0,userServiceID:void 0,orderStatus:void 0,orderType:void 0,offset:0}),t(!1)};return S("div",{className:"space-y-6",children:[S("div",{className:"flex items-center justify-between",children:[S("div",{children:[S("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[d(aa,{className:"h-6 w-6 mr-2 text-blue-600"}),"\u8BA2\u5355\u670D\u52A1\u5173\u8054"]}),d("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u8BA2\u5355\u4E0E\u670D\u52A1\u7684\u5173\u8054\u5173\u7CFB"})]}),S("div",{className:"flex space-x-3",children:[S("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[d(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u663E\u793A\u7B5B\u9009"]}),S("button",{onClick:b,disabled:a,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[d(xr,{className:`h-4 w-4 mr-2 ${a?"animate-spin":""}`}),"\u5237\u65B0"]}),d(Sr,{data:i,columns:[{key:"_id",title:"ID"},{key:"orderID",title:"\u8BA2\u5355ID"},{key:"userServiceID",title:"\u7528\u6237\u670D\u52A1ID"},{key:"orderStatus",title:"\u8BA2\u5355\u72B6\u6001"},{key:"orderType",title:"\u8BA2\u5355\u7C7B\u578B"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:D=>De(D)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:D=>De(D)}],filename:"order_services",disabled:a||i.length===0})]})]}),e&&d("div",{className:"bg-white rounded-lg shadow p-6",children:d(Hr,{fields:m,onSubmit:R,onReset:z,loading:a,initialValues:h})}),d("div",{className:"bg-white rounded-lg shadow p-6",children:d("div",{className:"flex items-center justify-between",children:S("div",{className:"flex items-center space-x-4",children:[S("div",{className:"flex items-center",children:[d(aa,{className:"h-5 w-5 text-blue-500 mr-2"}),S("span",{className:"text-sm font-medium text-gray-900",children:["\u603B\u5173\u8054\u6570: ",u.toLocaleString()]})]}),h.orderID&&S("div",{className:"text-sm text-gray-600",children:["\u8BA2\u5355ID: ",h.orderID]}),h.userServiceID&&S("div",{className:"text-sm text-gray-600",children:["\u670D\u52A1ID: ",h.userServiceID]}),h.orderStatus&&S("div",{className:"text-sm text-gray-600",children:["\u72B6\u6001: ",h.orderStatus]})]})})}),d("div",{className:"bg-white rounded-lg shadow",children:d(kr,{columns:A,data:i,loading:a,error:s||void 0,pagination:{current:f,pageSize:y,total:u,onChange:c,showSizeChanger:!0,showQuickJumper:!0,showTotal:(D,j)=>`\u663E\u793A ${j[0]}-${j[1]} \u6761\uFF0C\u5171 ${D} \u6761\u8BB0\u5F55`},onSort:p,sortBy:h.sortBy,sortDesc:h.sortDesc,onRowClick:g})}),d(ms,{isOpen:l,onClose:B,title:"\u8BA2\u5355\u670D\u52A1\u5173\u8054\u8BE6\u60C5",data:r,fields:v,icon:d(aa,{className:"h-6 w-6 text-blue-600"})})]})},Z1=()=>{const[e,t]=E.exports.useState(!1),{data:r,total:n,loading:l,error:o,page:i,pageSize:u,params:a,refetch:s,updateParams:f,changePage:y}=O1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),h=[{key:"version",title:"\u7248\u672C\u53F7",dataIndex:"version",width:150,sortable:!0,render:c=>S("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-sm font-mono bg-blue-100 text-blue-800",children:[d(h1,{className:"h-3 w-3 mr-1"}),c]})},{key:"minimalSupported",title:"\u6700\u4F4E\u652F\u6301\u7248\u672C",dataIndex:"minimalSupported",width:150,render:c=>d("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-sm font-mono bg-gray-100 text-gray-800",children:c||"-"})},{key:"changeLog",title:"\u66F4\u65B0\u65E5\u5FD7",dataIndex:"changeLog",render:c=>d("div",{className:"max-w-md",children:d("p",{className:"text-sm text-gray-900 line-clamp-3",children:c||"\u6682\u65E0\u66F4\u65B0\u65E5\u5FD7"})})},{key:"createdAt",title:"\u53D1\u5E03\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:c=>d("span",{className:"text-sm text-gray-600",children:c?new Date(c*1e3).toLocaleString():"-"})},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",dataIndex:"updatedAt",width:160,render:c=>d("span",{className:"text-sm text-gray-600",children:c?new Date(c*1e3).toLocaleString():"-"})}],w=[{key:"version",label:"\u7248\u672C\u53F7",type:"text",placeholder:"\u8F93\u5165\u7248\u672C\u53F7..."},{key:"minimalSupported",label:"\u6700\u4F4E\u652F\u6301\u7248\u672C",type:"text",placeholder:"\u8F93\u5165\u6700\u4F4E\u652F\u6301\u7248\u672C..."}],k=(c,g)=>{f({sortBy:c,sortDesc:g==="desc",offset:0})},C=(c,g)=>{y(c,g)},A=c=>{f({offset:0,...c}),t(!1)},m=()=>{s()},p=()=>{f({offset:0,limit:20,sortBy:"createdAt",sortDesc:!0}),t(!1)};return S("div",{className:"space-y-6",children:[S("div",{className:"flex items-center justify-between",children:[S("div",{children:[d("h1",{className:"text-2xl font-bold text-gray-900",children:"CLI\u7248\u672C\u7BA1\u7406"}),d("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406CLI\u7248\u672C\u4FE1\u606F\u548C\u66F4\u65B0\u65E5\u5FD7"})]}),S("div",{className:"flex space-x-3",children:[S("button",{onClick:m,disabled:l,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:[d(xr,{className:`h-4 w-4 mr-2 ${l?"animate-spin":""}`}),"\u5237\u65B0"]}),S("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[d(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u7B5B\u9009"]}),d(Sr,{data:r,columns:[{key:"_id",title:"ID"},{key:"version",title:"\u7248\u672C\u53F7"},{key:"minimalSupported",title:"\u6700\u4F4E\u652F\u6301\u7248\u672C"},{key:"changeLog",title:"\u66F4\u65B0\u65E5\u5FD7"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:c=>De(c)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:c=>De(c)}],filename:"cli_versions",disabled:l||r.length===0})]})]}),e&&d(Hr,{fields:w,onSubmit:A,onReset:p,loading:l}),o&&d("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:d("div",{className:"flex",children:S("div",{className:"ml-3",children:[d("h3",{className:"text-sm font-medium text-red-800",children:"\u52A0\u8F7D\u6570\u636E\u65F6\u51FA\u9519"}),d("div",{className:"mt-2 text-sm text-red-700",children:d("p",{children:o})}),d("div",{className:"mt-4",children:d("button",{onClick:m,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"\u91CD\u8BD5"})})]})})}),d(kr,{columns:h,data:r,loading:l,pagination:{current:i,pageSize:u,total:n,onChange:C},sortConfig:{key:a.sortBy||"createdAt",direction:a.sortDesc?"desc":"asc"},onSort:k,rowKey:"_id",onRowClick:c=>{console.log("\u70B9\u51FBCLI\u7248\u672C:",c)},emptyText:o?"\u52A0\u8F7D\u5931\u8D25":"\u6682\u65E0CLI\u7248\u672C\u6570\u636E"})]})},ex=()=>{const[e,t]=E.exports.useState(!1),{data:r,total:n,loading:l,error:o,page:i,pageSize:u,params:a,refetch:s,updateParams:f,changePage:y,lastRefresh:h,autoRefresh:w,refreshInterval:k,setAutoRefresh:C}=$1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),A=[{key:"nameOrId",title:"\u8D27\u5E01\u540D\u79F0/ID",dataIndex:"nameOrId",width:150,sortable:!0,render:R=>S("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-sm font-medium bg-green-100 text-green-800",children:[d(Pv,{className:"h-3 w-3 mr-1"}),R]})},{key:"symbolName",title:"\u7B26\u53F7\u540D\u79F0",dataIndex:"symbolName",width:120,render:R=>d("span",{className:"font-mono text-sm font-bold text-blue-600",children:R||"-"})},{key:"contractId",title:"\u5408\u7EA6ID",dataIndex:"contractId",width:200,render:R=>d("span",{className:"font-mono text-xs text-gray-600",children:R?`${R.slice(0,10)}...${R.slice(-8)}`:"-"})},{key:"contractType",title:"\u5408\u7EA6\u7C7B\u578B",dataIndex:"contractType",width:120,render:R=>d("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${R==="token"?"bg-blue-100 text-blue-800":R==="nft"?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"}`,children:R||"-"})},{key:"unit",title:"\u5355\u4F4D",dataIndex:"unit",width:100,align:"right",render:R=>d("span",{className:"font-medium",children:R||0})},{key:"exchangeRate",title:"\u6C47\u7387",dataIndex:"exchangeRate",width:120,align:"right",render:R=>d("span",{className:"font-medium text-green-600",children:R?R.toFixed(6):"0.000000"})},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:R=>d("span",{className:"text-sm text-gray-600",children:R?new Date(R*1e3).toLocaleString():"-"})}],m=[{key:"nameOrId",label:"\u8D27\u5E01\u540D\u79F0/ID",type:"text",placeholder:"\u8F93\u5165\u8D27\u5E01\u540D\u79F0\u6216ID..."},{key:"symbolName",label:"\u7B26\u53F7\u540D\u79F0",type:"text",placeholder:"\u8F93\u5165\u7B26\u53F7\u540D\u79F0..."},{key:"contractType",label:"\u5408\u7EA6\u7C7B\u578B",type:"select",options:[{label:"Token",value:"token"},{label:"NFT",value:"nft"}]},{key:"contractId",label:"\u5408\u7EA6ID",type:"text",placeholder:"\u8F93\u5165\u5408\u7EA6ID..."}],p=(R,b)=>{f({sortBy:R,sortDesc:b==="desc",offset:0})},c=(R,b)=>{y(R,b)},g=R=>{f({offset:0,...R}),t(!1)},B=()=>{s()},v=()=>{f({offset:0,limit:20,sortBy:"createdAt",sortDesc:!0}),t(!1)};return S("div",{className:"space-y-6",children:[S("div",{className:"flex items-center justify-between",children:[S("div",{children:[d("h1",{className:"text-2xl font-bold text-gray-900",children:"\u8D27\u5E01\u7BA1\u7406"}),d("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u8D27\u5E01\u4FE1\u606F\u548C\u6C47\u7387\u8BBE\u7F6E"})]}),S("div",{className:"flex items-center space-x-3",children:[d(r0,{onRefresh:B,loading:l,lastRefresh:h,autoRefresh:w,refreshInterval:k,onAutoRefreshChange:C}),S("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[d(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u7B5B\u9009"]}),d(Sr,{data:r,columns:[{key:"_id",title:"ID"},{key:"nameOrId",title:"\u8D27\u5E01\u540D\u79F0/ID"},{key:"contractId",title:"\u5408\u7EA6ID"},{key:"symbolName",title:"\u7B26\u53F7\u540D\u79F0"},{key:"contractType",title:"\u5408\u7EA6\u7C7B\u578B"},{key:"unit",title:"\u5355\u4F4D"},{key:"exchangeRate",title:"\u6C47\u7387"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:R=>De(R)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:R=>De(R)}],filename:"currencies",disabled:l||r.length===0})]})]}),e&&d(Hr,{fields:m,onSubmit:g,onReset:v,loading:l}),o&&d("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:d("div",{className:"flex",children:S("div",{className:"ml-3",children:[d("h3",{className:"text-sm font-medium text-red-800",children:"\u52A0\u8F7D\u6570\u636E\u65F6\u51FA\u9519"}),d("div",{className:"mt-2 text-sm text-red-700",children:d("p",{children:o})}),d("div",{className:"mt-4",children:d("button",{onClick:B,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"\u91CD\u8BD5"})})]})})}),d(kr,{columns:A,data:r,loading:l,pagination:{current:i,pageSize:u,total:n,onChange:c},sortConfig:{key:a.sortBy||"createdAt",direction:a.sortDesc?"desc":"asc"},onSort:p,rowKey:"_id",onRowClick:R=>{console.log("\u70B9\u51FB\u8D27\u5E01:",R)},emptyText:o?"\u52A0\u8F7D\u5931\u8D25":"\u6682\u65E0\u8D27\u5E01\u6570\u636E"})]})},tx=()=>{const[e,t]=E.exports.useState(!1);E.exports.useState(null),E.exports.useState(!1);const{data:r,total:n,loading:l,error:o,page:i,pageSize:u,params:a,refetch:s,updateParams:f,changePage:y}=j1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),h=[{key:"_id",title:"ID",dataIndex:"_id",width:120,render:c=>S("span",{className:"font-mono text-xs text-gray-600",children:[c.substring(0,8),"..."]})},{key:"name",title:"\u5206\u7C7B\u540D\u79F0",dataIndex:"name",width:150,sortable:!0,render:c=>d("span",{className:"font-medium text-gray-900",children:c})},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546",dataIndex:"provider",width:150,sortable:!0,render:c=>d("span",{className:"text-blue-600 font-medium",children:c})},{key:"description",title:"\u63CF\u8FF0",dataIndex:"description",width:200,render:c=>d("span",{className:"text-sm text-gray-600 truncate",title:c,children:c||"-"})},{key:"apiHost",title:"API\u4E3B\u673A",dataIndex:"apiHost",width:180,render:c=>d("span",{className:"font-mono text-xs text-green-600",children:c||"-"})},{key:"serviceOptions",title:"\u670D\u52A1\u9009\u9879",dataIndex:"serviceOptions",width:150,render:c=>{const g=c?Object.keys(c).length:0;return S("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:[g," \u4E2A\u9009\u9879"]})}},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:c=>d("span",{className:"text-sm text-gray-600",children:new Date(c*1e3).toLocaleString()})},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",dataIndex:"updatedAt",width:160,sortable:!0,render:c=>d("span",{className:"text-sm text-gray-600",children:new Date(c*1e3).toLocaleString()})}],w=[{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u63D0\u4F9B\u5546\u540D\u79F0..."},{key:"name",label:"\u5206\u7C7B\u540D\u79F0",type:"text",placeholder:"\u8F93\u5165\u5206\u7C7B\u540D\u79F0..."}],k=(c,g)=>{f({sortBy:c,sortDesc:g==="desc",offset:0})},C=(c,g)=>{y(c,g)},A=c=>{const g={offset:0,...c};f(g),t(!1)},m=()=>{s()},p=()=>{f({provider:void 0,name:void 0,offset:0}),t(!1)};return S("div",{className:"space-y-6",children:[S("div",{className:"flex items-center justify-between",children:[S("div",{children:[S("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[d(iu,{className:"h-6 w-6 mr-2 text-blue-600"}),"\u670D\u52A1\u5206\u7C7B\u7BA1\u7406"]}),d("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u670D\u52A1\u5206\u7C7B\u548C\u914D\u7F6E\u9009\u9879"})]}),S("div",{className:"flex space-x-3",children:[S("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[d(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u663E\u793A\u7B5B\u9009"]}),S("button",{onClick:m,disabled:l,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[d(xr,{className:`h-4 w-4 mr-2 ${l?"animate-spin":""}`}),"\u5237\u65B0"]}),d(Sr,{data:r,columns:[{key:"_id",title:"ID"},{key:"name",title:"\u5206\u7C7B\u540D\u79F0"},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546"},{key:"description",title:"\u63CF\u8FF0"},{key:"apiHost",title:"API\u4E3B\u673A"},{key:"serviceOptions",title:"\u670D\u52A1\u9009\u9879",render:c=>xn(c)},{key:"name2ID",title:"\u540D\u79F0\u6620\u5C04",render:c=>xn(c)},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:c=>De(c)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:c=>De(c)}],filename:"service_categories",disabled:l||r.length===0})]})]}),e&&d("div",{className:"bg-white rounded-lg shadow p-6",children:d(Hr,{fields:w,onSubmit:A,onReset:p,loading:l,initialValues:a})}),d("div",{className:"bg-white rounded-lg shadow p-6",children:d("div",{className:"flex items-center justify-between",children:S("div",{className:"flex items-center space-x-4",children:[S("div",{className:"flex items-center",children:[d(iu,{className:"h-5 w-5 text-blue-500 mr-2"}),S("span",{className:"text-sm font-medium text-gray-900",children:["\u603B\u5206\u7C7B\u6570: ",n.toLocaleString()]})]}),a.provider&&S("div",{className:"text-sm text-gray-600",children:["\u63D0\u4F9B\u5546: ",a.provider]}),a.name&&S("div",{className:"text-sm text-gray-600",children:["\u5206\u7C7B: ",a.name]})]})})}),d("div",{className:"bg-white rounded-lg shadow",children:d(kr,{columns:h,data:r,loading:l,error:o||void 0,pagination:{current:i,pageSize:u,total:n,onChange:C,showSizeChanger:!0,showQuickJumper:!0,showTotal:(c,g)=>`\u663E\u793A ${g[0]}-${g[1]} \u6761\uFF0C\u5171 ${c} \u6761\u8BB0\u5F55`},onSort:k,sortBy:a.sortBy,sortDesc:a.sortDesc})})]})},rx=()=>{const[e,t]=E.exports.useState(!1),{data:r,total:n,loading:l,error:o,page:i,pageSize:u,params:a,refetch:s,updateParams:f,changePage:y}=U1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),h=[{key:"_id",title:"ID",dataIndex:"_id",width:120,render:c=>S("span",{className:"font-mono text-xs text-gray-600",children:[c.substring(0,8),"..."]})},{key:"name",title:"\u63D0\u4F9B\u5546\u540D\u79F0",dataIndex:"name",width:150,sortable:!0,render:c=>d("span",{className:"font-medium text-gray-900",children:c})},{key:"walletAddress",title:"\u94B1\u5305\u5730\u5740",dataIndex:"walletAddress",width:180,sortable:!0,render:c=>S("span",{className:"font-mono text-xs text-blue-600",children:[c.substring(0,12),"..."]})},{key:"publickey",title:"\u516C\u94A5",dataIndex:"publickey",width:150,render:c=>d("span",{className:"font-mono text-xs text-green-600",children:c?`${c.substring(0,12)}...`:"-"})},{key:"signAddress",title:"\u7B7E\u540D\u5730\u5740",dataIndex:"signAddress",width:150,render:c=>d("span",{className:"font-mono text-xs text-purple-600",children:c?`${c.substring(0,12)}...`:"-"})},{key:"apiHost",title:"API\u4E3B\u673A",dataIndex:"apiHost",width:180,render:c=>d("span",{className:"font-mono text-xs text-green-600",children:c||"-"})},{key:"category2ID",title:"\u5206\u7C7B\u6620\u5C04",dataIndex:"category2ID",width:120,render:c=>{const g=c?Object.keys(c).length:0;return S("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:[g," \u4E2A\u5206\u7C7B"]})}},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:c=>d("span",{className:"text-sm text-gray-600",children:new Date(c*1e3).toLocaleString()})}],w=[{key:"name",label:"\u63D0\u4F9B\u5546\u540D\u79F0",type:"text",placeholder:"\u8F93\u5165\u63D0\u4F9B\u5546\u540D\u79F0..."},{key:"walletAddress",label:"\u94B1\u5305\u5730\u5740",type:"text",placeholder:"\u8F93\u5165\u94B1\u5305\u5730\u5740..."},{key:"publicKey",label:"\u516C\u94A5",type:"text",placeholder:"\u8F93\u5165\u516C\u94A5..."},{key:"signAddress",label:"\u7B7E\u540D\u5730\u5740",type:"text",placeholder:"\u8F93\u5165\u7B7E\u540D\u5730\u5740..."},{key:"apiHost",label:"API\u4E3B\u673A",type:"text",placeholder:"\u8F93\u5165API\u4E3B\u673A\u5730\u5740..."}],k=(c,g)=>{f({sortBy:c,sortDesc:g==="desc",offset:0})},C=(c,g)=>{y(c,g)},A=c=>{const g={offset:0,...c};f(g),t(!1)},m=()=>{s()},p=()=>{f({name:void 0,walletAddress:void 0,publicKey:void 0,signAddress:void 0,apiHost:void 0,offset:0}),t(!1)};return S("div",{className:"space-y-6",children:[S("div",{className:"flex items-center justify-between",children:[S("div",{children:[S("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[d(Zo,{className:"h-6 w-6 mr-2 text-blue-600"}),"\u670D\u52A1\u63D0\u4F9B\u5546\u7BA1\u7406"]}),d("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u670D\u52A1\u63D0\u4F9B\u5546\u4FE1\u606F\u548C\u914D\u7F6E"})]}),S("div",{className:"flex space-x-3",children:[S("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[d(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u663E\u793A\u7B5B\u9009"]}),S("button",{onClick:m,disabled:l,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[d(xr,{className:`h-4 w-4 mr-2 ${l?"animate-spin":""}`}),"\u5237\u65B0"]}),d(Sr,{data:r,columns:[{key:"_id",title:"ID"},{key:"name",title:"\u63D0\u4F9B\u5546\u540D\u79F0"},{key:"walletAddress",title:"\u94B1\u5305\u5730\u5740"},{key:"publickey",title:"\u516C\u94A5"},{key:"signAddress",title:"\u7B7E\u540D\u5730\u5740"},{key:"apiHost",title:"API\u4E3B\u673A"},{key:"category2ID",title:"\u5206\u7C7B\u6620\u5C04",render:c=>xn(c)},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:c=>De(c)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:c=>De(c)}],filename:"providers",disabled:l||r.length===0})]})]}),e&&d("div",{className:"bg-white rounded-lg shadow p-6",children:d(Hr,{fields:w,onSubmit:A,onReset:p,loading:l,initialValues:a})}),d("div",{className:"bg-white rounded-lg shadow p-6",children:d("div",{className:"flex items-center justify-between",children:S("div",{className:"flex items-center space-x-4",children:[S("div",{className:"flex items-center",children:[d(Zo,{className:"h-5 w-5 text-blue-500 mr-2"}),S("span",{className:"text-sm font-medium text-gray-900",children:["\u603B\u63D0\u4F9B\u5546\u6570: ",n.toLocaleString()]})]}),a.name&&S("div",{className:"text-sm text-gray-600",children:["\u540D\u79F0: ",a.name]}),a.walletAddress&&S("div",{className:"text-sm text-gray-600",children:["\u94B1\u5305: ",a.walletAddress]})]})})}),d("div",{className:"bg-white rounded-lg shadow",children:d(kr,{columns:h,data:r,loading:l,error:o||void 0,pagination:{current:i,pageSize:u,total:n,onChange:C,showSizeChanger:!0,showQuickJumper:!0,showTotal:(c,g)=>`\u663E\u793A ${g[0]}-${g[1]} \u6761\uFF0C\u5171 ${c} \u6761\u8BB0\u5F55`},onSort:k,sortBy:a.sortBy,sortDesc:a.sortDesc})})]})},nx=()=>{const[e,t]=E.exports.useState(!1),{data:r,total:n,loading:l,error:o,page:i,pageSize:u,params:a,refetch:s,updateParams:f,changePage:y}=V1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),h=[{key:"_id",title:"ID",dataIndex:"_id",width:120,render:c=>S("span",{className:"font-mono text-xs text-gray-600",children:[c.substring(0,8),"..."]})},{key:"name",title:"\u670D\u52A1\u7C7B\u578B\u540D\u79F0",dataIndex:"name",width:150,sortable:!0,render:c=>d("span",{className:"font-medium text-gray-900",children:c})},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546",dataIndex:"provider",width:150,sortable:!0,render:c=>d("span",{className:"text-blue-600 font-medium",children:c})},{key:"category",title:"\u670D\u52A1\u5206\u7C7B",dataIndex:"category",width:120,sortable:!0,render:c=>d("span",{className:"text-green-600 font-medium",children:c})},{key:"refundable",title:"\u53EF\u9000\u6B3E",dataIndex:"refundable",width:100,sortable:!0,render:c=>d("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${c?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:c?"\u662F":"\u5426"})},{key:"description",title:"\u63CF\u8FF0",dataIndex:"description",width:200,render:c=>d("span",{className:"text-sm text-gray-600 truncate",title:c,children:c||"-"})},{key:"apiHost",title:"API\u4E3B\u673A",dataIndex:"apiHost",width:180,render:c=>d("span",{className:"font-mono text-xs text-green-600",children:c||"-"})},{key:"durationToPrice",title:"\u4EF7\u683C\u8BBE\u7F6E",dataIndex:"durationToPrice",width:120,render:c=>{const g=c?c.length:0;return S("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800",children:[g," \u4E2A\u4EF7\u683C"]})}},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:c=>d("span",{className:"text-sm text-gray-600",children:new Date(c*1e3).toLocaleString()})}],w=[{key:"name",label:"\u670D\u52A1\u7C7B\u578B\u540D\u79F0",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u7C7B\u578B\u540D\u79F0..."},{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u63D0\u4F9B\u5546..."},{key:"category",label:"\u670D\u52A1\u5206\u7C7B",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u5206\u7C7B..."},{key:"categoryID",label:"\u5206\u7C7BID",type:"text",placeholder:"\u8F93\u5165\u5206\u7C7BID..."},{key:"refundable",label:"\u53EF\u9000\u6B3E",type:"select",options:[{label:"\u662F",value:"true"},{label:"\u5426",value:"false"}]}],k=(c,g)=>{f({sortBy:c,sortDesc:g==="desc",offset:0})},C=(c,g)=>{y(c,g)},A=c=>{const g={offset:0,...c};c.refundable&&(g.refundable=c.refundable==="true"),f(g),t(!1)},m=()=>{s()},p=()=>{f({name:void 0,provider:void 0,category:void 0,categoryID:void 0,refundable:void 0,offset:0}),t(!1)};return S("div",{className:"space-y-6",children:[S("div",{className:"flex items-center justify-between",children:[S("div",{children:[S("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[d(Fl,{className:"h-6 w-6 mr-2 text-blue-600"}),"\u670D\u52A1\u7C7B\u578B\u7BA1\u7406"]}),d("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u670D\u52A1\u7C7B\u578B\u5B9A\u4E49\u548C\u914D\u7F6E"})]}),S("div",{className:"flex space-x-3",children:[S("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[d(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u663E\u793A\u7B5B\u9009"]}),S("button",{onClick:m,disabled:l,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[d(xr,{className:`h-4 w-4 mr-2 ${l?"animate-spin":""}`}),"\u5237\u65B0"]}),d(Sr,{data:r,columns:[{key:"_id",title:"ID"},{key:"name",title:"\u670D\u52A1\u7C7B\u578B\u540D\u79F0"},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546"},{key:"category",title:"\u670D\u52A1\u5206\u7C7B"},{key:"categoryID",title:"\u5206\u7C7BID"},{key:"refundable",title:"\u53EF\u9000\u6B3E",render:c=>t0(c)},{key:"description",title:"\u63CF\u8FF0"},{key:"apiHost",title:"API\u4E3B\u673A"},{key:"serviceOptions",title:"\u670D\u52A1\u9009\u9879",render:c=>xn(c)},{key:"durationToPrice",title:"\u4EF7\u683C\u8BBE\u7F6E",render:c=>xn(c)},{key:"serviceOptionDesc",title:"\u9009\u9879\u63CF\u8FF0",render:c=>xn(c)},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:c=>De(c)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:c=>De(c)}],filename:"service_types",disabled:l||r.length===0})]})]}),e&&d("div",{className:"bg-white rounded-lg shadow p-6",children:d(Hr,{fields:w,onSubmit:A,onReset:p,loading:l,initialValues:a})}),d("div",{className:"bg-white rounded-lg shadow p-6",children:d("div",{className:"flex items-center justify-between",children:S("div",{className:"flex items-center space-x-4",children:[S("div",{className:"flex items-center",children:[d(Fl,{className:"h-5 w-5 text-blue-500 mr-2"}),S("span",{className:"text-sm font-medium text-gray-900",children:["\u603B\u670D\u52A1\u7C7B\u578B\u6570: ",n.toLocaleString()]})]}),a.name&&S("div",{className:"text-sm text-gray-600",children:["\u7C7B\u578B: ",a.name]}),a.provider&&S("div",{className:"text-sm text-gray-600",children:["\u63D0\u4F9B\u5546: ",a.provider]}),a.category&&S("div",{className:"text-sm text-gray-600",children:["\u5206\u7C7B: ",a.category]})]})})}),d("div",{className:"bg-white rounded-lg shadow",children:d(kr,{columns:h,data:r,loading:l,error:o||void 0,pagination:{current:i,pageSize:u,total:n,onChange:C,showSizeChanger:!0,showQuickJumper:!0,showTotal:(c,g)=>`\u663E\u793A ${g[0]}-${g[1]} \u6761\uFF0C\u5171 ${c} \u6761\u8BB0\u5F55`},onSort:k,sortBy:a.sortBy,sortDesc:a.sortDesc})})]})},lx=rv([{path:"/",element:d(Q1,{}),children:[{index:!0,element:d(Rg,{to:"/user-services",replace:!0})},{path:"dashboard",element:d(K1,{})},{path:"user-services",element:d(J1,{})},{path:"orders",element:d(X1,{})},{path:"order-services",element:d(q1,{})},{path:"cli-versions",element:d(Z1,{})},{path:"currencies",element:d(ex,{})},{path:"service-categories",element:d(tx,{})},{path:"providers",element:d(rx,{})},{path:"service-types",element:d(nx,{})}]}]);function ox(){return d(hv,{router:lx})}const ix=document.getElementById("root"),ax=kh(ix);ax.render(d(T0.StrictMode,{children:d(ox,{})}));
