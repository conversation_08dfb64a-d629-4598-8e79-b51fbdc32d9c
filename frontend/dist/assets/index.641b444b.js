var x0=Object.defineProperty;var w0=(e,t,r)=>t in e?x0(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Kr=(e,t,r)=>(w0(e,typeof t!="symbol"?t+"":t,r),r);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))n(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function r(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerpolicy&&(o.referrerPolicy=l.referrerpolicy),l.crossorigin==="use-credentials"?o.credentials="include":l.crossorigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(l){if(l.ep)return;l.ep=!0;const o=r(l);fetch(l.href,o)}})();function k0(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var C={exports:{}},ee={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fl=Symbol.for("react.element"),S0=Symbol.for("react.portal"),E0=Symbol.for("react.fragment"),C0=Symbol.for("react.strict_mode"),N0=Symbol.for("react.profiler"),D0=Symbol.for("react.provider"),F0=Symbol.for("react.context"),B0=Symbol.for("react.forward_ref"),R0=Symbol.for("react.suspense"),A0=Symbol.for("react.memo"),b0=Symbol.for("react.lazy"),Rs=Symbol.iterator;function P0(e){return e===null||typeof e!="object"?null:(e=Rs&&e[Rs]||e["@@iterator"],typeof e=="function"?e:null)}var pd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},md=Object.assign,yd={};function Dn(e,t,r){this.props=e,this.context=t,this.refs=yd,this.updater=r||pd}Dn.prototype.isReactComponent={};Dn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Dn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function gd(){}gd.prototype=Dn.prototype;function du(e,t,r){this.props=e,this.context=t,this.refs=yd,this.updater=r||pd}var fu=du.prototype=new gd;fu.constructor=du;md(fu,Dn.prototype);fu.isPureReactComponent=!0;var As=Array.isArray,vd=Object.prototype.hasOwnProperty,hu={current:null},xd={key:!0,ref:!0,__self:!0,__source:!0};function wd(e,t,r){var n,l={},o=null,i=null;if(t!=null)for(n in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(o=""+t.key),t)vd.call(t,n)&&!xd.hasOwnProperty(n)&&(l[n]=t[n]);var u=arguments.length-2;if(u===1)l.children=r;else if(1<u){for(var a=Array(u),s=0;s<u;s++)a[s]=arguments[s+2];l.children=a}if(e&&e.defaultProps)for(n in u=e.defaultProps,u)l[n]===void 0&&(l[n]=u[n]);return{$$typeof:Fl,type:e,key:o,ref:i,props:l,_owner:hu.current}}function _0(e,t){return{$$typeof:Fl,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function pu(e){return typeof e=="object"&&e!==null&&e.$$typeof===Fl}function L0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var bs=/\/+/g;function bi(e,t){return typeof e=="object"&&e!==null&&e.key!=null?L0(""+e.key):t.toString(36)}function uo(e,t,r,n,l){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case Fl:case S0:i=!0}}if(i)return i=e,l=l(i),e=n===""?"."+bi(i,0):n,As(l)?(r="",e!=null&&(r=e.replace(bs,"$&/")+"/"),uo(l,t,r,"",function(s){return s})):l!=null&&(pu(l)&&(l=_0(l,r+(!l.key||i&&i.key===l.key?"":(""+l.key).replace(bs,"$&/")+"/")+e)),t.push(l)),1;if(i=0,n=n===""?".":n+":",As(e))for(var u=0;u<e.length;u++){o=e[u];var a=n+bi(o,u);i+=uo(o,t,r,a,l)}else if(a=P0(e),typeof a=="function")for(e=a.call(e),u=0;!(o=e.next()).done;)o=o.value,a=n+bi(o,u++),i+=uo(o,t,r,a,l);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function Ul(e,t,r){if(e==null)return e;var n=[],l=0;return uo(e,n,"","",function(o){return t.call(r,o,l++)}),n}function I0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){(e._status===0||e._status===-1)&&(e._status=1,e._result=r)},function(r){(e._status===0||e._status===-1)&&(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var We={current:null},so={transition:null},M0={ReactCurrentDispatcher:We,ReactCurrentBatchConfig:so,ReactCurrentOwner:hu};function kd(){throw Error("act(...) is not supported in production builds of React.")}ee.Children={map:Ul,forEach:function(e,t,r){Ul(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return Ul(e,function(){t++}),t},toArray:function(e){return Ul(e,function(t){return t})||[]},only:function(e){if(!pu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};ee.Component=Dn;ee.Fragment=E0;ee.Profiler=N0;ee.PureComponent=du;ee.StrictMode=C0;ee.Suspense=R0;ee.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=M0;ee.act=kd;ee.cloneElement=function(e,t,r){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var n=md({},e.props),l=e.key,o=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,i=hu.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(a in t)vd.call(t,a)&&!xd.hasOwnProperty(a)&&(n[a]=t[a]===void 0&&u!==void 0?u[a]:t[a])}var a=arguments.length-2;if(a===1)n.children=r;else if(1<a){u=Array(a);for(var s=0;s<a;s++)u[s]=arguments[s+2];n.children=u}return{$$typeof:Fl,type:e.type,key:l,ref:o,props:n,_owner:i}};ee.createContext=function(e){return e={$$typeof:F0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:D0,_context:e},e.Consumer=e};ee.createElement=wd;ee.createFactory=function(e){var t=wd.bind(null,e);return t.type=e,t};ee.createRef=function(){return{current:null}};ee.forwardRef=function(e){return{$$typeof:B0,render:e}};ee.isValidElement=pu;ee.lazy=function(e){return{$$typeof:b0,_payload:{_status:-1,_result:e},_init:I0}};ee.memo=function(e,t){return{$$typeof:A0,type:e,compare:t===void 0?null:t}};ee.startTransition=function(e){var t=so.transition;so.transition={};try{e()}finally{so.transition=t}};ee.unstable_act=kd;ee.useCallback=function(e,t){return We.current.useCallback(e,t)};ee.useContext=function(e){return We.current.useContext(e)};ee.useDebugValue=function(){};ee.useDeferredValue=function(e){return We.current.useDeferredValue(e)};ee.useEffect=function(e,t){return We.current.useEffect(e,t)};ee.useId=function(){return We.current.useId()};ee.useImperativeHandle=function(e,t,r){return We.current.useImperativeHandle(e,t,r)};ee.useInsertionEffect=function(e,t){return We.current.useInsertionEffect(e,t)};ee.useLayoutEffect=function(e,t){return We.current.useLayoutEffect(e,t)};ee.useMemo=function(e,t){return We.current.useMemo(e,t)};ee.useReducer=function(e,t,r){return We.current.useReducer(e,t,r)};ee.useRef=function(e){return We.current.useRef(e)};ee.useState=function(e){return We.current.useState(e)};ee.useSyncExternalStore=function(e,t,r){return We.current.useSyncExternalStore(e,t,r)};ee.useTransition=function(){return We.current.useTransition()};ee.version="18.3.1";(function(e){e.exports=ee})(C);const T0=k0(C.exports);var mu={exports:{}},ot={},Sd={exports:{}},Ed={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(I,j){var V=I.length;I.push(j);e:for(;0<V;){var te=V-1>>>1,le=I[te];if(0<l(le,j))I[te]=j,I[V]=le,V=te;else break e}}function r(I){return I.length===0?null:I[0]}function n(I){if(I.length===0)return null;var j=I[0],V=I.pop();if(V!==j){I[0]=V;e:for(var te=0,le=I.length,ke=le>>>1;te<ke;){var $e=2*(te+1)-1,Wt=I[$e],je=$e+1,Hr=I[je];if(0>l(Wt,V))je<le&&0>l(Hr,Wt)?(I[te]=Hr,I[je]=V,te=je):(I[te]=Wt,I[$e]=V,te=$e);else if(je<le&&0>l(Hr,V))I[te]=Hr,I[je]=V,te=je;else break e}}return j}function l(I,j){var V=I.sortIndex-j.sortIndex;return V!==0?V:I.id-j.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,u=i.now();e.unstable_now=function(){return i.now()-u}}var a=[],s=[],d=1,y=null,p=3,x=!1,w=!1,N=!1,A=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function f(I){for(var j=r(s);j!==null;){if(j.callback===null)n(s);else if(j.startTime<=I)n(s),j.sortIndex=j.expirationTime,t(a,j);else break;j=r(s)}}function k(I){if(N=!1,f(I),!w)if(r(a)!==null)w=!0,Z(B);else{var j=r(s);j!==null&&Ce(k,j.startTime-I)}}function B(I,j){w=!1,N&&(N=!1,m(b),b=-1),x=!0;var V=p;try{for(f(j),y=r(a);y!==null&&(!(y.expirationTime>j)||I&&!G());){var te=y.callback;if(typeof te=="function"){y.callback=null,p=y.priorityLevel;var le=te(y.expirationTime<=j);j=e.unstable_now(),typeof le=="function"?y.callback=le:y===r(a)&&n(a),f(j)}else n(a);y=r(a)}if(y!==null)var ke=!0;else{var $e=r(s);$e!==null&&Ce(k,$e.startTime-j),ke=!1}return ke}finally{y=null,p=V,x=!1}}var g=!1,S=null,b=-1,z=5,$=-1;function G(){return!(e.unstable_now()-$<z)}function ge(){if(S!==null){var I=e.unstable_now();$=I;var j=!0;try{j=S(!0,I)}finally{j?we():(g=!1,S=null)}}else g=!1}var we;if(typeof h=="function")we=function(){h(ge)};else if(typeof MessageChannel<"u"){var R=new MessageChannel,L=R.port2;R.port1.onmessage=ge,we=function(){L.postMessage(null)}}else we=function(){A(ge,0)};function Z(I){S=I,g||(g=!0,we())}function Ce(I,j){b=A(function(){I(e.unstable_now())},j)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(I){I.callback=null},e.unstable_continueExecution=function(){w||x||(w=!0,Z(B))},e.unstable_forceFrameRate=function(I){0>I||125<I?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):z=0<I?Math.floor(1e3/I):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return r(a)},e.unstable_next=function(I){switch(p){case 1:case 2:case 3:var j=3;break;default:j=p}var V=p;p=j;try{return I()}finally{p=V}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(I,j){switch(I){case 1:case 2:case 3:case 4:case 5:break;default:I=3}var V=p;p=I;try{return j()}finally{p=V}},e.unstable_scheduleCallback=function(I,j,V){var te=e.unstable_now();switch(typeof V=="object"&&V!==null?(V=V.delay,V=typeof V=="number"&&0<V?te+V:te):V=te,I){case 1:var le=-1;break;case 2:le=250;break;case 5:le=**********;break;case 4:le=1e4;break;default:le=5e3}return le=V+le,I={id:d++,callback:j,priorityLevel:I,startTime:V,expirationTime:le,sortIndex:-1},V>te?(I.sortIndex=V,t(s,I),r(a)===null&&I===r(s)&&(N?(m(b),b=-1):N=!0,Ce(k,V-te))):(I.sortIndex=le,t(a,I),w||x||(w=!0,Z(B))),I},e.unstable_shouldYield=G,e.unstable_wrapCallback=function(I){var j=p;return function(){var V=p;p=j;try{return I.apply(this,arguments)}finally{p=V}}}})(Ed);(function(e){e.exports=Ed})(Sd);/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var z0=C.exports,lt=Sd.exports;function _(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Cd=new Set,al={};function $r(e,t){xn(e,t),xn(e+"Capture",t)}function xn(e,t){for(al[e]=t,e=0;e<t.length;e++)Cd.add(t[e])}var zt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),sa=Object.prototype.hasOwnProperty,O0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ps={},_s={};function $0(e){return sa.call(_s,e)?!0:sa.call(Ps,e)?!1:O0.test(e)?_s[e]=!0:(Ps[e]=!0,!1)}function j0(e,t,r,n){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return n?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function U0(e,t,r,n){if(t===null||typeof t>"u"||j0(e,t,r,n))return!0;if(n)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Qe(e,t,r,n,l,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=n,this.attributeNamespace=l,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var _e={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){_e[e]=new Qe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];_e[t]=new Qe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){_e[e]=new Qe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){_e[e]=new Qe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){_e[e]=new Qe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){_e[e]=new Qe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){_e[e]=new Qe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){_e[e]=new Qe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){_e[e]=new Qe(e,5,!1,e.toLowerCase(),null,!1,!1)});var yu=/[\-:]([a-z])/g;function gu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(yu,gu);_e[t]=new Qe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(yu,gu);_e[t]=new Qe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(yu,gu);_e[t]=new Qe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){_e[e]=new Qe(e,1,!1,e.toLowerCase(),null,!1,!1)});_e.xlinkHref=new Qe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){_e[e]=new Qe(e,1,!1,e.toLowerCase(),null,!0,!0)});function vu(e,t,r,n){var l=_e.hasOwnProperty(t)?_e[t]:null;(l!==null?l.type!==0:n||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(U0(t,r,l,n)&&(r=null),n||l===null?$0(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):l.mustUseProperty?e[l.propertyName]=r===null?l.type===3?!1:"":r:(t=l.attributeName,n=l.attributeNamespace,r===null?e.removeAttribute(t):(l=l.type,r=l===3||l===4&&r===!0?"":""+r,n?e.setAttributeNS(n,t,r):e.setAttribute(t,r))))}var Ut=z0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Vl=Symbol.for("react.element"),Xr=Symbol.for("react.portal"),qr=Symbol.for("react.fragment"),xu=Symbol.for("react.strict_mode"),ca=Symbol.for("react.profiler"),Nd=Symbol.for("react.provider"),Dd=Symbol.for("react.context"),wu=Symbol.for("react.forward_ref"),da=Symbol.for("react.suspense"),fa=Symbol.for("react.suspense_list"),ku=Symbol.for("react.memo"),Xt=Symbol.for("react.lazy"),Fd=Symbol.for("react.offscreen"),Ls=Symbol.iterator;function In(e){return e===null||typeof e!="object"?null:(e=Ls&&e[Ls]||e["@@iterator"],typeof e=="function"?e:null)}var pe=Object.assign,Pi;function Qn(e){if(Pi===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);Pi=t&&t[1]||""}return`
`+Pi+e}var _i=!1;function Li(e,t){if(!e||_i)return"";_i=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(s){var n=s}Reflect.construct(e,[],t)}else{try{t.call()}catch(s){n=s}e.call(t.prototype)}else{try{throw Error()}catch(s){n=s}e()}}catch(s){if(s&&n&&typeof s.stack=="string"){for(var l=s.stack.split(`
`),o=n.stack.split(`
`),i=l.length-1,u=o.length-1;1<=i&&0<=u&&l[i]!==o[u];)u--;for(;1<=i&&0<=u;i--,u--)if(l[i]!==o[u]){if(i!==1||u!==1)do if(i--,u--,0>u||l[i]!==o[u]){var a=`
`+l[i].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=i&&0<=u);break}}}finally{_i=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?Qn(e):""}function V0(e){switch(e.tag){case 5:return Qn(e.type);case 16:return Qn("Lazy");case 13:return Qn("Suspense");case 19:return Qn("SuspenseList");case 0:case 2:case 15:return e=Li(e.type,!1),e;case 11:return e=Li(e.type.render,!1),e;case 1:return e=Li(e.type,!0),e;default:return""}}function ha(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case qr:return"Fragment";case Xr:return"Portal";case ca:return"Profiler";case xu:return"StrictMode";case da:return"Suspense";case fa:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Dd:return(e.displayName||"Context")+".Consumer";case Nd:return(e._context.displayName||"Context")+".Provider";case wu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ku:return t=e.displayName||null,t!==null?t:ha(e.type)||"Memo";case Xt:t=e._payload,e=e._init;try{return ha(e(t))}catch{}}return null}function H0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ha(t);case 8:return t===xu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function fr(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Bd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function W0(e){var t=Bd(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var l=r.get,o=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(i){n=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return n},setValue:function(i){n=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Hl(e){e._valueTracker||(e._valueTracker=W0(e))}function Rd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),n="";return e&&(n=Bd(e)?e.checked?"true":"false":e.value),e=n,e!==r?(t.setValue(e),!0):!1}function Do(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function pa(e,t){var r=t.checked;return pe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r!=null?r:e._wrapperState.initialChecked})}function Is(e,t){var r=t.defaultValue==null?"":t.defaultValue,n=t.checked!=null?t.checked:t.defaultChecked;r=fr(t.value!=null?t.value:r),e._wrapperState={initialChecked:n,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Ad(e,t){t=t.checked,t!=null&&vu(e,"checked",t,!1)}function ma(e,t){Ad(e,t);var r=fr(t.value),n=t.type;if(r!=null)n==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(n==="submit"||n==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ya(e,t.type,r):t.hasOwnProperty("defaultValue")&&ya(e,t.type,fr(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ms(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var n=t.type;if(!(n!=="submit"&&n!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function ya(e,t,r){(t!=="number"||Do(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var Kn=Array.isArray;function dn(e,t,r,n){if(e=e.options,t){t={};for(var l=0;l<r.length;l++)t["$"+r[l]]=!0;for(r=0;r<e.length;r++)l=t.hasOwnProperty("$"+e[r].value),e[r].selected!==l&&(e[r].selected=l),l&&n&&(e[r].defaultSelected=!0)}else{for(r=""+fr(r),t=null,l=0;l<e.length;l++){if(e[l].value===r){e[l].selected=!0,n&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function ga(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(_(91));return pe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ts(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(_(92));if(Kn(r)){if(1<r.length)throw Error(_(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:fr(r)}}function bd(e,t){var r=fr(t.value),n=fr(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),n!=null&&(e.defaultValue=""+n)}function zs(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Pd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function va(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Pd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Wl,_d=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,n,l){MSApp.execUnsafeLocalFunction(function(){return e(t,r,n,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Wl=Wl||document.createElement("div"),Wl.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Wl.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ul(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var qn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Q0=["Webkit","ms","Moz","O"];Object.keys(qn).forEach(function(e){Q0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),qn[t]=qn[e]})});function Ld(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||qn.hasOwnProperty(e)&&qn[e]?(""+t).trim():t+"px"}function Id(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var n=r.indexOf("--")===0,l=Ld(r,t[r],n);r==="float"&&(r="cssFloat"),n?e.setProperty(r,l):e[r]=l}}var K0=pe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function xa(e,t){if(t){if(K0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(_(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(_(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(_(61))}if(t.style!=null&&typeof t.style!="object")throw Error(_(62))}}function wa(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ka=null;function Su(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Sa=null,fn=null,hn=null;function Os(e){if(e=Al(e)){if(typeof Sa!="function")throw Error(_(280));var t=e.stateNode;t&&(t=ci(t),Sa(e.stateNode,e.type,t))}}function Md(e){fn?hn?hn.push(e):hn=[e]:fn=e}function Td(){if(fn){var e=fn,t=hn;if(hn=fn=null,Os(e),t)for(e=0;e<t.length;e++)Os(t[e])}}function zd(e,t){return e(t)}function Od(){}var Ii=!1;function $d(e,t,r){if(Ii)return e(t,r);Ii=!0;try{return zd(e,t,r)}finally{Ii=!1,(fn!==null||hn!==null)&&(Od(),Td())}}function sl(e,t){var r=e.stateNode;if(r===null)return null;var n=ci(r);if(n===null)return null;r=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(_(231,t,typeof r));return r}var Ea=!1;if(zt)try{var Mn={};Object.defineProperty(Mn,"passive",{get:function(){Ea=!0}}),window.addEventListener("test",Mn,Mn),window.removeEventListener("test",Mn,Mn)}catch{Ea=!1}function Y0(e,t,r,n,l,o,i,u,a){var s=Array.prototype.slice.call(arguments,3);try{t.apply(r,s)}catch(d){this.onError(d)}}var Zn=!1,Fo=null,Bo=!1,Ca=null,G0={onError:function(e){Zn=!0,Fo=e}};function J0(e,t,r,n,l,o,i,u,a){Zn=!1,Fo=null,Y0.apply(G0,arguments)}function X0(e,t,r,n,l,o,i,u,a){if(J0.apply(this,arguments),Zn){if(Zn){var s=Fo;Zn=!1,Fo=null}else throw Error(_(198));Bo||(Bo=!0,Ca=s)}}function jr(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function jd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function $s(e){if(jr(e)!==e)throw Error(_(188))}function q0(e){var t=e.alternate;if(!t){if(t=jr(e),t===null)throw Error(_(188));return t!==e?null:e}for(var r=e,n=t;;){var l=r.return;if(l===null)break;var o=l.alternate;if(o===null){if(n=l.return,n!==null){r=n;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===r)return $s(l),e;if(o===n)return $s(l),t;o=o.sibling}throw Error(_(188))}if(r.return!==n.return)r=l,n=o;else{for(var i=!1,u=l.child;u;){if(u===r){i=!0,r=l,n=o;break}if(u===n){i=!0,n=l,r=o;break}u=u.sibling}if(!i){for(u=o.child;u;){if(u===r){i=!0,r=o,n=l;break}if(u===n){i=!0,n=o,r=l;break}u=u.sibling}if(!i)throw Error(_(189))}}if(r.alternate!==n)throw Error(_(190))}if(r.tag!==3)throw Error(_(188));return r.stateNode.current===r?e:t}function Ud(e){return e=q0(e),e!==null?Vd(e):null}function Vd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Vd(e);if(t!==null)return t;e=e.sibling}return null}var Hd=lt.unstable_scheduleCallback,js=lt.unstable_cancelCallback,Z0=lt.unstable_shouldYield,ep=lt.unstable_requestPaint,ve=lt.unstable_now,tp=lt.unstable_getCurrentPriorityLevel,Eu=lt.unstable_ImmediatePriority,Wd=lt.unstable_UserBlockingPriority,Ro=lt.unstable_NormalPriority,rp=lt.unstable_LowPriority,Qd=lt.unstable_IdlePriority,ii=null,Rt=null;function np(e){if(Rt&&typeof Rt.onCommitFiberRoot=="function")try{Rt.onCommitFiberRoot(ii,e,void 0,(e.current.flags&128)===128)}catch{}}var kt=Math.clz32?Math.clz32:ip,lp=Math.log,op=Math.LN2;function ip(e){return e>>>=0,e===0?32:31-(lp(e)/op|0)|0}var Ql=64,Kl=4194304;function Yn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ao(e,t){var r=e.pendingLanes;if(r===0)return 0;var n=0,l=e.suspendedLanes,o=e.pingedLanes,i=r&268435455;if(i!==0){var u=i&~l;u!==0?n=Yn(u):(o&=i,o!==0&&(n=Yn(o)))}else i=r&~l,i!==0?n=Yn(i):o!==0&&(n=Yn(o));if(n===0)return 0;if(t!==0&&t!==n&&(t&l)===0&&(l=n&-n,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if((n&4)!==0&&(n|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=n;0<t;)r=31-kt(t),l=1<<r,n|=e[r],t&=~l;return n}function ap(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function up(e,t){for(var r=e.suspendedLanes,n=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-kt(o),u=1<<i,a=l[i];a===-1?((u&r)===0||(u&n)!==0)&&(l[i]=ap(u,t)):a<=t&&(e.expiredLanes|=u),o&=~u}}function Na(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Kd(){var e=Ql;return Ql<<=1,(Ql&4194240)===0&&(Ql=64),e}function Mi(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function Bl(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-kt(t),e[t]=r}function sp(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var n=e.eventTimes;for(e=e.expirationTimes;0<r;){var l=31-kt(r),o=1<<l;t[l]=0,n[l]=-1,e[l]=-1,r&=~o}}function Cu(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var n=31-kt(r),l=1<<n;l&t|e[n]&t&&(e[n]|=t),r&=~l}}var oe=0;function Yd(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Gd,Nu,Jd,Xd,qd,Da=!1,Yl=[],lr=null,or=null,ir=null,cl=new Map,dl=new Map,Zt=[],cp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Us(e,t){switch(e){case"focusin":case"focusout":lr=null;break;case"dragenter":case"dragleave":or=null;break;case"mouseover":case"mouseout":ir=null;break;case"pointerover":case"pointerout":cl.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":dl.delete(t.pointerId)}}function Tn(e,t,r,n,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:r,eventSystemFlags:n,nativeEvent:o,targetContainers:[l]},t!==null&&(t=Al(t),t!==null&&Nu(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function dp(e,t,r,n,l){switch(t){case"focusin":return lr=Tn(lr,e,t,r,n,l),!0;case"dragenter":return or=Tn(or,e,t,r,n,l),!0;case"mouseover":return ir=Tn(ir,e,t,r,n,l),!0;case"pointerover":var o=l.pointerId;return cl.set(o,Tn(cl.get(o)||null,e,t,r,n,l)),!0;case"gotpointercapture":return o=l.pointerId,dl.set(o,Tn(dl.get(o)||null,e,t,r,n,l)),!0}return!1}function Zd(e){var t=Rr(e.target);if(t!==null){var r=jr(t);if(r!==null){if(t=r.tag,t===13){if(t=jd(r),t!==null){e.blockedOn=t,qd(e.priority,function(){Jd(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function co(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=Fa(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var n=new r.constructor(r.type,r);ka=n,r.target.dispatchEvent(n),ka=null}else return t=Al(r),t!==null&&Nu(t),e.blockedOn=r,!1;t.shift()}return!0}function Vs(e,t,r){co(e)&&r.delete(t)}function fp(){Da=!1,lr!==null&&co(lr)&&(lr=null),or!==null&&co(or)&&(or=null),ir!==null&&co(ir)&&(ir=null),cl.forEach(Vs),dl.forEach(Vs)}function zn(e,t){e.blockedOn===t&&(e.blockedOn=null,Da||(Da=!0,lt.unstable_scheduleCallback(lt.unstable_NormalPriority,fp)))}function fl(e){function t(l){return zn(l,e)}if(0<Yl.length){zn(Yl[0],e);for(var r=1;r<Yl.length;r++){var n=Yl[r];n.blockedOn===e&&(n.blockedOn=null)}}for(lr!==null&&zn(lr,e),or!==null&&zn(or,e),ir!==null&&zn(ir,e),cl.forEach(t),dl.forEach(t),r=0;r<Zt.length;r++)n=Zt[r],n.blockedOn===e&&(n.blockedOn=null);for(;0<Zt.length&&(r=Zt[0],r.blockedOn===null);)Zd(r),r.blockedOn===null&&Zt.shift()}var pn=Ut.ReactCurrentBatchConfig,bo=!0;function hp(e,t,r,n){var l=oe,o=pn.transition;pn.transition=null;try{oe=1,Du(e,t,r,n)}finally{oe=l,pn.transition=o}}function pp(e,t,r,n){var l=oe,o=pn.transition;pn.transition=null;try{oe=4,Du(e,t,r,n)}finally{oe=l,pn.transition=o}}function Du(e,t,r,n){if(bo){var l=Fa(e,t,r,n);if(l===null)Qi(e,t,n,Po,r),Us(e,n);else if(dp(l,e,t,r,n))n.stopPropagation();else if(Us(e,n),t&4&&-1<cp.indexOf(e)){for(;l!==null;){var o=Al(l);if(o!==null&&Gd(o),o=Fa(e,t,r,n),o===null&&Qi(e,t,n,Po,r),o===l)break;l=o}l!==null&&n.stopPropagation()}else Qi(e,t,n,null,r)}}var Po=null;function Fa(e,t,r,n){if(Po=null,e=Su(n),e=Rr(e),e!==null)if(t=jr(e),t===null)e=null;else if(r=t.tag,r===13){if(e=jd(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Po=e,null}function ef(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(tp()){case Eu:return 1;case Wd:return 4;case Ro:case rp:return 16;case Qd:return 536870912;default:return 16}default:return 16}}var rr=null,Fu=null,fo=null;function tf(){if(fo)return fo;var e,t=Fu,r=t.length,n,l="value"in rr?rr.value:rr.textContent,o=l.length;for(e=0;e<r&&t[e]===l[e];e++);var i=r-e;for(n=1;n<=i&&t[r-n]===l[o-n];n++);return fo=l.slice(e,1<n?1-n:void 0)}function ho(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Gl(){return!0}function Hs(){return!1}function it(e){function t(r,n,l,o,i){this._reactName=r,this._targetInst=l,this.type=n,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var u in e)e.hasOwnProperty(u)&&(r=e[u],this[u]=r?r(o):o[u]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Gl:Hs,this.isPropagationStopped=Hs,this}return pe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=Gl)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=Gl)},persist:function(){},isPersistent:Gl}),t}var Fn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Bu=it(Fn),Rl=pe({},Fn,{view:0,detail:0}),mp=it(Rl),Ti,zi,On,ai=pe({},Rl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ru,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==On&&(On&&e.type==="mousemove"?(Ti=e.screenX-On.screenX,zi=e.screenY-On.screenY):zi=Ti=0,On=e),Ti)},movementY:function(e){return"movementY"in e?e.movementY:zi}}),Ws=it(ai),yp=pe({},ai,{dataTransfer:0}),gp=it(yp),vp=pe({},Rl,{relatedTarget:0}),Oi=it(vp),xp=pe({},Fn,{animationName:0,elapsedTime:0,pseudoElement:0}),wp=it(xp),kp=pe({},Fn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Sp=it(kp),Ep=pe({},Fn,{data:0}),Qs=it(Ep),Cp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Np={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Dp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Fp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Dp[e])?!!t[e]:!1}function Ru(){return Fp}var Bp=pe({},Rl,{key:function(e){if(e.key){var t=Cp[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ho(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Np[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ru,charCode:function(e){return e.type==="keypress"?ho(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ho(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Rp=it(Bp),Ap=pe({},ai,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ks=it(Ap),bp=pe({},Rl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ru}),Pp=it(bp),_p=pe({},Fn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Lp=it(_p),Ip=pe({},ai,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Mp=it(Ip),Tp=[9,13,27,32],Au=zt&&"CompositionEvent"in window,el=null;zt&&"documentMode"in document&&(el=document.documentMode);var zp=zt&&"TextEvent"in window&&!el,rf=zt&&(!Au||el&&8<el&&11>=el),Ys=String.fromCharCode(32),Gs=!1;function nf(e,t){switch(e){case"keyup":return Tp.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function lf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Zr=!1;function Op(e,t){switch(e){case"compositionend":return lf(t);case"keypress":return t.which!==32?null:(Gs=!0,Ys);case"textInput":return e=t.data,e===Ys&&Gs?null:e;default:return null}}function $p(e,t){if(Zr)return e==="compositionend"||!Au&&nf(e,t)?(e=tf(),fo=Fu=rr=null,Zr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return rf&&t.locale!=="ko"?null:t.data;default:return null}}var jp={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Js(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!jp[e.type]:t==="textarea"}function of(e,t,r,n){Md(n),t=_o(t,"onChange"),0<t.length&&(r=new Bu("onChange","change",null,r,n),e.push({event:r,listeners:t}))}var tl=null,hl=null;function Up(e){gf(e,0)}function ui(e){var t=rn(e);if(Rd(t))return e}function Vp(e,t){if(e==="change")return t}var af=!1;if(zt){var $i;if(zt){var ji="oninput"in document;if(!ji){var Xs=document.createElement("div");Xs.setAttribute("oninput","return;"),ji=typeof Xs.oninput=="function"}$i=ji}else $i=!1;af=$i&&(!document.documentMode||9<document.documentMode)}function qs(){tl&&(tl.detachEvent("onpropertychange",uf),hl=tl=null)}function uf(e){if(e.propertyName==="value"&&ui(hl)){var t=[];of(t,hl,e,Su(e)),$d(Up,t)}}function Hp(e,t,r){e==="focusin"?(qs(),tl=t,hl=r,tl.attachEvent("onpropertychange",uf)):e==="focusout"&&qs()}function Wp(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ui(hl)}function Qp(e,t){if(e==="click")return ui(t)}function Kp(e,t){if(e==="input"||e==="change")return ui(t)}function Yp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Et=typeof Object.is=="function"?Object.is:Yp;function pl(e,t){if(Et(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++){var l=r[n];if(!sa.call(t,l)||!Et(e[l],t[l]))return!1}return!0}function Zs(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ec(e,t){var r=Zs(e);e=0;for(var n;r;){if(r.nodeType===3){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Zs(r)}}function sf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?sf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function cf(){for(var e=window,t=Do();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=Do(e.document)}return t}function bu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Gp(e){var t=cf(),r=e.focusedElem,n=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&sf(r.ownerDocument.documentElement,r)){if(n!==null&&bu(r)){if(t=n.start,e=n.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=r.textContent.length,o=Math.min(n.start,l);n=n.end===void 0?o:Math.min(n.end,l),!e.extend&&o>n&&(l=n,n=o,o=l),l=ec(r,o);var i=ec(r,n);l&&i&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>n?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Jp=zt&&"documentMode"in document&&11>=document.documentMode,en=null,Ba=null,rl=null,Ra=!1;function tc(e,t,r){var n=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;Ra||en==null||en!==Do(n)||(n=en,"selectionStart"in n&&bu(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),rl&&pl(rl,n)||(rl=n,n=_o(Ba,"onSelect"),0<n.length&&(t=new Bu("onSelect","select",null,t,r),e.push({event:t,listeners:n}),t.target=en)))}function Jl(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var tn={animationend:Jl("Animation","AnimationEnd"),animationiteration:Jl("Animation","AnimationIteration"),animationstart:Jl("Animation","AnimationStart"),transitionend:Jl("Transition","TransitionEnd")},Ui={},df={};zt&&(df=document.createElement("div").style,"AnimationEvent"in window||(delete tn.animationend.animation,delete tn.animationiteration.animation,delete tn.animationstart.animation),"TransitionEvent"in window||delete tn.transitionend.transition);function si(e){if(Ui[e])return Ui[e];if(!tn[e])return e;var t=tn[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in df)return Ui[e]=t[r];return e}var ff=si("animationend"),hf=si("animationiteration"),pf=si("animationstart"),mf=si("transitionend"),yf=new Map,rc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function mr(e,t){yf.set(e,t),$r(t,[e])}for(var Vi=0;Vi<rc.length;Vi++){var Hi=rc[Vi],Xp=Hi.toLowerCase(),qp=Hi[0].toUpperCase()+Hi.slice(1);mr(Xp,"on"+qp)}mr(ff,"onAnimationEnd");mr(hf,"onAnimationIteration");mr(pf,"onAnimationStart");mr("dblclick","onDoubleClick");mr("focusin","onFocus");mr("focusout","onBlur");mr(mf,"onTransitionEnd");xn("onMouseEnter",["mouseout","mouseover"]);xn("onMouseLeave",["mouseout","mouseover"]);xn("onPointerEnter",["pointerout","pointerover"]);xn("onPointerLeave",["pointerout","pointerover"]);$r("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));$r("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));$r("onBeforeInput",["compositionend","keypress","textInput","paste"]);$r("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));$r("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));$r("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Gn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Zp=new Set("cancel close invalid load scroll toggle".split(" ").concat(Gn));function nc(e,t,r){var n=e.type||"unknown-event";e.currentTarget=r,X0(n,t,void 0,e),e.currentTarget=null}function gf(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var n=e[r],l=n.event;n=n.listeners;e:{var o=void 0;if(t)for(var i=n.length-1;0<=i;i--){var u=n[i],a=u.instance,s=u.currentTarget;if(u=u.listener,a!==o&&l.isPropagationStopped())break e;nc(l,u,s),o=a}else for(i=0;i<n.length;i++){if(u=n[i],a=u.instance,s=u.currentTarget,u=u.listener,a!==o&&l.isPropagationStopped())break e;nc(l,u,s),o=a}}}if(Bo)throw e=Ca,Bo=!1,Ca=null,e}function se(e,t){var r=t[La];r===void 0&&(r=t[La]=new Set);var n=e+"__bubble";r.has(n)||(vf(t,e,2,!1),r.add(n))}function Wi(e,t,r){var n=0;t&&(n|=4),vf(r,e,n,t)}var Xl="_reactListening"+Math.random().toString(36).slice(2);function ml(e){if(!e[Xl]){e[Xl]=!0,Cd.forEach(function(r){r!=="selectionchange"&&(Zp.has(r)||Wi(r,!1,e),Wi(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Xl]||(t[Xl]=!0,Wi("selectionchange",!1,t))}}function vf(e,t,r,n){switch(ef(t)){case 1:var l=hp;break;case 4:l=pp;break;default:l=Du}r=l.bind(null,t,r,e),l=void 0,!Ea||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),n?l!==void 0?e.addEventListener(t,r,{capture:!0,passive:l}):e.addEventListener(t,r,!0):l!==void 0?e.addEventListener(t,r,{passive:l}):e.addEventListener(t,r,!1)}function Qi(e,t,r,n,l){var o=n;if((t&1)===0&&(t&2)===0&&n!==null)e:for(;;){if(n===null)return;var i=n.tag;if(i===3||i===4){var u=n.stateNode.containerInfo;if(u===l||u.nodeType===8&&u.parentNode===l)break;if(i===4)for(i=n.return;i!==null;){var a=i.tag;if((a===3||a===4)&&(a=i.stateNode.containerInfo,a===l||a.nodeType===8&&a.parentNode===l))return;i=i.return}for(;u!==null;){if(i=Rr(u),i===null)return;if(a=i.tag,a===5||a===6){n=o=i;continue e}u=u.parentNode}}n=n.return}$d(function(){var s=o,d=Su(r),y=[];e:{var p=yf.get(e);if(p!==void 0){var x=Bu,w=e;switch(e){case"keypress":if(ho(r)===0)break e;case"keydown":case"keyup":x=Rp;break;case"focusin":w="focus",x=Oi;break;case"focusout":w="blur",x=Oi;break;case"beforeblur":case"afterblur":x=Oi;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":x=Ws;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":x=gp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":x=Pp;break;case ff:case hf:case pf:x=wp;break;case mf:x=Lp;break;case"scroll":x=mp;break;case"wheel":x=Mp;break;case"copy":case"cut":case"paste":x=Sp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":x=Ks}var N=(t&4)!==0,A=!N&&e==="scroll",m=N?p!==null?p+"Capture":null:p;N=[];for(var h=s,f;h!==null;){f=h;var k=f.stateNode;if(f.tag===5&&k!==null&&(f=k,m!==null&&(k=sl(h,m),k!=null&&N.push(yl(h,k,f)))),A)break;h=h.return}0<N.length&&(p=new x(p,w,null,r,d),y.push({event:p,listeners:N}))}}if((t&7)===0){e:{if(p=e==="mouseover"||e==="pointerover",x=e==="mouseout"||e==="pointerout",p&&r!==ka&&(w=r.relatedTarget||r.fromElement)&&(Rr(w)||w[Ot]))break e;if((x||p)&&(p=d.window===d?d:(p=d.ownerDocument)?p.defaultView||p.parentWindow:window,x?(w=r.relatedTarget||r.toElement,x=s,w=w?Rr(w):null,w!==null&&(A=jr(w),w!==A||w.tag!==5&&w.tag!==6)&&(w=null)):(x=null,w=s),x!==w)){if(N=Ws,k="onMouseLeave",m="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(N=Ks,k="onPointerLeave",m="onPointerEnter",h="pointer"),A=x==null?p:rn(x),f=w==null?p:rn(w),p=new N(k,h+"leave",x,r,d),p.target=A,p.relatedTarget=f,k=null,Rr(d)===s&&(N=new N(m,h+"enter",w,r,d),N.target=f,N.relatedTarget=A,k=N),A=k,x&&w)t:{for(N=x,m=w,h=0,f=N;f;f=Yr(f))h++;for(f=0,k=m;k;k=Yr(k))f++;for(;0<h-f;)N=Yr(N),h--;for(;0<f-h;)m=Yr(m),f--;for(;h--;){if(N===m||m!==null&&N===m.alternate)break t;N=Yr(N),m=Yr(m)}N=null}else N=null;x!==null&&lc(y,p,x,N,!1),w!==null&&A!==null&&lc(y,A,w,N,!0)}}e:{if(p=s?rn(s):window,x=p.nodeName&&p.nodeName.toLowerCase(),x==="select"||x==="input"&&p.type==="file")var B=Vp;else if(Js(p))if(af)B=Kp;else{B=Wp;var g=Hp}else(x=p.nodeName)&&x.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(B=Qp);if(B&&(B=B(e,s))){of(y,B,r,d);break e}g&&g(e,p,s),e==="focusout"&&(g=p._wrapperState)&&g.controlled&&p.type==="number"&&ya(p,"number",p.value)}switch(g=s?rn(s):window,e){case"focusin":(Js(g)||g.contentEditable==="true")&&(en=g,Ba=s,rl=null);break;case"focusout":rl=Ba=en=null;break;case"mousedown":Ra=!0;break;case"contextmenu":case"mouseup":case"dragend":Ra=!1,tc(y,r,d);break;case"selectionchange":if(Jp)break;case"keydown":case"keyup":tc(y,r,d)}var S;if(Au)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Zr?nf(e,r)&&(b="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(b="onCompositionStart");b&&(rf&&r.locale!=="ko"&&(Zr||b!=="onCompositionStart"?b==="onCompositionEnd"&&Zr&&(S=tf()):(rr=d,Fu="value"in rr?rr.value:rr.textContent,Zr=!0)),g=_o(s,b),0<g.length&&(b=new Qs(b,e,null,r,d),y.push({event:b,listeners:g}),S?b.data=S:(S=lf(r),S!==null&&(b.data=S)))),(S=zp?Op(e,r):$p(e,r))&&(s=_o(s,"onBeforeInput"),0<s.length&&(d=new Qs("onBeforeInput","beforeinput",null,r,d),y.push({event:d,listeners:s}),d.data=S))}gf(y,t)})}function yl(e,t,r){return{instance:e,listener:t,currentTarget:r}}function _o(e,t){for(var r=t+"Capture",n=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=sl(e,r),o!=null&&n.unshift(yl(e,o,l)),o=sl(e,t),o!=null&&n.push(yl(e,o,l))),e=e.return}return n}function Yr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function lc(e,t,r,n,l){for(var o=t._reactName,i=[];r!==null&&r!==n;){var u=r,a=u.alternate,s=u.stateNode;if(a!==null&&a===n)break;u.tag===5&&s!==null&&(u=s,l?(a=sl(r,o),a!=null&&i.unshift(yl(r,a,u))):l||(a=sl(r,o),a!=null&&i.push(yl(r,a,u)))),r=r.return}i.length!==0&&e.push({event:t,listeners:i})}var em=/\r\n?/g,tm=/\u0000|\uFFFD/g;function oc(e){return(typeof e=="string"?e:""+e).replace(em,`
`).replace(tm,"")}function ql(e,t,r){if(t=oc(t),oc(e)!==t&&r)throw Error(_(425))}function Lo(){}var Aa=null,ba=null;function Pa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var _a=typeof setTimeout=="function"?setTimeout:void 0,rm=typeof clearTimeout=="function"?clearTimeout:void 0,ic=typeof Promise=="function"?Promise:void 0,nm=typeof queueMicrotask=="function"?queueMicrotask:typeof ic<"u"?function(e){return ic.resolve(null).then(e).catch(lm)}:_a;function lm(e){setTimeout(function(){throw e})}function Ki(e,t){var r=t,n=0;do{var l=r.nextSibling;if(e.removeChild(r),l&&l.nodeType===8)if(r=l.data,r==="/$"){if(n===0){e.removeChild(l),fl(t);return}n--}else r!=="$"&&r!=="$?"&&r!=="$!"||n++;r=l}while(r);fl(t)}function ar(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ac(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var Bn=Math.random().toString(36).slice(2),Bt="__reactFiber$"+Bn,gl="__reactProps$"+Bn,Ot="__reactContainer$"+Bn,La="__reactEvents$"+Bn,om="__reactListeners$"+Bn,im="__reactHandles$"+Bn;function Rr(e){var t=e[Bt];if(t)return t;for(var r=e.parentNode;r;){if(t=r[Ot]||r[Bt]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=ac(e);e!==null;){if(r=e[Bt])return r;e=ac(e)}return t}e=r,r=e.parentNode}return null}function Al(e){return e=e[Bt]||e[Ot],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function rn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(_(33))}function ci(e){return e[gl]||null}var Ia=[],nn=-1;function yr(e){return{current:e}}function ce(e){0>nn||(e.current=Ia[nn],Ia[nn]=null,nn--)}function ue(e,t){nn++,Ia[nn]=e.current,e.current=t}var hr={},ze=yr(hr),Ge=yr(!1),Ir=hr;function wn(e,t){var r=e.type.contextTypes;if(!r)return hr;var n=e.stateNode;if(n&&n.__reactInternalMemoizedUnmaskedChildContext===t)return n.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in r)l[o]=t[o];return n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Je(e){return e=e.childContextTypes,e!=null}function Io(){ce(Ge),ce(ze)}function uc(e,t,r){if(ze.current!==hr)throw Error(_(168));ue(ze,t),ue(Ge,r)}function xf(e,t,r){var n=e.stateNode;if(t=t.childContextTypes,typeof n.getChildContext!="function")return r;n=n.getChildContext();for(var l in n)if(!(l in t))throw Error(_(108,H0(e)||"Unknown",l));return pe({},r,n)}function Mo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||hr,Ir=ze.current,ue(ze,e),ue(Ge,Ge.current),!0}function sc(e,t,r){var n=e.stateNode;if(!n)throw Error(_(169));r?(e=xf(e,t,Ir),n.__reactInternalMemoizedMergedChildContext=e,ce(Ge),ce(ze),ue(ze,e)):ce(Ge),ue(Ge,r)}var Lt=null,di=!1,Yi=!1;function wf(e){Lt===null?Lt=[e]:Lt.push(e)}function am(e){di=!0,wf(e)}function gr(){if(!Yi&&Lt!==null){Yi=!0;var e=0,t=oe;try{var r=Lt;for(oe=1;e<r.length;e++){var n=r[e];do n=n(!0);while(n!==null)}Lt=null,di=!1}catch(l){throw Lt!==null&&(Lt=Lt.slice(e+1)),Hd(Eu,gr),l}finally{oe=t,Yi=!1}}return null}var ln=[],on=0,To=null,zo=0,ct=[],dt=0,Mr=null,It=1,Mt="";function Dr(e,t){ln[on++]=zo,ln[on++]=To,To=e,zo=t}function kf(e,t,r){ct[dt++]=It,ct[dt++]=Mt,ct[dt++]=Mr,Mr=e;var n=It;e=Mt;var l=32-kt(n)-1;n&=~(1<<l),r+=1;var o=32-kt(t)+l;if(30<o){var i=l-l%5;o=(n&(1<<i)-1).toString(32),n>>=i,l-=i,It=1<<32-kt(t)+l|r<<l|n,Mt=o+e}else It=1<<o|r<<l|n,Mt=e}function Pu(e){e.return!==null&&(Dr(e,1),kf(e,1,0))}function _u(e){for(;e===To;)To=ln[--on],ln[on]=null,zo=ln[--on],ln[on]=null;for(;e===Mr;)Mr=ct[--dt],ct[dt]=null,Mt=ct[--dt],ct[dt]=null,It=ct[--dt],ct[dt]=null}var nt=null,rt=null,de=!1,wt=null;function Sf(e,t){var r=ft(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function cc(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,nt=e,rt=ar(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,nt=e,rt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=Mr!==null?{id:It,overflow:Mt}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=ft(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,nt=e,rt=null,!0):!1;default:return!1}}function Ma(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ta(e){if(de){var t=rt;if(t){var r=t;if(!cc(e,t)){if(Ma(e))throw Error(_(418));t=ar(r.nextSibling);var n=nt;t&&cc(e,t)?Sf(n,r):(e.flags=e.flags&-4097|2,de=!1,nt=e)}}else{if(Ma(e))throw Error(_(418));e.flags=e.flags&-4097|2,de=!1,nt=e}}}function dc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;nt=e}function Zl(e){if(e!==nt)return!1;if(!de)return dc(e),de=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Pa(e.type,e.memoizedProps)),t&&(t=rt)){if(Ma(e))throw Ef(),Error(_(418));for(;t;)Sf(e,t),t=ar(t.nextSibling)}if(dc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){rt=ar(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}rt=null}}else rt=nt?ar(e.stateNode.nextSibling):null;return!0}function Ef(){for(var e=rt;e;)e=ar(e.nextSibling)}function kn(){rt=nt=null,de=!1}function Lu(e){wt===null?wt=[e]:wt.push(e)}var um=Ut.ReactCurrentBatchConfig;function $n(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(_(309));var n=r.stateNode}if(!n)throw Error(_(147,e));var l=n,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var u=l.refs;i===null?delete u[o]:u[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(_(284));if(!r._owner)throw Error(_(290,e))}return e}function eo(e,t){throw e=Object.prototype.toString.call(t),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function fc(e){var t=e._init;return t(e._payload)}function Cf(e){function t(m,h){if(e){var f=m.deletions;f===null?(m.deletions=[h],m.flags|=16):f.push(h)}}function r(m,h){if(!e)return null;for(;h!==null;)t(m,h),h=h.sibling;return null}function n(m,h){for(m=new Map;h!==null;)h.key!==null?m.set(h.key,h):m.set(h.index,h),h=h.sibling;return m}function l(m,h){return m=dr(m,h),m.index=0,m.sibling=null,m}function o(m,h,f){return m.index=f,e?(f=m.alternate,f!==null?(f=f.index,f<h?(m.flags|=2,h):f):(m.flags|=2,h)):(m.flags|=1048576,h)}function i(m){return e&&m.alternate===null&&(m.flags|=2),m}function u(m,h,f,k){return h===null||h.tag!==6?(h=ta(f,m.mode,k),h.return=m,h):(h=l(h,f),h.return=m,h)}function a(m,h,f,k){var B=f.type;return B===qr?d(m,h,f.props.children,k,f.key):h!==null&&(h.elementType===B||typeof B=="object"&&B!==null&&B.$$typeof===Xt&&fc(B)===h.type)?(k=l(h,f.props),k.ref=$n(m,h,f),k.return=m,k):(k=wo(f.type,f.key,f.props,null,m.mode,k),k.ref=$n(m,h,f),k.return=m,k)}function s(m,h,f,k){return h===null||h.tag!==4||h.stateNode.containerInfo!==f.containerInfo||h.stateNode.implementation!==f.implementation?(h=ra(f,m.mode,k),h.return=m,h):(h=l(h,f.children||[]),h.return=m,h)}function d(m,h,f,k,B){return h===null||h.tag!==7?(h=Lr(f,m.mode,k,B),h.return=m,h):(h=l(h,f),h.return=m,h)}function y(m,h,f){if(typeof h=="string"&&h!==""||typeof h=="number")return h=ta(""+h,m.mode,f),h.return=m,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Vl:return f=wo(h.type,h.key,h.props,null,m.mode,f),f.ref=$n(m,null,h),f.return=m,f;case Xr:return h=ra(h,m.mode,f),h.return=m,h;case Xt:var k=h._init;return y(m,k(h._payload),f)}if(Kn(h)||In(h))return h=Lr(h,m.mode,f,null),h.return=m,h;eo(m,h)}return null}function p(m,h,f,k){var B=h!==null?h.key:null;if(typeof f=="string"&&f!==""||typeof f=="number")return B!==null?null:u(m,h,""+f,k);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case Vl:return f.key===B?a(m,h,f,k):null;case Xr:return f.key===B?s(m,h,f,k):null;case Xt:return B=f._init,p(m,h,B(f._payload),k)}if(Kn(f)||In(f))return B!==null?null:d(m,h,f,k,null);eo(m,f)}return null}function x(m,h,f,k,B){if(typeof k=="string"&&k!==""||typeof k=="number")return m=m.get(f)||null,u(h,m,""+k,B);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case Vl:return m=m.get(k.key===null?f:k.key)||null,a(h,m,k,B);case Xr:return m=m.get(k.key===null?f:k.key)||null,s(h,m,k,B);case Xt:var g=k._init;return x(m,h,f,g(k._payload),B)}if(Kn(k)||In(k))return m=m.get(f)||null,d(h,m,k,B,null);eo(h,k)}return null}function w(m,h,f,k){for(var B=null,g=null,S=h,b=h=0,z=null;S!==null&&b<f.length;b++){S.index>b?(z=S,S=null):z=S.sibling;var $=p(m,S,f[b],k);if($===null){S===null&&(S=z);break}e&&S&&$.alternate===null&&t(m,S),h=o($,h,b),g===null?B=$:g.sibling=$,g=$,S=z}if(b===f.length)return r(m,S),de&&Dr(m,b),B;if(S===null){for(;b<f.length;b++)S=y(m,f[b],k),S!==null&&(h=o(S,h,b),g===null?B=S:g.sibling=S,g=S);return de&&Dr(m,b),B}for(S=n(m,S);b<f.length;b++)z=x(S,m,b,f[b],k),z!==null&&(e&&z.alternate!==null&&S.delete(z.key===null?b:z.key),h=o(z,h,b),g===null?B=z:g.sibling=z,g=z);return e&&S.forEach(function(G){return t(m,G)}),de&&Dr(m,b),B}function N(m,h,f,k){var B=In(f);if(typeof B!="function")throw Error(_(150));if(f=B.call(f),f==null)throw Error(_(151));for(var g=B=null,S=h,b=h=0,z=null,$=f.next();S!==null&&!$.done;b++,$=f.next()){S.index>b?(z=S,S=null):z=S.sibling;var G=p(m,S,$.value,k);if(G===null){S===null&&(S=z);break}e&&S&&G.alternate===null&&t(m,S),h=o(G,h,b),g===null?B=G:g.sibling=G,g=G,S=z}if($.done)return r(m,S),de&&Dr(m,b),B;if(S===null){for(;!$.done;b++,$=f.next())$=y(m,$.value,k),$!==null&&(h=o($,h,b),g===null?B=$:g.sibling=$,g=$);return de&&Dr(m,b),B}for(S=n(m,S);!$.done;b++,$=f.next())$=x(S,m,b,$.value,k),$!==null&&(e&&$.alternate!==null&&S.delete($.key===null?b:$.key),h=o($,h,b),g===null?B=$:g.sibling=$,g=$);return e&&S.forEach(function(ge){return t(m,ge)}),de&&Dr(m,b),B}function A(m,h,f,k){if(typeof f=="object"&&f!==null&&f.type===qr&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case Vl:e:{for(var B=f.key,g=h;g!==null;){if(g.key===B){if(B=f.type,B===qr){if(g.tag===7){r(m,g.sibling),h=l(g,f.props.children),h.return=m,m=h;break e}}else if(g.elementType===B||typeof B=="object"&&B!==null&&B.$$typeof===Xt&&fc(B)===g.type){r(m,g.sibling),h=l(g,f.props),h.ref=$n(m,g,f),h.return=m,m=h;break e}r(m,g);break}else t(m,g);g=g.sibling}f.type===qr?(h=Lr(f.props.children,m.mode,k,f.key),h.return=m,m=h):(k=wo(f.type,f.key,f.props,null,m.mode,k),k.ref=$n(m,h,f),k.return=m,m=k)}return i(m);case Xr:e:{for(g=f.key;h!==null;){if(h.key===g)if(h.tag===4&&h.stateNode.containerInfo===f.containerInfo&&h.stateNode.implementation===f.implementation){r(m,h.sibling),h=l(h,f.children||[]),h.return=m,m=h;break e}else{r(m,h);break}else t(m,h);h=h.sibling}h=ra(f,m.mode,k),h.return=m,m=h}return i(m);case Xt:return g=f._init,A(m,h,g(f._payload),k)}if(Kn(f))return w(m,h,f,k);if(In(f))return N(m,h,f,k);eo(m,f)}return typeof f=="string"&&f!==""||typeof f=="number"?(f=""+f,h!==null&&h.tag===6?(r(m,h.sibling),h=l(h,f),h.return=m,m=h):(r(m,h),h=ta(f,m.mode,k),h.return=m,m=h),i(m)):r(m,h)}return A}var Sn=Cf(!0),Nf=Cf(!1),Oo=yr(null),$o=null,an=null,Iu=null;function Mu(){Iu=an=$o=null}function Tu(e){var t=Oo.current;ce(Oo),e._currentValue=t}function za(e,t,r){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===r)break;e=e.return}}function mn(e,t){$o=e,Iu=an=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(Ye=!0),e.firstContext=null)}function pt(e){var t=e._currentValue;if(Iu!==e)if(e={context:e,memoizedValue:t,next:null},an===null){if($o===null)throw Error(_(308));an=e,$o.dependencies={lanes:0,firstContext:e}}else an=an.next=e;return t}var Ar=null;function zu(e){Ar===null?Ar=[e]:Ar.push(e)}function Df(e,t,r,n){var l=t.interleaved;return l===null?(r.next=r,zu(t)):(r.next=l.next,l.next=r),t.interleaved=r,$t(e,n)}function $t(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var qt=!1;function Ou(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Ff(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Tt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function ur(e,t,r){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,(re&2)!==0){var l=n.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),n.pending=t,$t(e,r)}return l=n.interleaved,l===null?(t.next=t,zu(n)):(t.next=l.next,l.next=t),n.interleaved=t,$t(e,r)}function po(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Cu(e,r)}}function hc(e,t){var r=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,r===n)){var l=null,o=null;if(r=r.firstBaseUpdate,r!==null){do{var i={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};o===null?l=o=i:o=o.next=i,r=r.next}while(r!==null);o===null?l=o=t:o=o.next=t}else l=o=t;r={baseState:n.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:n.shared,effects:n.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function jo(e,t,r,n){var l=e.updateQueue;qt=!1;var o=l.firstBaseUpdate,i=l.lastBaseUpdate,u=l.shared.pending;if(u!==null){l.shared.pending=null;var a=u,s=a.next;a.next=null,i===null?o=s:i.next=s,i=a;var d=e.alternate;d!==null&&(d=d.updateQueue,u=d.lastBaseUpdate,u!==i&&(u===null?d.firstBaseUpdate=s:u.next=s,d.lastBaseUpdate=a))}if(o!==null){var y=l.baseState;i=0,d=s=a=null,u=o;do{var p=u.lane,x=u.eventTime;if((n&p)===p){d!==null&&(d=d.next={eventTime:x,lane:0,tag:u.tag,payload:u.payload,callback:u.callback,next:null});e:{var w=e,N=u;switch(p=t,x=r,N.tag){case 1:if(w=N.payload,typeof w=="function"){y=w.call(x,y,p);break e}y=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=N.payload,p=typeof w=="function"?w.call(x,y,p):w,p==null)break e;y=pe({},y,p);break e;case 2:qt=!0}}u.callback!==null&&u.lane!==0&&(e.flags|=64,p=l.effects,p===null?l.effects=[u]:p.push(u))}else x={eventTime:x,lane:p,tag:u.tag,payload:u.payload,callback:u.callback,next:null},d===null?(s=d=x,a=y):d=d.next=x,i|=p;if(u=u.next,u===null){if(u=l.shared.pending,u===null)break;p=u,u=p.next,p.next=null,l.lastBaseUpdate=p,l.shared.pending=null}}while(1);if(d===null&&(a=y),l.baseState=a,l.firstBaseUpdate=s,l.lastBaseUpdate=d,t=l.shared.interleaved,t!==null){l=t;do i|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);zr|=i,e.lanes=i,e.memoizedState=y}}function pc(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var n=e[t],l=n.callback;if(l!==null){if(n.callback=null,n=r,typeof l!="function")throw Error(_(191,l));l.call(n)}}}var bl={},At=yr(bl),vl=yr(bl),xl=yr(bl);function br(e){if(e===bl)throw Error(_(174));return e}function $u(e,t){switch(ue(xl,t),ue(vl,e),ue(At,bl),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:va(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=va(t,e)}ce(At),ue(At,t)}function En(){ce(At),ce(vl),ce(xl)}function Bf(e){br(xl.current);var t=br(At.current),r=va(t,e.type);t!==r&&(ue(vl,e),ue(At,r))}function ju(e){vl.current===e&&(ce(At),ce(vl))}var fe=yr(0);function Uo(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Gi=[];function Uu(){for(var e=0;e<Gi.length;e++)Gi[e]._workInProgressVersionPrimary=null;Gi.length=0}var mo=Ut.ReactCurrentDispatcher,Ji=Ut.ReactCurrentBatchConfig,Tr=0,he=null,De=null,Re=null,Vo=!1,nl=!1,wl=0,sm=0;function Ie(){throw Error(_(321))}function Vu(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!Et(e[r],t[r]))return!1;return!0}function Hu(e,t,r,n,l,o){if(Tr=o,he=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,mo.current=e===null||e.memoizedState===null?hm:pm,e=r(n,l),nl){o=0;do{if(nl=!1,wl=0,25<=o)throw Error(_(301));o+=1,Re=De=null,t.updateQueue=null,mo.current=mm,e=r(n,l)}while(nl)}if(mo.current=Ho,t=De!==null&&De.next!==null,Tr=0,Re=De=he=null,Vo=!1,t)throw Error(_(300));return e}function Wu(){var e=wl!==0;return wl=0,e}function Ft(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Re===null?he.memoizedState=Re=e:Re=Re.next=e,Re}function mt(){if(De===null){var e=he.alternate;e=e!==null?e.memoizedState:null}else e=De.next;var t=Re===null?he.memoizedState:Re.next;if(t!==null)Re=t,De=e;else{if(e===null)throw Error(_(310));De=e,e={memoizedState:De.memoizedState,baseState:De.baseState,baseQueue:De.baseQueue,queue:De.queue,next:null},Re===null?he.memoizedState=Re=e:Re=Re.next=e}return Re}function kl(e,t){return typeof t=="function"?t(e):t}function Xi(e){var t=mt(),r=t.queue;if(r===null)throw Error(_(311));r.lastRenderedReducer=e;var n=De,l=n.baseQueue,o=r.pending;if(o!==null){if(l!==null){var i=l.next;l.next=o.next,o.next=i}n.baseQueue=l=o,r.pending=null}if(l!==null){o=l.next,n=n.baseState;var u=i=null,a=null,s=o;do{var d=s.lane;if((Tr&d)===d)a!==null&&(a=a.next={lane:0,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null}),n=s.hasEagerState?s.eagerState:e(n,s.action);else{var y={lane:d,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null};a===null?(u=a=y,i=n):a=a.next=y,he.lanes|=d,zr|=d}s=s.next}while(s!==null&&s!==o);a===null?i=n:a.next=u,Et(n,t.memoizedState)||(Ye=!0),t.memoizedState=n,t.baseState=i,t.baseQueue=a,r.lastRenderedState=n}if(e=r.interleaved,e!==null){l=e;do o=l.lane,he.lanes|=o,zr|=o,l=l.next;while(l!==e)}else l===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function qi(e){var t=mt(),r=t.queue;if(r===null)throw Error(_(311));r.lastRenderedReducer=e;var n=r.dispatch,l=r.pending,o=t.memoizedState;if(l!==null){r.pending=null;var i=l=l.next;do o=e(o,i.action),i=i.next;while(i!==l);Et(o,t.memoizedState)||(Ye=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),r.lastRenderedState=o}return[o,n]}function Rf(){}function Af(e,t){var r=he,n=mt(),l=t(),o=!Et(n.memoizedState,l);if(o&&(n.memoizedState=l,Ye=!0),n=n.queue,Qu(_f.bind(null,r,n,e),[e]),n.getSnapshot!==t||o||Re!==null&&Re.memoizedState.tag&1){if(r.flags|=2048,Sl(9,Pf.bind(null,r,n,l,t),void 0,null),Ae===null)throw Error(_(349));(Tr&30)!==0||bf(r,t,l)}return l}function bf(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=he.updateQueue,t===null?(t={lastEffect:null,stores:null},he.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function Pf(e,t,r,n){t.value=r,t.getSnapshot=n,Lf(t)&&If(e)}function _f(e,t,r){return r(function(){Lf(t)&&If(e)})}function Lf(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!Et(e,r)}catch{return!0}}function If(e){var t=$t(e,1);t!==null&&St(t,e,1,-1)}function mc(e){var t=Ft();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:kl,lastRenderedState:e},t.queue=e,e=e.dispatch=fm.bind(null,he,e),[t.memoizedState,e]}function Sl(e,t,r,n){return e={tag:e,create:t,destroy:r,deps:n,next:null},t=he.updateQueue,t===null?(t={lastEffect:null,stores:null},he.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e)),e}function Mf(){return mt().memoizedState}function yo(e,t,r,n){var l=Ft();he.flags|=e,l.memoizedState=Sl(1|t,r,void 0,n===void 0?null:n)}function fi(e,t,r,n){var l=mt();n=n===void 0?null:n;var o=void 0;if(De!==null){var i=De.memoizedState;if(o=i.destroy,n!==null&&Vu(n,i.deps)){l.memoizedState=Sl(t,r,o,n);return}}he.flags|=e,l.memoizedState=Sl(1|t,r,o,n)}function yc(e,t){return yo(8390656,8,e,t)}function Qu(e,t){return fi(2048,8,e,t)}function Tf(e,t){return fi(4,2,e,t)}function zf(e,t){return fi(4,4,e,t)}function Of(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function $f(e,t,r){return r=r!=null?r.concat([e]):null,fi(4,4,Of.bind(null,t,e),r)}function Ku(){}function jf(e,t){var r=mt();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&Vu(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function Uf(e,t){var r=mt();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&Vu(t,n[1])?n[0]:(e=e(),r.memoizedState=[e,t],e)}function Vf(e,t,r){return(Tr&21)===0?(e.baseState&&(e.baseState=!1,Ye=!0),e.memoizedState=r):(Et(r,t)||(r=Kd(),he.lanes|=r,zr|=r,e.baseState=!0),t)}function cm(e,t){var r=oe;oe=r!==0&&4>r?r:4,e(!0);var n=Ji.transition;Ji.transition={};try{e(!1),t()}finally{oe=r,Ji.transition=n}}function Hf(){return mt().memoizedState}function dm(e,t,r){var n=cr(e);if(r={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null},Wf(e))Qf(t,r);else if(r=Df(e,t,r,n),r!==null){var l=He();St(r,e,n,l),Kf(r,t,n)}}function fm(e,t,r){var n=cr(e),l={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null};if(Wf(e))Qf(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,u=o(i,r);if(l.hasEagerState=!0,l.eagerState=u,Et(u,i)){var a=t.interleaved;a===null?(l.next=l,zu(t)):(l.next=a.next,a.next=l),t.interleaved=l;return}}catch{}finally{}r=Df(e,t,l,n),r!==null&&(l=He(),St(r,e,n,l),Kf(r,t,n))}}function Wf(e){var t=e.alternate;return e===he||t!==null&&t===he}function Qf(e,t){nl=Vo=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function Kf(e,t,r){if((r&4194240)!==0){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Cu(e,r)}}var Ho={readContext:pt,useCallback:Ie,useContext:Ie,useEffect:Ie,useImperativeHandle:Ie,useInsertionEffect:Ie,useLayoutEffect:Ie,useMemo:Ie,useReducer:Ie,useRef:Ie,useState:Ie,useDebugValue:Ie,useDeferredValue:Ie,useTransition:Ie,useMutableSource:Ie,useSyncExternalStore:Ie,useId:Ie,unstable_isNewReconciler:!1},hm={readContext:pt,useCallback:function(e,t){return Ft().memoizedState=[e,t===void 0?null:t],e},useContext:pt,useEffect:yc,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,yo(4194308,4,Of.bind(null,t,e),r)},useLayoutEffect:function(e,t){return yo(4194308,4,e,t)},useInsertionEffect:function(e,t){return yo(4,2,e,t)},useMemo:function(e,t){var r=Ft();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var n=Ft();return t=r!==void 0?r(t):t,n.memoizedState=n.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},n.queue=e,e=e.dispatch=dm.bind(null,he,e),[n.memoizedState,e]},useRef:function(e){var t=Ft();return e={current:e},t.memoizedState=e},useState:mc,useDebugValue:Ku,useDeferredValue:function(e){return Ft().memoizedState=e},useTransition:function(){var e=mc(!1),t=e[0];return e=cm.bind(null,e[1]),Ft().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var n=he,l=Ft();if(de){if(r===void 0)throw Error(_(407));r=r()}else{if(r=t(),Ae===null)throw Error(_(349));(Tr&30)!==0||bf(n,t,r)}l.memoizedState=r;var o={value:r,getSnapshot:t};return l.queue=o,yc(_f.bind(null,n,o,e),[e]),n.flags|=2048,Sl(9,Pf.bind(null,n,o,r,t),void 0,null),r},useId:function(){var e=Ft(),t=Ae.identifierPrefix;if(de){var r=Mt,n=It;r=(n&~(1<<32-kt(n)-1)).toString(32)+r,t=":"+t+"R"+r,r=wl++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=sm++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},pm={readContext:pt,useCallback:jf,useContext:pt,useEffect:Qu,useImperativeHandle:$f,useInsertionEffect:Tf,useLayoutEffect:zf,useMemo:Uf,useReducer:Xi,useRef:Mf,useState:function(){return Xi(kl)},useDebugValue:Ku,useDeferredValue:function(e){var t=mt();return Vf(t,De.memoizedState,e)},useTransition:function(){var e=Xi(kl)[0],t=mt().memoizedState;return[e,t]},useMutableSource:Rf,useSyncExternalStore:Af,useId:Hf,unstable_isNewReconciler:!1},mm={readContext:pt,useCallback:jf,useContext:pt,useEffect:Qu,useImperativeHandle:$f,useInsertionEffect:Tf,useLayoutEffect:zf,useMemo:Uf,useReducer:qi,useRef:Mf,useState:function(){return qi(kl)},useDebugValue:Ku,useDeferredValue:function(e){var t=mt();return De===null?t.memoizedState=e:Vf(t,De.memoizedState,e)},useTransition:function(){var e=qi(kl)[0],t=mt().memoizedState;return[e,t]},useMutableSource:Rf,useSyncExternalStore:Af,useId:Hf,unstable_isNewReconciler:!1};function vt(e,t){if(e&&e.defaultProps){t=pe({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function Oa(e,t,r,n){t=e.memoizedState,r=r(n,t),r=r==null?t:pe({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var hi={isMounted:function(e){return(e=e._reactInternals)?jr(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var n=He(),l=cr(e),o=Tt(n,l);o.payload=t,r!=null&&(o.callback=r),t=ur(e,o,l),t!==null&&(St(t,e,l,n),po(t,e,l))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var n=He(),l=cr(e),o=Tt(n,l);o.tag=1,o.payload=t,r!=null&&(o.callback=r),t=ur(e,o,l),t!==null&&(St(t,e,l,n),po(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=He(),n=cr(e),l=Tt(r,n);l.tag=2,t!=null&&(l.callback=t),t=ur(e,l,n),t!==null&&(St(t,e,n,r),po(t,e,n))}};function gc(e,t,r,n,l,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,o,i):t.prototype&&t.prototype.isPureReactComponent?!pl(r,n)||!pl(l,o):!0}function Yf(e,t,r){var n=!1,l=hr,o=t.contextType;return typeof o=="object"&&o!==null?o=pt(o):(l=Je(t)?Ir:ze.current,n=t.contextTypes,o=(n=n!=null)?wn(e,l):hr),t=new t(r,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=hi,e.stateNode=t,t._reactInternals=e,n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function vc(e,t,r,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&hi.enqueueReplaceState(t,t.state,null)}function $a(e,t,r,n){var l=e.stateNode;l.props=r,l.state=e.memoizedState,l.refs={},Ou(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=pt(o):(o=Je(t)?Ir:ze.current,l.context=wn(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Oa(e,t,o,r),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&hi.enqueueReplaceState(l,l.state,null),jo(e,r,l,n),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function Cn(e,t){try{var r="",n=t;do r+=V0(n),n=n.return;while(n);var l=r}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function Zi(e,t,r){return{value:e,source:null,stack:r!=null?r:null,digest:t!=null?t:null}}function ja(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var ym=typeof WeakMap=="function"?WeakMap:Map;function Gf(e,t,r){r=Tt(-1,r),r.tag=3,r.payload={element:null};var n=t.value;return r.callback=function(){Qo||(Qo=!0,Xa=n),ja(e,t)},r}function Jf(e,t,r){r=Tt(-1,r),r.tag=3;var n=e.type.getDerivedStateFromError;if(typeof n=="function"){var l=t.value;r.payload=function(){return n(l)},r.callback=function(){ja(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(r.callback=function(){ja(e,t),typeof n!="function"&&(sr===null?sr=new Set([this]):sr.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),r}function xc(e,t,r){var n=e.pingCache;if(n===null){n=e.pingCache=new ym;var l=new Set;n.set(t,l)}else l=n.get(t),l===void 0&&(l=new Set,n.set(t,l));l.has(r)||(l.add(r),e=Am.bind(null,e,t,r),t.then(e,e))}function wc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function kc(e,t,r,n,l){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=Tt(-1,1),t.tag=2,ur(r,t,1))),r.lanes|=1),e):(e.flags|=65536,e.lanes=l,e)}var gm=Ut.ReactCurrentOwner,Ye=!1;function Ve(e,t,r,n){t.child=e===null?Nf(t,null,r,n):Sn(t,e.child,r,n)}function Sc(e,t,r,n,l){r=r.render;var o=t.ref;return mn(t,l),n=Hu(e,t,r,n,o,l),r=Wu(),e!==null&&!Ye?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,jt(e,t,l)):(de&&r&&Pu(t),t.flags|=1,Ve(e,t,n,l),t.child)}function Ec(e,t,r,n,l){if(e===null){var o=r.type;return typeof o=="function"&&!ts(o)&&o.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=o,Xf(e,t,o,n,l)):(e=wo(r.type,null,n,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,(e.lanes&l)===0){var i=o.memoizedProps;if(r=r.compare,r=r!==null?r:pl,r(i,n)&&e.ref===t.ref)return jt(e,t,l)}return t.flags|=1,e=dr(o,n),e.ref=t.ref,e.return=t,t.child=e}function Xf(e,t,r,n,l){if(e!==null){var o=e.memoizedProps;if(pl(o,n)&&e.ref===t.ref)if(Ye=!1,t.pendingProps=n=o,(e.lanes&l)!==0)(e.flags&131072)!==0&&(Ye=!0);else return t.lanes=e.lanes,jt(e,t,l)}return Ua(e,t,r,n,l)}function qf(e,t,r){var n=t.pendingProps,l=n.children,o=e!==null?e.memoizedState:null;if(n.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ue(sn,Ze),Ze|=r;else{if((r&1073741824)===0)return e=o!==null?o.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ue(sn,Ze),Ze|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},n=o!==null?o.baseLanes:r,ue(sn,Ze),Ze|=n}else o!==null?(n=o.baseLanes|r,t.memoizedState=null):n=r,ue(sn,Ze),Ze|=n;return Ve(e,t,l,r),t.child}function Zf(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function Ua(e,t,r,n,l){var o=Je(r)?Ir:ze.current;return o=wn(t,o),mn(t,l),r=Hu(e,t,r,n,o,l),n=Wu(),e!==null&&!Ye?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,jt(e,t,l)):(de&&n&&Pu(t),t.flags|=1,Ve(e,t,r,l),t.child)}function Cc(e,t,r,n,l){if(Je(r)){var o=!0;Mo(t)}else o=!1;if(mn(t,l),t.stateNode===null)go(e,t),Yf(t,r,n),$a(t,r,n,l),n=!0;else if(e===null){var i=t.stateNode,u=t.memoizedProps;i.props=u;var a=i.context,s=r.contextType;typeof s=="object"&&s!==null?s=pt(s):(s=Je(r)?Ir:ze.current,s=wn(t,s));var d=r.getDerivedStateFromProps,y=typeof d=="function"||typeof i.getSnapshotBeforeUpdate=="function";y||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(u!==n||a!==s)&&vc(t,i,n,s),qt=!1;var p=t.memoizedState;i.state=p,jo(t,n,i,l),a=t.memoizedState,u!==n||p!==a||Ge.current||qt?(typeof d=="function"&&(Oa(t,r,d,n),a=t.memoizedState),(u=qt||gc(t,r,u,n,p,a,s))?(y||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=a),i.props=n,i.state=a,i.context=s,n=u):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{i=t.stateNode,Ff(e,t),u=t.memoizedProps,s=t.type===t.elementType?u:vt(t.type,u),i.props=s,y=t.pendingProps,p=i.context,a=r.contextType,typeof a=="object"&&a!==null?a=pt(a):(a=Je(r)?Ir:ze.current,a=wn(t,a));var x=r.getDerivedStateFromProps;(d=typeof x=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(u!==y||p!==a)&&vc(t,i,n,a),qt=!1,p=t.memoizedState,i.state=p,jo(t,n,i,l);var w=t.memoizedState;u!==y||p!==w||Ge.current||qt?(typeof x=="function"&&(Oa(t,r,x,n),w=t.memoizedState),(s=qt||gc(t,r,s,n,p,w,a)||!1)?(d||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(n,w,a),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(n,w,a)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=w),i.props=n,i.state=w,i.context=a,n=s):(typeof i.componentDidUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),n=!1)}return Va(e,t,r,n,o,l)}function Va(e,t,r,n,l,o){Zf(e,t);var i=(t.flags&128)!==0;if(!n&&!i)return l&&sc(t,r,!1),jt(e,t,o);n=t.stateNode,gm.current=t;var u=i&&typeof r.getDerivedStateFromError!="function"?null:n.render();return t.flags|=1,e!==null&&i?(t.child=Sn(t,e.child,null,o),t.child=Sn(t,null,u,o)):Ve(e,t,u,o),t.memoizedState=n.state,l&&sc(t,r,!0),t.child}function eh(e){var t=e.stateNode;t.pendingContext?uc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&uc(e,t.context,!1),$u(e,t.containerInfo)}function Nc(e,t,r,n,l){return kn(),Lu(l),t.flags|=256,Ve(e,t,r,n),t.child}var Ha={dehydrated:null,treeContext:null,retryLane:0};function Wa(e){return{baseLanes:e,cachePool:null,transitions:null}}function th(e,t,r){var n=t.pendingProps,l=fe.current,o=!1,i=(t.flags&128)!==0,u;if((u=i)||(u=e!==null&&e.memoizedState===null?!1:(l&2)!==0),u?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),ue(fe,l&1),e===null)return Ta(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(i=n.children,e=n.fallback,o?(n=t.mode,o=t.child,i={mode:"hidden",children:i},(n&1)===0&&o!==null?(o.childLanes=0,o.pendingProps=i):o=yi(i,n,0,null),e=Lr(e,n,r,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Wa(r),t.memoizedState=Ha,e):Yu(t,i));if(l=e.memoizedState,l!==null&&(u=l.dehydrated,u!==null))return vm(e,t,i,n,u,l,r);if(o){o=n.fallback,i=t.mode,l=e.child,u=l.sibling;var a={mode:"hidden",children:n.children};return(i&1)===0&&t.child!==l?(n=t.child,n.childLanes=0,n.pendingProps=a,t.deletions=null):(n=dr(l,a),n.subtreeFlags=l.subtreeFlags&14680064),u!==null?o=dr(u,o):(o=Lr(o,i,r,null),o.flags|=2),o.return=t,n.return=t,n.sibling=o,t.child=n,n=o,o=t.child,i=e.child.memoizedState,i=i===null?Wa(r):{baseLanes:i.baseLanes|r,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~r,t.memoizedState=Ha,n}return o=e.child,e=o.sibling,n=dr(o,{mode:"visible",children:n.children}),(t.mode&1)===0&&(n.lanes=r),n.return=t,n.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n}function Yu(e,t){return t=yi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function to(e,t,r,n){return n!==null&&Lu(n),Sn(t,e.child,null,r),e=Yu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function vm(e,t,r,n,l,o,i){if(r)return t.flags&256?(t.flags&=-257,n=Zi(Error(_(422))),to(e,t,i,n)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=n.fallback,l=t.mode,n=yi({mode:"visible",children:n.children},l,0,null),o=Lr(o,l,i,null),o.flags|=2,n.return=t,o.return=t,n.sibling=o,t.child=n,(t.mode&1)!==0&&Sn(t,e.child,null,i),t.child.memoizedState=Wa(i),t.memoizedState=Ha,o);if((t.mode&1)===0)return to(e,t,i,null);if(l.data==="$!"){if(n=l.nextSibling&&l.nextSibling.dataset,n)var u=n.dgst;return n=u,o=Error(_(419)),n=Zi(o,n,void 0),to(e,t,i,n)}if(u=(i&e.childLanes)!==0,Ye||u){if(n=Ae,n!==null){switch(i&-i){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=(l&(n.suspendedLanes|i))!==0?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,$t(e,l),St(n,e,l,-1))}return es(),n=Zi(Error(_(421))),to(e,t,i,n)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=bm.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,rt=ar(l.nextSibling),nt=t,de=!0,wt=null,e!==null&&(ct[dt++]=It,ct[dt++]=Mt,ct[dt++]=Mr,It=e.id,Mt=e.overflow,Mr=t),t=Yu(t,n.children),t.flags|=4096,t)}function Dc(e,t,r){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),za(e.return,t,r)}function ea(e,t,r,n,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=n,o.tail=r,o.tailMode=l)}function rh(e,t,r){var n=t.pendingProps,l=n.revealOrder,o=n.tail;if(Ve(e,t,n.children,r),n=fe.current,(n&2)!==0)n=n&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Dc(e,r,t);else if(e.tag===19)Dc(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}if(ue(fe,n),(t.mode&1)===0)t.memoizedState=null;else switch(l){case"forwards":for(r=t.child,l=null;r!==null;)e=r.alternate,e!==null&&Uo(e)===null&&(l=r),r=r.sibling;r=l,r===null?(l=t.child,t.child=null):(l=r.sibling,r.sibling=null),ea(t,!1,l,r,o);break;case"backwards":for(r=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&Uo(e)===null){t.child=l;break}e=l.sibling,l.sibling=r,r=l,l=e}ea(t,!0,r,null,o);break;case"together":ea(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function go(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function jt(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),zr|=t.lanes,(r&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(_(153));if(t.child!==null){for(e=t.child,r=dr(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=dr(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function xm(e,t,r){switch(t.tag){case 3:eh(t),kn();break;case 5:Bf(t);break;case 1:Je(t.type)&&Mo(t);break;case 4:$u(t,t.stateNode.containerInfo);break;case 10:var n=t.type._context,l=t.memoizedProps.value;ue(Oo,n._currentValue),n._currentValue=l;break;case 13:if(n=t.memoizedState,n!==null)return n.dehydrated!==null?(ue(fe,fe.current&1),t.flags|=128,null):(r&t.child.childLanes)!==0?th(e,t,r):(ue(fe,fe.current&1),e=jt(e,t,r),e!==null?e.sibling:null);ue(fe,fe.current&1);break;case 19:if(n=(r&t.childLanes)!==0,(e.flags&128)!==0){if(n)return rh(e,t,r);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),ue(fe,fe.current),n)break;return null;case 22:case 23:return t.lanes=0,qf(e,t,r)}return jt(e,t,r)}var nh,Qa,lh,oh;nh=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};Qa=function(){};lh=function(e,t,r,n){var l=e.memoizedProps;if(l!==n){e=t.stateNode,br(At.current);var o=null;switch(r){case"input":l=pa(e,l),n=pa(e,n),o=[];break;case"select":l=pe({},l,{value:void 0}),n=pe({},n,{value:void 0}),o=[];break;case"textarea":l=ga(e,l),n=ga(e,n),o=[];break;default:typeof l.onClick!="function"&&typeof n.onClick=="function"&&(e.onclick=Lo)}xa(r,n);var i;r=null;for(s in l)if(!n.hasOwnProperty(s)&&l.hasOwnProperty(s)&&l[s]!=null)if(s==="style"){var u=l[s];for(i in u)u.hasOwnProperty(i)&&(r||(r={}),r[i]="")}else s!=="dangerouslySetInnerHTML"&&s!=="children"&&s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(al.hasOwnProperty(s)?o||(o=[]):(o=o||[]).push(s,null));for(s in n){var a=n[s];if(u=l!=null?l[s]:void 0,n.hasOwnProperty(s)&&a!==u&&(a!=null||u!=null))if(s==="style")if(u){for(i in u)!u.hasOwnProperty(i)||a&&a.hasOwnProperty(i)||(r||(r={}),r[i]="");for(i in a)a.hasOwnProperty(i)&&u[i]!==a[i]&&(r||(r={}),r[i]=a[i])}else r||(o||(o=[]),o.push(s,r)),r=a;else s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,u=u?u.__html:void 0,a!=null&&u!==a&&(o=o||[]).push(s,a)):s==="children"?typeof a!="string"&&typeof a!="number"||(o=o||[]).push(s,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&(al.hasOwnProperty(s)?(a!=null&&s==="onScroll"&&se("scroll",e),o||u===a||(o=[])):(o=o||[]).push(s,a))}r&&(o=o||[]).push("style",r);var s=o;(t.updateQueue=s)&&(t.flags|=4)}};oh=function(e,t,r,n){r!==n&&(t.flags|=4)};function jn(e,t){if(!de)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;r!==null;)r.alternate!==null&&(n=r),r=r.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function Me(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,n=0;if(t)for(var l=e.child;l!==null;)r|=l.lanes|l.childLanes,n|=l.subtreeFlags&14680064,n|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)r|=l.lanes|l.childLanes,n|=l.subtreeFlags,n|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=n,e.childLanes=r,t}function wm(e,t,r){var n=t.pendingProps;switch(_u(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Me(t),null;case 1:return Je(t.type)&&Io(),Me(t),null;case 3:return n=t.stateNode,En(),ce(Ge),ce(ze),Uu(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Zl(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,wt!==null&&(eu(wt),wt=null))),Qa(e,t),Me(t),null;case 5:ju(t);var l=br(xl.current);if(r=t.type,e!==null&&t.stateNode!=null)lh(e,t,r,n,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!n){if(t.stateNode===null)throw Error(_(166));return Me(t),null}if(e=br(At.current),Zl(t)){n=t.stateNode,r=t.type;var o=t.memoizedProps;switch(n[Bt]=t,n[gl]=o,e=(t.mode&1)!==0,r){case"dialog":se("cancel",n),se("close",n);break;case"iframe":case"object":case"embed":se("load",n);break;case"video":case"audio":for(l=0;l<Gn.length;l++)se(Gn[l],n);break;case"source":se("error",n);break;case"img":case"image":case"link":se("error",n),se("load",n);break;case"details":se("toggle",n);break;case"input":Is(n,o),se("invalid",n);break;case"select":n._wrapperState={wasMultiple:!!o.multiple},se("invalid",n);break;case"textarea":Ts(n,o),se("invalid",n)}xa(r,o),l=null;for(var i in o)if(o.hasOwnProperty(i)){var u=o[i];i==="children"?typeof u=="string"?n.textContent!==u&&(o.suppressHydrationWarning!==!0&&ql(n.textContent,u,e),l=["children",u]):typeof u=="number"&&n.textContent!==""+u&&(o.suppressHydrationWarning!==!0&&ql(n.textContent,u,e),l=["children",""+u]):al.hasOwnProperty(i)&&u!=null&&i==="onScroll"&&se("scroll",n)}switch(r){case"input":Hl(n),Ms(n,o,!0);break;case"textarea":Hl(n),zs(n);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(n.onclick=Lo)}n=l,t.updateQueue=n,n!==null&&(t.flags|=4)}else{i=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Pd(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof n.is=="string"?e=i.createElement(r,{is:n.is}):(e=i.createElement(r),r==="select"&&(i=e,n.multiple?i.multiple=!0:n.size&&(i.size=n.size))):e=i.createElementNS(e,r),e[Bt]=t,e[gl]=n,nh(e,t,!1,!1),t.stateNode=e;e:{switch(i=wa(r,n),r){case"dialog":se("cancel",e),se("close",e),l=n;break;case"iframe":case"object":case"embed":se("load",e),l=n;break;case"video":case"audio":for(l=0;l<Gn.length;l++)se(Gn[l],e);l=n;break;case"source":se("error",e),l=n;break;case"img":case"image":case"link":se("error",e),se("load",e),l=n;break;case"details":se("toggle",e),l=n;break;case"input":Is(e,n),l=pa(e,n),se("invalid",e);break;case"option":l=n;break;case"select":e._wrapperState={wasMultiple:!!n.multiple},l=pe({},n,{value:void 0}),se("invalid",e);break;case"textarea":Ts(e,n),l=ga(e,n),se("invalid",e);break;default:l=n}xa(r,l),u=l;for(o in u)if(u.hasOwnProperty(o)){var a=u[o];o==="style"?Id(e,a):o==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&_d(e,a)):o==="children"?typeof a=="string"?(r!=="textarea"||a!=="")&&ul(e,a):typeof a=="number"&&ul(e,""+a):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(al.hasOwnProperty(o)?a!=null&&o==="onScroll"&&se("scroll",e):a!=null&&vu(e,o,a,i))}switch(r){case"input":Hl(e),Ms(e,n,!1);break;case"textarea":Hl(e),zs(e);break;case"option":n.value!=null&&e.setAttribute("value",""+fr(n.value));break;case"select":e.multiple=!!n.multiple,o=n.value,o!=null?dn(e,!!n.multiple,o,!1):n.defaultValue!=null&&dn(e,!!n.multiple,n.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=Lo)}switch(r){case"button":case"input":case"select":case"textarea":n=!!n.autoFocus;break e;case"img":n=!0;break e;default:n=!1}}n&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Me(t),null;case 6:if(e&&t.stateNode!=null)oh(e,t,e.memoizedProps,n);else{if(typeof n!="string"&&t.stateNode===null)throw Error(_(166));if(r=br(xl.current),br(At.current),Zl(t)){if(n=t.stateNode,r=t.memoizedProps,n[Bt]=t,(o=n.nodeValue!==r)&&(e=nt,e!==null))switch(e.tag){case 3:ql(n.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ql(n.nodeValue,r,(e.mode&1)!==0)}o&&(t.flags|=4)}else n=(r.nodeType===9?r:r.ownerDocument).createTextNode(n),n[Bt]=t,t.stateNode=n}return Me(t),null;case 13:if(ce(fe),n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(de&&rt!==null&&(t.mode&1)!==0&&(t.flags&128)===0)Ef(),kn(),t.flags|=98560,o=!1;else if(o=Zl(t),n!==null&&n.dehydrated!==null){if(e===null){if(!o)throw Error(_(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(_(317));o[Bt]=t}else kn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Me(t),o=!1}else wt!==null&&(eu(wt),wt=null),o=!0;if(!o)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=r,t):(n=n!==null,n!==(e!==null&&e.memoizedState!==null)&&n&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(fe.current&1)!==0?Fe===0&&(Fe=3):es())),t.updateQueue!==null&&(t.flags|=4),Me(t),null);case 4:return En(),Qa(e,t),e===null&&ml(t.stateNode.containerInfo),Me(t),null;case 10:return Tu(t.type._context),Me(t),null;case 17:return Je(t.type)&&Io(),Me(t),null;case 19:if(ce(fe),o=t.memoizedState,o===null)return Me(t),null;if(n=(t.flags&128)!==0,i=o.rendering,i===null)if(n)jn(o,!1);else{if(Fe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(i=Uo(e),i!==null){for(t.flags|=128,jn(o,!1),n=i.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),t.subtreeFlags=0,n=r,r=t.child;r!==null;)o=r,e=n,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return ue(fe,fe.current&1|2),t.child}e=e.sibling}o.tail!==null&&ve()>Nn&&(t.flags|=128,n=!0,jn(o,!1),t.lanes=4194304)}else{if(!n)if(e=Uo(i),e!==null){if(t.flags|=128,n=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),jn(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!de)return Me(t),null}else 2*ve()-o.renderingStartTime>Nn&&r!==1073741824&&(t.flags|=128,n=!0,jn(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(r=o.last,r!==null?r.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=ve(),t.sibling=null,r=fe.current,ue(fe,n?r&1|2:r&1),t):(Me(t),null);case 22:case 23:return Zu(),n=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==n&&(t.flags|=8192),n&&(t.mode&1)!==0?(Ze&1073741824)!==0&&(Me(t),t.subtreeFlags&6&&(t.flags|=8192)):Me(t),null;case 24:return null;case 25:return null}throw Error(_(156,t.tag))}function km(e,t){switch(_u(t),t.tag){case 1:return Je(t.type)&&Io(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return En(),ce(Ge),ce(ze),Uu(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return ju(t),null;case 13:if(ce(fe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(_(340));kn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ce(fe),null;case 4:return En(),null;case 10:return Tu(t.type._context),null;case 22:case 23:return Zu(),null;case 24:return null;default:return null}}var ro=!1,Te=!1,Sm=typeof WeakSet=="function"?WeakSet:Set,T=null;function un(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(n){me(e,t,n)}else r.current=null}function Ka(e,t,r){try{r()}catch(n){me(e,t,n)}}var Fc=!1;function Em(e,t){if(Aa=bo,e=cf(),bu(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var n=r.getSelection&&r.getSelection();if(n&&n.rangeCount!==0){r=n.anchorNode;var l=n.anchorOffset,o=n.focusNode;n=n.focusOffset;try{r.nodeType,o.nodeType}catch{r=null;break e}var i=0,u=-1,a=-1,s=0,d=0,y=e,p=null;t:for(;;){for(var x;y!==r||l!==0&&y.nodeType!==3||(u=i+l),y!==o||n!==0&&y.nodeType!==3||(a=i+n),y.nodeType===3&&(i+=y.nodeValue.length),(x=y.firstChild)!==null;)p=y,y=x;for(;;){if(y===e)break t;if(p===r&&++s===l&&(u=i),p===o&&++d===n&&(a=i),(x=y.nextSibling)!==null)break;y=p,p=y.parentNode}y=x}r=u===-1||a===-1?null:{start:u,end:a}}else r=null}r=r||{start:0,end:0}}else r=null;for(ba={focusedElem:e,selectionRange:r},bo=!1,T=t;T!==null;)if(t=T,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,T=e;else for(;T!==null;){t=T;try{var w=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var N=w.memoizedProps,A=w.memoizedState,m=t.stateNode,h=m.getSnapshotBeforeUpdate(t.elementType===t.type?N:vt(t.type,N),A);m.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var f=t.stateNode.containerInfo;f.nodeType===1?f.textContent="":f.nodeType===9&&f.documentElement&&f.removeChild(f.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(_(163))}}catch(k){me(t,t.return,k)}if(e=t.sibling,e!==null){e.return=t.return,T=e;break}T=t.return}return w=Fc,Fc=!1,w}function ll(e,t,r){var n=t.updateQueue;if(n=n!==null?n.lastEffect:null,n!==null){var l=n=n.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&Ka(t,r,o)}l=l.next}while(l!==n)}}function pi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var n=r.create;r.destroy=n()}r=r.next}while(r!==t)}}function Ya(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function ih(e){var t=e.alternate;t!==null&&(e.alternate=null,ih(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Bt],delete t[gl],delete t[La],delete t[om],delete t[im])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ah(e){return e.tag===5||e.tag===3||e.tag===4}function Bc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ah(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ga(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=Lo));else if(n!==4&&(e=e.child,e!==null))for(Ga(e,t,r),e=e.sibling;e!==null;)Ga(e,t,r),e=e.sibling}function Ja(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(n!==4&&(e=e.child,e!==null))for(Ja(e,t,r),e=e.sibling;e!==null;)Ja(e,t,r),e=e.sibling}var be=null,xt=!1;function Gt(e,t,r){for(r=r.child;r!==null;)uh(e,t,r),r=r.sibling}function uh(e,t,r){if(Rt&&typeof Rt.onCommitFiberUnmount=="function")try{Rt.onCommitFiberUnmount(ii,r)}catch{}switch(r.tag){case 5:Te||un(r,t);case 6:var n=be,l=xt;be=null,Gt(e,t,r),be=n,xt=l,be!==null&&(xt?(e=be,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):be.removeChild(r.stateNode));break;case 18:be!==null&&(xt?(e=be,r=r.stateNode,e.nodeType===8?Ki(e.parentNode,r):e.nodeType===1&&Ki(e,r),fl(e)):Ki(be,r.stateNode));break;case 4:n=be,l=xt,be=r.stateNode.containerInfo,xt=!0,Gt(e,t,r),be=n,xt=l;break;case 0:case 11:case 14:case 15:if(!Te&&(n=r.updateQueue,n!==null&&(n=n.lastEffect,n!==null))){l=n=n.next;do{var o=l,i=o.destroy;o=o.tag,i!==void 0&&((o&2)!==0||(o&4)!==0)&&Ka(r,t,i),l=l.next}while(l!==n)}Gt(e,t,r);break;case 1:if(!Te&&(un(r,t),n=r.stateNode,typeof n.componentWillUnmount=="function"))try{n.props=r.memoizedProps,n.state=r.memoizedState,n.componentWillUnmount()}catch(u){me(r,t,u)}Gt(e,t,r);break;case 21:Gt(e,t,r);break;case 22:r.mode&1?(Te=(n=Te)||r.memoizedState!==null,Gt(e,t,r),Te=n):Gt(e,t,r);break;default:Gt(e,t,r)}}function Rc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new Sm),t.forEach(function(n){var l=Pm.bind(null,e,n);r.has(n)||(r.add(n),n.then(l,l))})}}function gt(e,t){var r=t.deletions;if(r!==null)for(var n=0;n<r.length;n++){var l=r[n];try{var o=e,i=t,u=i;e:for(;u!==null;){switch(u.tag){case 5:be=u.stateNode,xt=!1;break e;case 3:be=u.stateNode.containerInfo,xt=!0;break e;case 4:be=u.stateNode.containerInfo,xt=!0;break e}u=u.return}if(be===null)throw Error(_(160));uh(o,i,l),be=null,xt=!1;var a=l.alternate;a!==null&&(a.return=null),l.return=null}catch(s){me(l,t,s)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)sh(t,e),t=t.sibling}function sh(e,t){var r=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(gt(t,e),Dt(e),n&4){try{ll(3,e,e.return),pi(3,e)}catch(N){me(e,e.return,N)}try{ll(5,e,e.return)}catch(N){me(e,e.return,N)}}break;case 1:gt(t,e),Dt(e),n&512&&r!==null&&un(r,r.return);break;case 5:if(gt(t,e),Dt(e),n&512&&r!==null&&un(r,r.return),e.flags&32){var l=e.stateNode;try{ul(l,"")}catch(N){me(e,e.return,N)}}if(n&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,i=r!==null?r.memoizedProps:o,u=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{u==="input"&&o.type==="radio"&&o.name!=null&&Ad(l,o),wa(u,i);var s=wa(u,o);for(i=0;i<a.length;i+=2){var d=a[i],y=a[i+1];d==="style"?Id(l,y):d==="dangerouslySetInnerHTML"?_d(l,y):d==="children"?ul(l,y):vu(l,d,y,s)}switch(u){case"input":ma(l,o);break;case"textarea":bd(l,o);break;case"select":var p=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var x=o.value;x!=null?dn(l,!!o.multiple,x,!1):p!==!!o.multiple&&(o.defaultValue!=null?dn(l,!!o.multiple,o.defaultValue,!0):dn(l,!!o.multiple,o.multiple?[]:"",!1))}l[gl]=o}catch(N){me(e,e.return,N)}}break;case 6:if(gt(t,e),Dt(e),n&4){if(e.stateNode===null)throw Error(_(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(N){me(e,e.return,N)}}break;case 3:if(gt(t,e),Dt(e),n&4&&r!==null&&r.memoizedState.isDehydrated)try{fl(t.containerInfo)}catch(N){me(e,e.return,N)}break;case 4:gt(t,e),Dt(e);break;case 13:gt(t,e),Dt(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(Xu=ve())),n&4&&Rc(e);break;case 22:if(d=r!==null&&r.memoizedState!==null,e.mode&1?(Te=(s=Te)||d,gt(t,e),Te=s):gt(t,e),Dt(e),n&8192){if(s=e.memoizedState!==null,(e.stateNode.isHidden=s)&&!d&&(e.mode&1)!==0)for(T=e,d=e.child;d!==null;){for(y=T=d;T!==null;){switch(p=T,x=p.child,p.tag){case 0:case 11:case 14:case 15:ll(4,p,p.return);break;case 1:un(p,p.return);var w=p.stateNode;if(typeof w.componentWillUnmount=="function"){n=p,r=p.return;try{t=n,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(N){me(n,r,N)}}break;case 5:un(p,p.return);break;case 22:if(p.memoizedState!==null){bc(y);continue}}x!==null?(x.return=p,T=x):bc(y)}d=d.sibling}e:for(d=null,y=e;;){if(y.tag===5){if(d===null){d=y;try{l=y.stateNode,s?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(u=y.stateNode,a=y.memoizedProps.style,i=a!=null&&a.hasOwnProperty("display")?a.display:null,u.style.display=Ld("display",i))}catch(N){me(e,e.return,N)}}}else if(y.tag===6){if(d===null)try{y.stateNode.nodeValue=s?"":y.memoizedProps}catch(N){me(e,e.return,N)}}else if((y.tag!==22&&y.tag!==23||y.memoizedState===null||y===e)&&y.child!==null){y.child.return=y,y=y.child;continue}if(y===e)break e;for(;y.sibling===null;){if(y.return===null||y.return===e)break e;d===y&&(d=null),y=y.return}d===y&&(d=null),y.sibling.return=y.return,y=y.sibling}}break;case 19:gt(t,e),Dt(e),n&4&&Rc(e);break;case 21:break;default:gt(t,e),Dt(e)}}function Dt(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(ah(r)){var n=r;break e}r=r.return}throw Error(_(160))}switch(n.tag){case 5:var l=n.stateNode;n.flags&32&&(ul(l,""),n.flags&=-33);var o=Bc(e);Ja(e,o,l);break;case 3:case 4:var i=n.stateNode.containerInfo,u=Bc(e);Ga(e,u,i);break;default:throw Error(_(161))}}catch(a){me(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Cm(e,t,r){T=e,ch(e)}function ch(e,t,r){for(var n=(e.mode&1)!==0;T!==null;){var l=T,o=l.child;if(l.tag===22&&n){var i=l.memoizedState!==null||ro;if(!i){var u=l.alternate,a=u!==null&&u.memoizedState!==null||Te;u=ro;var s=Te;if(ro=i,(Te=a)&&!s)for(T=l;T!==null;)i=T,a=i.child,i.tag===22&&i.memoizedState!==null?Pc(l):a!==null?(a.return=i,T=a):Pc(l);for(;o!==null;)T=o,ch(o),o=o.sibling;T=l,ro=u,Te=s}Ac(e)}else(l.subtreeFlags&8772)!==0&&o!==null?(o.return=l,T=o):Ac(e)}}function Ac(e){for(;T!==null;){var t=T;if((t.flags&8772)!==0){var r=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:Te||pi(5,t);break;case 1:var n=t.stateNode;if(t.flags&4&&!Te)if(r===null)n.componentDidMount();else{var l=t.elementType===t.type?r.memoizedProps:vt(t.type,r.memoizedProps);n.componentDidUpdate(l,r.memoizedState,n.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&pc(t,o,n);break;case 3:var i=t.updateQueue;if(i!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}pc(t,i,r)}break;case 5:var u=t.stateNode;if(r===null&&t.flags&4){r=u;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&r.focus();break;case"img":a.src&&(r.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var s=t.alternate;if(s!==null){var d=s.memoizedState;if(d!==null){var y=d.dehydrated;y!==null&&fl(y)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(_(163))}Te||t.flags&512&&Ya(t)}catch(p){me(t,t.return,p)}}if(t===e){T=null;break}if(r=t.sibling,r!==null){r.return=t.return,T=r;break}T=t.return}}function bc(e){for(;T!==null;){var t=T;if(t===e){T=null;break}var r=t.sibling;if(r!==null){r.return=t.return,T=r;break}T=t.return}}function Pc(e){for(;T!==null;){var t=T;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{pi(4,t)}catch(a){me(t,r,a)}break;case 1:var n=t.stateNode;if(typeof n.componentDidMount=="function"){var l=t.return;try{n.componentDidMount()}catch(a){me(t,l,a)}}var o=t.return;try{Ya(t)}catch(a){me(t,o,a)}break;case 5:var i=t.return;try{Ya(t)}catch(a){me(t,i,a)}}}catch(a){me(t,t.return,a)}if(t===e){T=null;break}var u=t.sibling;if(u!==null){u.return=t.return,T=u;break}T=t.return}}var Nm=Math.ceil,Wo=Ut.ReactCurrentDispatcher,Gu=Ut.ReactCurrentOwner,ht=Ut.ReactCurrentBatchConfig,re=0,Ae=null,Ee=null,Pe=0,Ze=0,sn=yr(0),Fe=0,El=null,zr=0,mi=0,Ju=0,ol=null,Ke=null,Xu=0,Nn=1/0,_t=null,Qo=!1,Xa=null,sr=null,no=!1,nr=null,Ko=0,il=0,qa=null,vo=-1,xo=0;function He(){return(re&6)!==0?ve():vo!==-1?vo:vo=ve()}function cr(e){return(e.mode&1)===0?1:(re&2)!==0&&Pe!==0?Pe&-Pe:um.transition!==null?(xo===0&&(xo=Kd()),xo):(e=oe,e!==0||(e=window.event,e=e===void 0?16:ef(e.type)),e)}function St(e,t,r,n){if(50<il)throw il=0,qa=null,Error(_(185));Bl(e,r,n),((re&2)===0||e!==Ae)&&(e===Ae&&((re&2)===0&&(mi|=r),Fe===4&&er(e,Pe)),Xe(e,n),r===1&&re===0&&(t.mode&1)===0&&(Nn=ve()+500,di&&gr()))}function Xe(e,t){var r=e.callbackNode;up(e,t);var n=Ao(e,e===Ae?Pe:0);if(n===0)r!==null&&js(r),e.callbackNode=null,e.callbackPriority=0;else if(t=n&-n,e.callbackPriority!==t){if(r!=null&&js(r),t===1)e.tag===0?am(_c.bind(null,e)):wf(_c.bind(null,e)),nm(function(){(re&6)===0&&gr()}),r=null;else{switch(Yd(n)){case 1:r=Eu;break;case 4:r=Wd;break;case 16:r=Ro;break;case 536870912:r=Qd;break;default:r=Ro}r=vh(r,dh.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function dh(e,t){if(vo=-1,xo=0,(re&6)!==0)throw Error(_(327));var r=e.callbackNode;if(yn()&&e.callbackNode!==r)return null;var n=Ao(e,e===Ae?Pe:0);if(n===0)return null;if((n&30)!==0||(n&e.expiredLanes)!==0||t)t=Yo(e,n);else{t=n;var l=re;re|=2;var o=hh();(Ae!==e||Pe!==t)&&(_t=null,Nn=ve()+500,_r(e,t));do try{Bm();break}catch(u){fh(e,u)}while(1);Mu(),Wo.current=o,re=l,Ee!==null?t=0:(Ae=null,Pe=0,t=Fe)}if(t!==0){if(t===2&&(l=Na(e),l!==0&&(n=l,t=Za(e,l))),t===1)throw r=El,_r(e,0),er(e,n),Xe(e,ve()),r;if(t===6)er(e,n);else{if(l=e.current.alternate,(n&30)===0&&!Dm(l)&&(t=Yo(e,n),t===2&&(o=Na(e),o!==0&&(n=o,t=Za(e,o))),t===1))throw r=El,_r(e,0),er(e,n),Xe(e,ve()),r;switch(e.finishedWork=l,e.finishedLanes=n,t){case 0:case 1:throw Error(_(345));case 2:Fr(e,Ke,_t);break;case 3:if(er(e,n),(n&130023424)===n&&(t=Xu+500-ve(),10<t)){if(Ao(e,0)!==0)break;if(l=e.suspendedLanes,(l&n)!==n){He(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=_a(Fr.bind(null,e,Ke,_t),t);break}Fr(e,Ke,_t);break;case 4:if(er(e,n),(n&4194240)===n)break;for(t=e.eventTimes,l=-1;0<n;){var i=31-kt(n);o=1<<i,i=t[i],i>l&&(l=i),n&=~o}if(n=l,n=ve()-n,n=(120>n?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*Nm(n/1960))-n,10<n){e.timeoutHandle=_a(Fr.bind(null,e,Ke,_t),n);break}Fr(e,Ke,_t);break;case 5:Fr(e,Ke,_t);break;default:throw Error(_(329))}}}return Xe(e,ve()),e.callbackNode===r?dh.bind(null,e):null}function Za(e,t){var r=ol;return e.current.memoizedState.isDehydrated&&(_r(e,t).flags|=256),e=Yo(e,t),e!==2&&(t=Ke,Ke=r,t!==null&&eu(t)),e}function eu(e){Ke===null?Ke=e:Ke.push.apply(Ke,e)}function Dm(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var n=0;n<r.length;n++){var l=r[n],o=l.getSnapshot;l=l.value;try{if(!Et(o(),l))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function er(e,t){for(t&=~Ju,t&=~mi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-kt(t),n=1<<r;e[r]=-1,t&=~n}}function _c(e){if((re&6)!==0)throw Error(_(327));yn();var t=Ao(e,0);if((t&1)===0)return Xe(e,ve()),null;var r=Yo(e,t);if(e.tag!==0&&r===2){var n=Na(e);n!==0&&(t=n,r=Za(e,n))}if(r===1)throw r=El,_r(e,0),er(e,t),Xe(e,ve()),r;if(r===6)throw Error(_(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Fr(e,Ke,_t),Xe(e,ve()),null}function qu(e,t){var r=re;re|=1;try{return e(t)}finally{re=r,re===0&&(Nn=ve()+500,di&&gr())}}function Or(e){nr!==null&&nr.tag===0&&(re&6)===0&&yn();var t=re;re|=1;var r=ht.transition,n=oe;try{if(ht.transition=null,oe=1,e)return e()}finally{oe=n,ht.transition=r,re=t,(re&6)===0&&gr()}}function Zu(){Ze=sn.current,ce(sn)}function _r(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,rm(r)),Ee!==null)for(r=Ee.return;r!==null;){var n=r;switch(_u(n),n.tag){case 1:n=n.type.childContextTypes,n!=null&&Io();break;case 3:En(),ce(Ge),ce(ze),Uu();break;case 5:ju(n);break;case 4:En();break;case 13:ce(fe);break;case 19:ce(fe);break;case 10:Tu(n.type._context);break;case 22:case 23:Zu()}r=r.return}if(Ae=e,Ee=e=dr(e.current,null),Pe=Ze=t,Fe=0,El=null,Ju=mi=zr=0,Ke=ol=null,Ar!==null){for(t=0;t<Ar.length;t++)if(r=Ar[t],n=r.interleaved,n!==null){r.interleaved=null;var l=n.next,o=r.pending;if(o!==null){var i=o.next;o.next=l,n.next=i}r.pending=n}Ar=null}return e}function fh(e,t){do{var r=Ee;try{if(Mu(),mo.current=Ho,Vo){for(var n=he.memoizedState;n!==null;){var l=n.queue;l!==null&&(l.pending=null),n=n.next}Vo=!1}if(Tr=0,Re=De=he=null,nl=!1,wl=0,Gu.current=null,r===null||r.return===null){Fe=1,El=t,Ee=null;break}e:{var o=e,i=r.return,u=r,a=t;if(t=Pe,u.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var s=a,d=u,y=d.tag;if((d.mode&1)===0&&(y===0||y===11||y===15)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var x=wc(i);if(x!==null){x.flags&=-257,kc(x,i,u,o,t),x.mode&1&&xc(o,s,t),t=x,a=s;var w=t.updateQueue;if(w===null){var N=new Set;N.add(a),t.updateQueue=N}else w.add(a);break e}else{if((t&1)===0){xc(o,s,t),es();break e}a=Error(_(426))}}else if(de&&u.mode&1){var A=wc(i);if(A!==null){(A.flags&65536)===0&&(A.flags|=256),kc(A,i,u,o,t),Lu(Cn(a,u));break e}}o=a=Cn(a,u),Fe!==4&&(Fe=2),ol===null?ol=[o]:ol.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=Gf(o,a,t);hc(o,m);break e;case 1:u=a;var h=o.type,f=o.stateNode;if((o.flags&128)===0&&(typeof h.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(sr===null||!sr.has(f)))){o.flags|=65536,t&=-t,o.lanes|=t;var k=Jf(o,u,t);hc(o,k);break e}}o=o.return}while(o!==null)}mh(r)}catch(B){t=B,Ee===r&&r!==null&&(Ee=r=r.return);continue}break}while(1)}function hh(){var e=Wo.current;return Wo.current=Ho,e===null?Ho:e}function es(){(Fe===0||Fe===3||Fe===2)&&(Fe=4),Ae===null||(zr&268435455)===0&&(mi&268435455)===0||er(Ae,Pe)}function Yo(e,t){var r=re;re|=2;var n=hh();(Ae!==e||Pe!==t)&&(_t=null,_r(e,t));do try{Fm();break}catch(l){fh(e,l)}while(1);if(Mu(),re=r,Wo.current=n,Ee!==null)throw Error(_(261));return Ae=null,Pe=0,Fe}function Fm(){for(;Ee!==null;)ph(Ee)}function Bm(){for(;Ee!==null&&!Z0();)ph(Ee)}function ph(e){var t=gh(e.alternate,e,Ze);e.memoizedProps=e.pendingProps,t===null?mh(e):Ee=t,Gu.current=null}function mh(e){var t=e;do{var r=t.alternate;if(e=t.return,(t.flags&32768)===0){if(r=wm(r,t,Ze),r!==null){Ee=r;return}}else{if(r=km(r,t),r!==null){r.flags&=32767,Ee=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Fe=6,Ee=null;return}}if(t=t.sibling,t!==null){Ee=t;return}Ee=t=e}while(t!==null);Fe===0&&(Fe=5)}function Fr(e,t,r){var n=oe,l=ht.transition;try{ht.transition=null,oe=1,Rm(e,t,r,n)}finally{ht.transition=l,oe=n}return null}function Rm(e,t,r,n){do yn();while(nr!==null);if((re&6)!==0)throw Error(_(327));r=e.finishedWork;var l=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(_(177));e.callbackNode=null,e.callbackPriority=0;var o=r.lanes|r.childLanes;if(sp(e,o),e===Ae&&(Ee=Ae=null,Pe=0),(r.subtreeFlags&2064)===0&&(r.flags&2064)===0||no||(no=!0,vh(Ro,function(){return yn(),null})),o=(r.flags&15990)!==0,(r.subtreeFlags&15990)!==0||o){o=ht.transition,ht.transition=null;var i=oe;oe=1;var u=re;re|=4,Gu.current=null,Em(e,r),sh(r,e),Gp(ba),bo=!!Aa,ba=Aa=null,e.current=r,Cm(r),ep(),re=u,oe=i,ht.transition=o}else e.current=r;if(no&&(no=!1,nr=e,Ko=l),o=e.pendingLanes,o===0&&(sr=null),np(r.stateNode),Xe(e,ve()),t!==null)for(n=e.onRecoverableError,r=0;r<t.length;r++)l=t[r],n(l.value,{componentStack:l.stack,digest:l.digest});if(Qo)throw Qo=!1,e=Xa,Xa=null,e;return(Ko&1)!==0&&e.tag!==0&&yn(),o=e.pendingLanes,(o&1)!==0?e===qa?il++:(il=0,qa=e):il=0,gr(),null}function yn(){if(nr!==null){var e=Yd(Ko),t=ht.transition,r=oe;try{if(ht.transition=null,oe=16>e?16:e,nr===null)var n=!1;else{if(e=nr,nr=null,Ko=0,(re&6)!==0)throw Error(_(331));var l=re;for(re|=4,T=e.current;T!==null;){var o=T,i=o.child;if((T.flags&16)!==0){var u=o.deletions;if(u!==null){for(var a=0;a<u.length;a++){var s=u[a];for(T=s;T!==null;){var d=T;switch(d.tag){case 0:case 11:case 15:ll(8,d,o)}var y=d.child;if(y!==null)y.return=d,T=y;else for(;T!==null;){d=T;var p=d.sibling,x=d.return;if(ih(d),d===s){T=null;break}if(p!==null){p.return=x,T=p;break}T=x}}}var w=o.alternate;if(w!==null){var N=w.child;if(N!==null){w.child=null;do{var A=N.sibling;N.sibling=null,N=A}while(N!==null)}}T=o}}if((o.subtreeFlags&2064)!==0&&i!==null)i.return=o,T=i;else e:for(;T!==null;){if(o=T,(o.flags&2048)!==0)switch(o.tag){case 0:case 11:case 15:ll(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,T=m;break e}T=o.return}}var h=e.current;for(T=h;T!==null;){i=T;var f=i.child;if((i.subtreeFlags&2064)!==0&&f!==null)f.return=i,T=f;else e:for(i=h;T!==null;){if(u=T,(u.flags&2048)!==0)try{switch(u.tag){case 0:case 11:case 15:pi(9,u)}}catch(B){me(u,u.return,B)}if(u===i){T=null;break e}var k=u.sibling;if(k!==null){k.return=u.return,T=k;break e}T=u.return}}if(re=l,gr(),Rt&&typeof Rt.onPostCommitFiberRoot=="function")try{Rt.onPostCommitFiberRoot(ii,e)}catch{}n=!0}return n}finally{oe=r,ht.transition=t}}return!1}function Lc(e,t,r){t=Cn(r,t),t=Gf(e,t,1),e=ur(e,t,1),t=He(),e!==null&&(Bl(e,1,t),Xe(e,t))}function me(e,t,r){if(e.tag===3)Lc(e,e,r);else for(;t!==null;){if(t.tag===3){Lc(t,e,r);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(sr===null||!sr.has(n))){e=Cn(r,e),e=Jf(t,e,1),t=ur(t,e,1),e=He(),t!==null&&(Bl(t,1,e),Xe(t,e));break}}t=t.return}}function Am(e,t,r){var n=e.pingCache;n!==null&&n.delete(t),t=He(),e.pingedLanes|=e.suspendedLanes&r,Ae===e&&(Pe&r)===r&&(Fe===4||Fe===3&&(Pe&130023424)===Pe&&500>ve()-Xu?_r(e,0):Ju|=r),Xe(e,t)}function yh(e,t){t===0&&((e.mode&1)===0?t=1:(t=Kl,Kl<<=1,(Kl&130023424)===0&&(Kl=4194304)));var r=He();e=$t(e,t),e!==null&&(Bl(e,t,r),Xe(e,r))}function bm(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),yh(e,r)}function Pm(e,t){var r=0;switch(e.tag){case 13:var n=e.stateNode,l=e.memoizedState;l!==null&&(r=l.retryLane);break;case 19:n=e.stateNode;break;default:throw Error(_(314))}n!==null&&n.delete(t),yh(e,r)}var gh;gh=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ge.current)Ye=!0;else{if((e.lanes&r)===0&&(t.flags&128)===0)return Ye=!1,xm(e,t,r);Ye=(e.flags&131072)!==0}else Ye=!1,de&&(t.flags&1048576)!==0&&kf(t,zo,t.index);switch(t.lanes=0,t.tag){case 2:var n=t.type;go(e,t),e=t.pendingProps;var l=wn(t,ze.current);mn(t,r),l=Hu(null,t,n,e,l,r);var o=Wu();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Je(n)?(o=!0,Mo(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Ou(t),l.updater=hi,t.stateNode=l,l._reactInternals=t,$a(t,n,e,r),t=Va(null,t,n,!0,o,r)):(t.tag=0,de&&o&&Pu(t),Ve(null,t,l,r),t=t.child),t;case 16:n=t.elementType;e:{switch(go(e,t),e=t.pendingProps,l=n._init,n=l(n._payload),t.type=n,l=t.tag=Lm(n),e=vt(n,e),l){case 0:t=Ua(null,t,n,e,r);break e;case 1:t=Cc(null,t,n,e,r);break e;case 11:t=Sc(null,t,n,e,r);break e;case 14:t=Ec(null,t,n,vt(n.type,e),r);break e}throw Error(_(306,n,""))}return t;case 0:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:vt(n,l),Ua(e,t,n,l,r);case 1:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:vt(n,l),Cc(e,t,n,l,r);case 3:e:{if(eh(t),e===null)throw Error(_(387));n=t.pendingProps,o=t.memoizedState,l=o.element,Ff(e,t),jo(t,n,null,r);var i=t.memoizedState;if(n=i.element,o.isDehydrated)if(o={element:n,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=Cn(Error(_(423)),t),t=Nc(e,t,n,r,l);break e}else if(n!==l){l=Cn(Error(_(424)),t),t=Nc(e,t,n,r,l);break e}else for(rt=ar(t.stateNode.containerInfo.firstChild),nt=t,de=!0,wt=null,r=Nf(t,null,n,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(kn(),n===l){t=jt(e,t,r);break e}Ve(e,t,n,r)}t=t.child}return t;case 5:return Bf(t),e===null&&Ta(t),n=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,i=l.children,Pa(n,l)?i=null:o!==null&&Pa(n,o)&&(t.flags|=32),Zf(e,t),Ve(e,t,i,r),t.child;case 6:return e===null&&Ta(t),null;case 13:return th(e,t,r);case 4:return $u(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=Sn(t,null,n,r):Ve(e,t,n,r),t.child;case 11:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:vt(n,l),Sc(e,t,n,l,r);case 7:return Ve(e,t,t.pendingProps,r),t.child;case 8:return Ve(e,t,t.pendingProps.children,r),t.child;case 12:return Ve(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(n=t.type._context,l=t.pendingProps,o=t.memoizedProps,i=l.value,ue(Oo,n._currentValue),n._currentValue=i,o!==null)if(Et(o.value,i)){if(o.children===l.children&&!Ge.current){t=jt(e,t,r);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var u=o.dependencies;if(u!==null){i=o.child;for(var a=u.firstContext;a!==null;){if(a.context===n){if(o.tag===1){a=Tt(-1,r&-r),a.tag=2;var s=o.updateQueue;if(s!==null){s=s.shared;var d=s.pending;d===null?a.next=a:(a.next=d.next,d.next=a),s.pending=a}}o.lanes|=r,a=o.alternate,a!==null&&(a.lanes|=r),za(o.return,r,t),u.lanes|=r;break}a=a.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(_(341));i.lanes|=r,u=i.alternate,u!==null&&(u.lanes|=r),za(i,r,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}Ve(e,t,l.children,r),t=t.child}return t;case 9:return l=t.type,n=t.pendingProps.children,mn(t,r),l=pt(l),n=n(l),t.flags|=1,Ve(e,t,n,r),t.child;case 14:return n=t.type,l=vt(n,t.pendingProps),l=vt(n.type,l),Ec(e,t,n,l,r);case 15:return Xf(e,t,t.type,t.pendingProps,r);case 17:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:vt(n,l),go(e,t),t.tag=1,Je(n)?(e=!0,Mo(t)):e=!1,mn(t,r),Yf(t,n,l),$a(t,n,l,r),Va(null,t,n,!0,e,r);case 19:return rh(e,t,r);case 22:return qf(e,t,r)}throw Error(_(156,t.tag))};function vh(e,t){return Hd(e,t)}function _m(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ft(e,t,r,n){return new _m(e,t,r,n)}function ts(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Lm(e){if(typeof e=="function")return ts(e)?1:0;if(e!=null){if(e=e.$$typeof,e===wu)return 11;if(e===ku)return 14}return 2}function dr(e,t){var r=e.alternate;return r===null?(r=ft(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function wo(e,t,r,n,l,o){var i=2;if(n=e,typeof e=="function")ts(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case qr:return Lr(r.children,l,o,t);case xu:i=8,l|=8;break;case ca:return e=ft(12,r,t,l|2),e.elementType=ca,e.lanes=o,e;case da:return e=ft(13,r,t,l),e.elementType=da,e.lanes=o,e;case fa:return e=ft(19,r,t,l),e.elementType=fa,e.lanes=o,e;case Fd:return yi(r,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Nd:i=10;break e;case Dd:i=9;break e;case wu:i=11;break e;case ku:i=14;break e;case Xt:i=16,n=null;break e}throw Error(_(130,e==null?e:typeof e,""))}return t=ft(i,r,t,l),t.elementType=e,t.type=n,t.lanes=o,t}function Lr(e,t,r,n){return e=ft(7,e,n,t),e.lanes=r,e}function yi(e,t,r,n){return e=ft(22,e,n,t),e.elementType=Fd,e.lanes=r,e.stateNode={isHidden:!1},e}function ta(e,t,r){return e=ft(6,e,null,t),e.lanes=r,e}function ra(e,t,r){return t=ft(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Im(e,t,r,n,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Mi(0),this.expirationTimes=Mi(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Mi(0),this.identifierPrefix=n,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function rs(e,t,r,n,l,o,i,u,a){return e=new Im(e,t,r,u,a),t===1?(t=1,o===!0&&(t|=8)):t=0,o=ft(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:n,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ou(o),e}function Mm(e,t,r){var n=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Xr,key:n==null?null:""+n,children:e,containerInfo:t,implementation:r}}function xh(e){if(!e)return hr;e=e._reactInternals;e:{if(jr(e)!==e||e.tag!==1)throw Error(_(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Je(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(_(171))}if(e.tag===1){var r=e.type;if(Je(r))return xf(e,r,t)}return t}function wh(e,t,r,n,l,o,i,u,a){return e=rs(r,n,!0,e,l,o,i,u,a),e.context=xh(null),r=e.current,n=He(),l=cr(r),o=Tt(n,l),o.callback=t!=null?t:null,ur(r,o,l),e.current.lanes=l,Bl(e,l,n),Xe(e,n),e}function gi(e,t,r,n){var l=t.current,o=He(),i=cr(l);return r=xh(r),t.context===null?t.context=r:t.pendingContext=r,t=Tt(o,i),t.payload={element:e},n=n===void 0?null:n,n!==null&&(t.callback=n),e=ur(l,t,i),e!==null&&(St(e,l,i,o),po(e,l,i)),i}function Go(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ic(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function ns(e,t){Ic(e,t),(e=e.alternate)&&Ic(e,t)}function Tm(){return null}var kh=typeof reportError=="function"?reportError:function(e){console.error(e)};function ls(e){this._internalRoot=e}vi.prototype.render=ls.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(_(409));gi(e,t,null,null)};vi.prototype.unmount=ls.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Or(function(){gi(null,e,null,null)}),t[Ot]=null}};function vi(e){this._internalRoot=e}vi.prototype.unstable_scheduleHydration=function(e){if(e){var t=Xd();e={blockedOn:null,target:e,priority:t};for(var r=0;r<Zt.length&&t!==0&&t<Zt[r].priority;r++);Zt.splice(r,0,e),r===0&&Zd(e)}};function os(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function xi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Mc(){}function zm(e,t,r,n,l){if(l){if(typeof n=="function"){var o=n;n=function(){var s=Go(i);o.call(s)}}var i=wh(t,n,e,0,null,!1,!1,"",Mc);return e._reactRootContainer=i,e[Ot]=i.current,ml(e.nodeType===8?e.parentNode:e),Or(),i}for(;l=e.lastChild;)e.removeChild(l);if(typeof n=="function"){var u=n;n=function(){var s=Go(a);u.call(s)}}var a=rs(e,0,!1,null,null,!1,!1,"",Mc);return e._reactRootContainer=a,e[Ot]=a.current,ml(e.nodeType===8?e.parentNode:e),Or(function(){gi(t,a,r,n)}),a}function wi(e,t,r,n,l){var o=r._reactRootContainer;if(o){var i=o;if(typeof l=="function"){var u=l;l=function(){var a=Go(i);u.call(a)}}gi(t,i,e,l)}else i=zm(r,t,e,l,n);return Go(i)}Gd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=Yn(t.pendingLanes);r!==0&&(Cu(t,r|1),Xe(t,ve()),(re&6)===0&&(Nn=ve()+500,gr()))}break;case 13:Or(function(){var n=$t(e,1);if(n!==null){var l=He();St(n,e,1,l)}}),ns(e,1)}};Nu=function(e){if(e.tag===13){var t=$t(e,134217728);if(t!==null){var r=He();St(t,e,134217728,r)}ns(e,134217728)}};Jd=function(e){if(e.tag===13){var t=cr(e),r=$t(e,t);if(r!==null){var n=He();St(r,e,t,n)}ns(e,t)}};Xd=function(){return oe};qd=function(e,t){var r=oe;try{return oe=e,t()}finally{oe=r}};Sa=function(e,t,r){switch(t){case"input":if(ma(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var n=r[t];if(n!==e&&n.form===e.form){var l=ci(n);if(!l)throw Error(_(90));Rd(n),ma(n,l)}}}break;case"textarea":bd(e,r);break;case"select":t=r.value,t!=null&&dn(e,!!r.multiple,t,!1)}};zd=qu;Od=Or;var Om={usingClientEntryPoint:!1,Events:[Al,rn,ci,Md,Td,qu]},Un={findFiberByHostInstance:Rr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},$m={bundleType:Un.bundleType,version:Un.version,rendererPackageName:Un.rendererPackageName,rendererConfig:Un.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Ut.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Ud(e),e===null?null:e.stateNode},findFiberByHostInstance:Un.findFiberByHostInstance||Tm,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var lo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!lo.isDisabled&&lo.supportsFiber)try{ii=lo.inject($m),Rt=lo}catch{}}ot.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Om;ot.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!os(t))throw Error(_(200));return Mm(e,t,null,r)};ot.createRoot=function(e,t){if(!os(e))throw Error(_(299));var r=!1,n="",l=kh;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=rs(e,1,!1,null,null,r,!1,n,l),e[Ot]=t.current,ml(e.nodeType===8?e.parentNode:e),new ls(t)};ot.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=Ud(t),e=e===null?null:e.stateNode,e};ot.flushSync=function(e){return Or(e)};ot.hydrate=function(e,t,r){if(!xi(t))throw Error(_(200));return wi(null,e,t,!0,r)};ot.hydrateRoot=function(e,t,r){if(!os(e))throw Error(_(405));var n=r!=null&&r.hydratedSources||null,l=!1,o="",i=kh;if(r!=null&&(r.unstable_strictMode===!0&&(l=!0),r.identifierPrefix!==void 0&&(o=r.identifierPrefix),r.onRecoverableError!==void 0&&(i=r.onRecoverableError)),t=wh(t,null,e,1,r!=null?r:null,l,!1,o,i),e[Ot]=t.current,ml(e),n)for(e=0;e<n.length;e++)r=n[e],l=r._getVersion,l=l(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,l]:t.mutableSourceEagerHydrationData.push(r,l);return new vi(t)};ot.render=function(e,t,r){if(!xi(t))throw Error(_(200));return wi(null,e,t,!1,r)};ot.unmountComponentAtNode=function(e){if(!xi(e))throw Error(_(40));return e._reactRootContainer?(Or(function(){wi(null,null,e,!1,function(){e._reactRootContainer=null,e[Ot]=null})}),!0):!1};ot.unstable_batchedUpdates=qu;ot.unstable_renderSubtreeIntoContainer=function(e,t,r,n){if(!xi(r))throw Error(_(200));if(e==null||e._reactInternals===void 0)throw Error(_(38));return wi(e,t,r,!1,n)};ot.version="18.3.1-next-f1338f8080-20240426";(function(e){function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(r){console.error(r)}}t(),e.exports=ot})(mu);var Sh,Tc=mu.exports;Sh=Tc.createRoot,Tc.hydrateRoot;const jm="modulepreload",Um=function(e){return"/"+e},zc={},Vm=function(t,r,n){if(!r||r.length===0)return t();const l=document.getElementsByTagName("link");return Promise.all(r.map(o=>{if(o=Um(o),o in zc)return;zc[o]=!0;const i=o.endsWith(".css"),u=i?'[rel="stylesheet"]':"";if(!!n)for(let d=l.length-1;d>=0;d--){const y=l[d];if(y.href===o&&(!i||y.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${u}`))return;const s=document.createElement("link");if(s.rel=i?"stylesheet":jm,i||(s.as="script",s.crossOrigin=""),s.href=o,document.head.appendChild(s),i)return new Promise((d,y)=>{s.addEventListener("load",d),s.addEventListener("error",()=>y(new Error(`Unable to preload CSS for ${o}`)))})})).then(()=>t())};var is={};Object.defineProperty(is,"__esModule",{value:!0});is.parse=Jm;is.serialize=Xm;const Hm=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,Wm=/^[\u0021-\u003A\u003C-\u007E]*$/,Qm=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,Km=/^[\u0020-\u003A\u003D-\u007E]*$/,Ym=Object.prototype.toString,Gm=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function Jm(e,t){const r=new Gm,n=e.length;if(n<2)return r;const l=(t==null?void 0:t.decode)||qm;let o=0;do{const i=e.indexOf("=",o);if(i===-1)break;const u=e.indexOf(";",o),a=u===-1?n:u;if(i>a){o=e.lastIndexOf(";",i-1)+1;continue}const s=Oc(e,o,i),d=$c(e,i,s),y=e.slice(s,d);if(r[y]===void 0){let p=Oc(e,i+1,a),x=$c(e,a,p);const w=l(e.slice(p,x));r[y]=w}o=a+1}while(o<n);return r}function Oc(e,t,r){do{const n=e.charCodeAt(t);if(n!==32&&n!==9)return t}while(++t<r);return r}function $c(e,t,r){for(;t>r;){const n=e.charCodeAt(--t);if(n!==32&&n!==9)return t+1}return r}function Xm(e,t,r){const n=(r==null?void 0:r.encode)||encodeURIComponent;if(!Hm.test(e))throw new TypeError(`argument name is invalid: ${e}`);const l=n(t);if(!Wm.test(l))throw new TypeError(`argument val is invalid: ${t}`);let o=e+"="+l;if(!r)return o;if(r.maxAge!==void 0){if(!Number.isInteger(r.maxAge))throw new TypeError(`option maxAge is invalid: ${r.maxAge}`);o+="; Max-Age="+r.maxAge}if(r.domain){if(!Qm.test(r.domain))throw new TypeError(`option domain is invalid: ${r.domain}`);o+="; Domain="+r.domain}if(r.path){if(!Km.test(r.path))throw new TypeError(`option path is invalid: ${r.path}`);o+="; Path="+r.path}if(r.expires){if(!Zm(r.expires)||!Number.isFinite(r.expires.valueOf()))throw new TypeError(`option expires is invalid: ${r.expires}`);o+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(o+="; HttpOnly"),r.secure&&(o+="; Secure"),r.partitioned&&(o+="; Partitioned"),r.priority)switch(typeof r.priority=="string"?r.priority.toLowerCase():void 0){case"low":o+="; Priority=Low";break;case"medium":o+="; Priority=Medium";break;case"high":o+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch(typeof r.sameSite=="string"?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":o+="; SameSite=Strict";break;case"lax":o+="; SameSite=Lax";break;case"none":o+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${r.sameSite}`)}return o}function qm(e){if(e.indexOf("%")===-1)return e;try{return decodeURIComponent(e)}catch{return e}}function Zm(e){return Ym.call(e)==="[object Date]"}var ki={exports:{}},Si={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ey=C.exports,ty=Symbol.for("react.element"),ry=Symbol.for("react.fragment"),ny=Object.prototype.hasOwnProperty,ly=ey.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,oy={key:!0,ref:!0,__self:!0,__source:!0};function Eh(e,t,r){var n,l={},o=null,i=null;r!==void 0&&(o=""+r),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(n in t)ny.call(t,n)&&!oy.hasOwnProperty(n)&&(l[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)l[n]===void 0&&(l[n]=t[n]);return{$$typeof:ty,type:e,key:o,ref:i,props:l,_owner:ly.current}}Si.Fragment=ry;Si.jsx=Eh;Si.jsxs=Eh;(function(e){e.exports=Si})(ki);const as=ki.exports.Fragment,c=ki.exports.jsx,E=ki.exports.jsxs;var Ch=e=>{throw TypeError(e)},iy=(e,t,r)=>t.has(e)||Ch("Cannot "+r),na=(e,t,r)=>(iy(e,t,"read from private field"),r?r.call(e):t.get(e)),ay=(e,t,r)=>t.has(e)?Ch("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),jc="popstate";function uy(e={}){function t(n,l){let{pathname:o,search:i,hash:u}=n.location;return Cl("",{pathname:o,search:i,hash:u},l.state&&l.state.usr||null,l.state&&l.state.key||"default")}function r(n,l){return typeof l=="string"?l:pr(l)}return cy(t,r,null,e)}function X(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function xe(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function sy(){return Math.random().toString(36).substring(2,10)}function Uc(e,t){return{usr:e.state,key:e.key,idx:t}}function Cl(e,t,r=null,n){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Vt(t):t,state:r,key:t&&t.key||n||sy()}}function pr({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Vt(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substring(n),e=e.substring(0,n)),e&&(t.pathname=e)}return t}function cy(e,t,r,n={}){let{window:l=document.defaultView,v5Compat:o=!1}=n,i=l.history,u="POP",a=null,s=d();s==null&&(s=0,i.replaceState({...i.state,idx:s},""));function d(){return(i.state||{idx:null}).idx}function y(){u="POP";let A=d(),m=A==null?null:A-s;s=A,a&&a({action:u,location:N.location,delta:m})}function p(A,m){u="PUSH";let h=Cl(N.location,A,m);r&&r(h,A),s=d()+1;let f=Uc(h,s),k=N.createHref(h);try{i.pushState(f,"",k)}catch(B){if(B instanceof DOMException&&B.name==="DataCloneError")throw B;l.location.assign(k)}o&&a&&a({action:u,location:N.location,delta:1})}function x(A,m){u="REPLACE";let h=Cl(N.location,A,m);r&&r(h,A),s=d();let f=Uc(h,s),k=N.createHref(h);i.replaceState(f,"",k),o&&a&&a({action:u,location:N.location,delta:0})}function w(A){return Nh(A)}let N={get action(){return u},get location(){return e(l,i)},listen(A){if(a)throw new Error("A history only accepts one active listener");return l.addEventListener(jc,y),a=A,()=>{l.removeEventListener(jc,y),a=null}},createHref(A){return t(l,A)},createURL:w,encodeLocation(A){let m=w(A);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:p,replace:x,go(A){return i.go(A)}};return N}function Nh(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),X(r,"No window.location.(origin|href) available to create URL");let n=typeof e=="string"?e:pr(e);return n=n.replace(/ $/,"%20"),!t&&n.startsWith("//")&&(n=r+n),new URL(n,r)}var Jn,Vc=class{constructor(e){if(ay(this,Jn,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(na(this,Jn).has(e))return na(this,Jn).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,t){na(this,Jn).set(e,t)}};Jn=new WeakMap;var dy=new Set(["lazy","caseSensitive","path","id","index","children"]);function fy(e){return dy.has(e)}var hy=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function py(e){return hy.has(e)}function my(e){return e.index===!0}function Jo(e,t,r=[],n={}){return e.map((l,o)=>{let i=[...r,String(o)],u=typeof l.id=="string"?l.id:i.join("-");if(X(l.index!==!0||!l.children,"Cannot specify children on an index route"),X(!n[u],`Found a route id collision on id "${u}".  Route id's must be globally unique within Data Router usages`),my(l)){let a={...l,...t(l),id:u};return n[u]=a,a}else{let a={...l,...t(l),id:u,children:void 0};return n[u]=a,l.children&&(a.children=Jo(l.children,t,i,n)),a}})}function tr(e,t,r="/"){return ko(e,t,r,!1)}function ko(e,t,r,n){let l=typeof t=="string"?Vt(t):t,o=yt(l.pathname||"/",r);if(o==null)return null;let i=Dh(e);gy(i);let u=null;for(let a=0;u==null&&a<i.length;++a){let s=By(o);u=Dy(i[a],s,n)}return u}function yy(e,t){let{route:r,pathname:n,params:l}=e;return{id:r.id,pathname:n,params:l,data:t[r.id],handle:r.handle}}function Dh(e,t=[],r=[],n=""){let l=(o,i,u)=>{let a={relativePath:u===void 0?o.path||"":u,caseSensitive:o.caseSensitive===!0,childrenIndex:i,route:o};a.relativePath.startsWith("/")&&(X(a.relativePath.startsWith(n),`Absolute route path "${a.relativePath}" nested under path "${n}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),a.relativePath=a.relativePath.slice(n.length));let s=bt([n,a.relativePath]),d=r.concat(a);o.children&&o.children.length>0&&(X(o.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${s}".`),Dh(o.children,t,d,s)),!(o.path==null&&!o.index)&&t.push({path:s,score:Cy(s,o.index),routesMeta:d})};return e.forEach((o,i)=>{var u;if(o.path===""||!((u=o.path)!=null&&u.includes("?")))l(o,i);else for(let a of Fh(o.path))l(o,i,a)}),t}function Fh(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,l=r.endsWith("?"),o=r.replace(/\?$/,"");if(n.length===0)return l?[o,""]:[o];let i=Fh(n.join("/")),u=[];return u.push(...i.map(a=>a===""?o:[o,a].join("/"))),l&&u.push(...i),u.map(a=>e.startsWith("/")&&a===""?"/":a)}function gy(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Ny(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}var vy=/^:[\w-]+$/,xy=3,wy=2,ky=1,Sy=10,Ey=-2,Hc=e=>e==="*";function Cy(e,t){let r=e.split("/"),n=r.length;return r.some(Hc)&&(n+=Ey),t&&(n+=wy),r.filter(l=>!Hc(l)).reduce((l,o)=>l+(vy.test(o)?xy:o===""?ky:Sy),n)}function Ny(e,t){return e.length===t.length&&e.slice(0,-1).every((n,l)=>n===t[l])?e[e.length-1]-t[t.length-1]:0}function Dy(e,t,r=!1){let{routesMeta:n}=e,l={},o="/",i=[];for(let u=0;u<n.length;++u){let a=n[u],s=u===n.length-1,d=o==="/"?t:t.slice(o.length)||"/",y=Xo({path:a.relativePath,caseSensitive:a.caseSensitive,end:s},d),p=a.route;if(!y&&s&&r&&!n[n.length-1].route.index&&(y=Xo({path:a.relativePath,caseSensitive:a.caseSensitive,end:!1},d)),!y)return null;Object.assign(l,y.params),i.push({params:l,pathname:bt([o,y.pathname]),pathnameBase:by(bt([o,y.pathnameBase])),route:p}),y.pathnameBase!=="/"&&(o=bt([o,y.pathnameBase]))}return i}function Xo(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=Fy(e.path,e.caseSensitive,e.end),l=t.match(r);if(!l)return null;let o=l[0],i=o.replace(/(.)\/+$/,"$1"),u=l.slice(1);return{params:n.reduce((s,{paramName:d,isOptional:y},p)=>{if(d==="*"){let w=u[p]||"";i=o.slice(0,o.length-w.length).replace(/(.)\/+$/,"$1")}const x=u[p];return y&&!x?s[d]=void 0:s[d]=(x||"").replace(/%2F/g,"/"),s},{}),pathname:o,pathnameBase:i,pattern:e}}function Fy(e,t=!1,r=!0){xe(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let n=[],l="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,u,a)=>(n.push({paramName:u,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),l+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?l+="\\/*$":e!==""&&e!=="/"&&(l+="(?:(?=\\/|$))"),[new RegExp(l,t?void 0:"i"),n]}function By(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return xe(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function yt(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function Ry(e,t="/"){let{pathname:r,search:n="",hash:l=""}=typeof e=="string"?Vt(e):e;return{pathname:r?r.startsWith("/")?r:Ay(r,t):t,search:Py(n),hash:_y(l)}}function Ay(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(l=>{l===".."?r.length>1&&r.pop():l!=="."&&r.push(l)}),r.length>1?r.join("/"):"/"}function la(e,t,r,n){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(n)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Bh(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function Ei(e){let t=Bh(e);return t.map((r,n)=>n===t.length-1?r.pathname:r.pathnameBase)}function Ci(e,t,r,n=!1){let l;typeof e=="string"?l=Vt(e):(l={...e},X(!l.pathname||!l.pathname.includes("?"),la("?","pathname","search",l)),X(!l.pathname||!l.pathname.includes("#"),la("#","pathname","hash",l)),X(!l.search||!l.search.includes("#"),la("#","search","hash",l)));let o=e===""||l.pathname==="",i=o?"/":l.pathname,u;if(i==null)u=r;else{let y=t.length-1;if(!n&&i.startsWith("..")){let p=i.split("/");for(;p[0]==="..";)p.shift(),y-=1;l.pathname=p.join("/")}u=y>=0?t[y]:"/"}let a=Ry(l,u),s=i&&i!=="/"&&i.endsWith("/"),d=(o||i===".")&&r.endsWith("/");return!a.pathname.endsWith("/")&&(s||d)&&(a.pathname+="/"),a}var bt=e=>e.join("/").replace(/\/\/+/g,"/"),by=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Py=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,_y=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,qo=class{constructor(e,t,r,n=!1){this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function Nl(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var Rh=["POST","PUT","PATCH","DELETE"],Ly=new Set(Rh),Iy=["GET",...Rh],My=new Set(Iy),Ty=new Set([301,302,303,307,308]),zy=new Set([307,308]),oa={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Oy={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Vn={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},us=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,$y=e=>({hasErrorBoundary:Boolean(e.hasErrorBoundary)}),Ah="remix-router-transitions",bh=Symbol("ResetLoaderData");function jy(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u";X(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let n=e.hydrationRouteProperties||[],l=e.mapRouteProperties||$y,o={},i=Jo(e.routes,l,void 0,o),u,a=e.basename||"/",s=e.dataStrategy||Qy,d={unstable_middleware:!1,...e.future},y=null,p=new Set,x=null,w=null,N=null,A=e.hydrationData!=null,m=tr(i,e.history.location,a),h=!1,f=null,k;if(m==null&&!e.patchRoutesOnNavigation){let v=st(404,{pathname:e.history.location.pathname}),{matches:D,route:F}=rd(i);k=!0,m=D,f={[F.id]:v}}else if(m&&!e.hydrationData&&zl(m,i,e.history.location.pathname).active&&(m=null),m)if(m.some(v=>v.route.lazy))k=!1;else if(!m.some(v=>v.route.loader))k=!0;else{let v=e.hydrationData?e.hydrationData.loaderData:null,D=e.hydrationData?e.hydrationData.errors:null;if(D){let F=m.findIndex(P=>D[P.route.id]!==void 0);k=m.slice(0,F+1).every(P=>!ru(P.route,v,D))}else k=m.every(F=>!ru(F.route,v,D))}else{k=!1,m=[];let v=zl(null,i,e.history.location.pathname);v.active&&v.matches&&(h=!0,m=v.matches)}let B,g={historyAction:e.history.action,location:e.history.location,matches:m,initialized:k,navigation:oa,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||f,fetchers:new Map,blockers:new Map},S="POP",b=!1,z,$=!1,G=new Map,ge=null,we=!1,R=!1,L=new Set,Z=new Map,Ce=0,I=-1,j=new Map,V=new Set,te=new Map,le=new Map,ke=new Set,$e=new Map,Wt,je=null;function Hr(){if(y=e.history.listen(({action:v,location:D,delta:F})=>{if(Wt){Wt(),Wt=void 0;return}xe($e.size===0||F!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let P=Ns({currentLocation:g.location,nextLocation:D,historyAction:v});if(P&&F!=null){let M=new Promise(U=>{Wt=U});e.history.go(F*-1),Tl(P,{state:"blocked",location:D,proceed(){Tl(P,{state:"proceeding",proceed:void 0,reset:void 0,location:D}),M.then(()=>e.history.go(F))},reset(){let U=new Map(g.blockers);U.set(P,Vn),Ue({blockers:U})}});return}return Sr(v,D)}),r){ng(t,G);let v=()=>lg(t,G);t.addEventListener("pagehide",v),ge=()=>t.removeEventListener("pagehide",v)}return g.initialized||Sr("POP",g.location,{initialHydration:!0}),B}function n0(){y&&y(),ge&&ge(),p.clear(),z&&z.abort(),g.fetchers.forEach((v,D)=>Fi(D)),g.blockers.forEach((v,D)=>Cs(D))}function l0(v){return p.add(v),()=>p.delete(v)}function Ue(v,D={}){g={...g,...v};let F=[],P=[];g.fetchers.forEach((M,U)=>{M.state==="idle"&&(ke.has(U)?F.push(U):P.push(U))}),ke.forEach(M=>{!g.fetchers.has(M)&&!Z.has(M)&&F.push(M)}),[...p].forEach(M=>M(g,{deletedFetchers:F,viewTransitionOpts:D.viewTransitionOpts,flushSync:D.flushSync===!0})),F.forEach(M=>Fi(M)),P.forEach(M=>g.fetchers.delete(M))}function Wr(v,D,{flushSync:F}={}){var Q,Y;let P=g.actionData!=null&&g.navigation.formMethod!=null&&tt(g.navigation.formMethod)&&g.navigation.state==="loading"&&((Q=v.state)==null?void 0:Q._isRedirect)!==!0,M;D.actionData?Object.keys(D.actionData).length>0?M=D.actionData:M=null:P?M=g.actionData:M=null;let U=D.loaderData?ed(g.loaderData,D.loaderData,D.matches||[],D.errors):g.loaderData,K=g.blockers;K.size>0&&(K=new Map(K),K.forEach((W,J)=>K.set(J,Vn)));let O=b===!0||g.navigation.formMethod!=null&&tt(g.navigation.formMethod)&&((Y=v.state)==null?void 0:Y._isRedirect)!==!0;u&&(i=u,u=void 0),we||S==="POP"||(S==="PUSH"?e.history.push(v,v.state):S==="REPLACE"&&e.history.replace(v,v.state));let H;if(S==="POP"){let W=G.get(g.location.pathname);W&&W.has(v.pathname)?H={currentLocation:g.location,nextLocation:v}:G.has(v.pathname)&&(H={currentLocation:v,nextLocation:g.location})}else if($){let W=G.get(g.location.pathname);W?W.add(v.pathname):(W=new Set([v.pathname]),G.set(g.location.pathname,W)),H={currentLocation:g.location,nextLocation:v}}Ue({...D,actionData:M,loaderData:U,historyAction:S,location:v,initialized:!0,navigation:oa,revalidation:"idle",restoreScrollPosition:Fs(v,D.matches||g.matches),preventScrollReset:O,blockers:K},{viewTransitionOpts:H,flushSync:F===!0}),S="POP",b=!1,$=!1,we=!1,R=!1,je==null||je.resolve(),je=null}async function gs(v,D){if(typeof v=="number"){e.history.go(v);return}let F=tu(g.location,g.matches,a,v,D==null?void 0:D.fromRouteId,D==null?void 0:D.relative),{path:P,submission:M,error:U}=Wc(!1,F,D),K=g.location,O=Cl(g.location,P,D&&D.state);O={...O,...e.history.encodeLocation(O)};let H=D&&D.replace!=null?D.replace:void 0,Q="PUSH";H===!0?Q="REPLACE":H===!1||M!=null&&tt(M.formMethod)&&M.formAction===g.location.pathname+g.location.search&&(Q="REPLACE");let Y=D&&"preventScrollReset"in D?D.preventScrollReset===!0:void 0,W=(D&&D.flushSync)===!0,J=Ns({currentLocation:K,nextLocation:O,historyAction:Q});if(J){Tl(J,{state:"blocked",location:O,proceed(){Tl(J,{state:"proceeding",proceed:void 0,reset:void 0,location:O}),gs(v,D)},reset(){let ae=new Map(g.blockers);ae.set(J,Vn),Ue({blockers:ae})}});return}await Sr(Q,O,{submission:M,pendingError:U,preventScrollReset:Y,replace:D&&D.replace,enableViewTransition:D&&D.viewTransition,flushSync:W})}function o0(){je||(je=og()),Di(),Ue({revalidation:"loading"});let v=je.promise;return g.navigation.state==="submitting"?v:g.navigation.state==="idle"?(Sr(g.historyAction,g.location,{startUninterruptedRevalidation:!0}),v):(Sr(S||g.historyAction,g.navigation.location,{overrideNavigation:g.navigation,enableViewTransition:$===!0}),v)}async function Sr(v,D,F){z&&z.abort(),z=null,S=v,we=(F&&F.startUninterruptedRevalidation)===!0,m0(g.location,g.matches),b=(F&&F.preventScrollReset)===!0,$=(F&&F.enableViewTransition)===!0;let P=u||i,M=F&&F.overrideNavigation,U=(F==null?void 0:F.initialHydration)&&g.matches&&g.matches.length>0&&!h?g.matches:tr(P,D,a),K=(F&&F.flushSync)===!0;if(U&&g.initialized&&!R&&qy(g.location,D)&&!(F&&F.submission&&tt(F.submission.formMethod))){Wr(D,{matches:U},{flushSync:K});return}let O=zl(U,P,D.pathname);if(O.active&&O.matches&&(U=O.matches),!U){let{error:Le,notFoundMatches:qe,route:ne}=Bi(D.pathname);Wr(D,{matches:qe,loaderData:{},errors:{[ne.id]:Le}},{flushSync:K});return}z=new AbortController;let H=Jr(e.history,D,z.signal,F&&F.submission),Q=new Vc(e.unstable_getContext?await e.unstable_getContext():void 0),Y;if(F&&F.pendingError)Y=[Br(U).route.id,{type:"error",error:F.pendingError}];else if(F&&F.submission&&tt(F.submission.formMethod)){let Le=await i0(H,D,F.submission,U,Q,O.active,F&&F.initialHydration===!0,{replace:F.replace,flushSync:K});if(Le.shortCircuited)return;if(Le.pendingActionResult){let[qe,ne]=Le.pendingActionResult;if(et(ne)&&Nl(ne.error)&&ne.error.status===404){z=null,Wr(D,{matches:Le.matches,loaderData:{},errors:{[qe]:ne.error}});return}}U=Le.matches||U,Y=Le.pendingActionResult,M=ia(D,F.submission),K=!1,O.active=!1,H=Jr(e.history,H.url,H.signal)}let{shortCircuited:W,matches:J,loaderData:ae,errors:Ne}=await a0(H,D,U,Q,O.active,M,F&&F.submission,F&&F.fetcherSubmission,F&&F.replace,F&&F.initialHydration===!0,K,Y);W||(z=null,Wr(D,{matches:J||U,...td(Y),loaderData:ae,errors:Ne}))}async function i0(v,D,F,P,M,U,K,O={}){Di();let H=tg(D,F);if(Ue({navigation:H},{flushSync:O.flushSync===!0}),U){let W=await Ol(P,D.pathname,v.signal);if(W.type==="aborted")return{shortCircuited:!0};if(W.type==="error"){let J=Br(W.partialMatches).route.id;return{matches:W.partialMatches,pendingActionResult:[J,{type:"error",error:W.error}]}}else if(W.matches)P=W.matches;else{let{notFoundMatches:J,error:ae,route:Ne}=Bi(D.pathname);return{matches:J,pendingActionResult:[Ne.id,{type:"error",error:ae}]}}}let Q,Y=Xn(P,D);if(!Y.route.action&&!Y.route.lazy)Q={type:"error",error:st(405,{method:v.method,pathname:D.pathname,routeId:Y.route.id})};else{let W=gn(l,o,v,P,Y,K?[]:n,M),J=await An(v,W,M,null);if(Q=J[Y.route.id],!Q){for(let ae of P)if(J[ae.route.id]){Q=J[ae.route.id];break}}if(v.signal.aborted)return{shortCircuited:!0}}if(Pr(Q)){let W;return O&&O.replace!=null?W=O.replace:W=Xc(Q.response.headers.get("Location"),new URL(v.url),a)===g.location.pathname+g.location.search,await Er(v,Q,!0,{submission:F,replace:W}),{shortCircuited:!0}}if(et(Q)){let W=Br(P,Y.route.id);return(O&&O.replace)!==!0&&(S="PUSH"),{matches:P,pendingActionResult:[W.route.id,Q,Y.route.id]}}return{matches:P,pendingActionResult:[Y.route.id,Q]}}async function a0(v,D,F,P,M,U,K,O,H,Q,Y,W){let J=U||ia(D,K),ae=K||O||ld(J),Ne=!we&&!Q;if(M){if(Ne){let ut=vs(W);Ue({navigation:J,...ut!==void 0?{actionData:ut}:{}},{flushSync:Y})}let ie=await Ol(F,D.pathname,v.signal);if(ie.type==="aborted")return{shortCircuited:!0};if(ie.type==="error"){let ut=Br(ie.partialMatches).route.id;return{matches:ie.partialMatches,loaderData:{},errors:{[ut]:ie.error}}}else if(ie.matches)F=ie.matches;else{let{error:ut,notFoundMatches:Yt,route:jl}=Bi(D.pathname);return{matches:Yt,loaderData:{},errors:{[jl.id]:ut}}}}let Le=u||i,{dsMatches:qe,revalidatingFetchers:ne}=Qc(v,P,l,o,e.history,g,F,ae,D,Q?[]:n,Q===!0,R,L,ke,te,V,Le,a,e.patchRoutesOnNavigation!=null,W);if(I=++Ce,!e.dataStrategy&&!qe.some(ie=>ie.shouldLoad)&&ne.length===0){let ie=Ss();return Wr(D,{matches:F,loaderData:{},errors:W&&et(W[1])?{[W[0]]:W[1].error}:null,...td(W),...ie?{fetchers:new Map(g.fetchers)}:{}},{flushSync:Y}),{shortCircuited:!0}}if(Ne){let ie={};if(!M){ie.navigation=J;let ut=vs(W);ut!==void 0&&(ie.actionData=ut)}ne.length>0&&(ie.fetchers=u0(ne)),Ue(ie,{flushSync:Y})}ne.forEach(ie=>{Kt(ie.key),ie.controller&&Z.set(ie.key,ie.controller)});let bn=()=>ne.forEach(ie=>Kt(ie.key));z&&z.signal.addEventListener("abort",bn);let{loaderResults:Cr,fetcherResults:Pn}=await xs(qe,ne,v,P);if(v.signal.aborted)return{shortCircuited:!0};z&&z.signal.removeEventListener("abort",bn),ne.forEach(ie=>Z.delete(ie.key));let at=oo(Cr);if(at)return await Er(v,at.result,!0,{replace:H}),{shortCircuited:!0};if(at=oo(Pn),at)return V.add(at.key),await Er(v,at.result,!0,{replace:H}),{shortCircuited:!0};let{loaderData:_n,errors:Ln}=Zc(g,F,Cr,W,ne,Pn);Q&&g.errors&&(Ln={...g.errors,...Ln});let Ri=Ss(),Nr=Es(I),$l=Ri||Nr||ne.length>0;return{matches:F,loaderData:_n,errors:Ln,...$l?{fetchers:new Map(g.fetchers)}:{}}}function vs(v){if(v&&!et(v[1]))return{[v[0]]:v[1].data};if(g.actionData)return Object.keys(g.actionData).length===0?null:g.actionData}function u0(v){return v.forEach(D=>{let F=g.fetchers.get(D.key),P=Hn(void 0,F?F.data:void 0);g.fetchers.set(D.key,P)}),new Map(g.fetchers)}async function s0(v,D,F,P){Kt(v);let M=(P&&P.flushSync)===!0,U=u||i,K=tu(g.location,g.matches,a,F,D,P==null?void 0:P.relative),O=tr(U,K,a),H=zl(O,U,K);if(H.active&&H.matches&&(O=H.matches),!O){Pt(v,D,st(404,{pathname:K}),{flushSync:M});return}let{path:Q,submission:Y,error:W}=Wc(!0,K,P);if(W){Pt(v,D,W,{flushSync:M});return}let J=Xn(O,Q),ae=new Vc(e.unstable_getContext?await e.unstable_getContext():void 0),Ne=(P&&P.preventScrollReset)===!0;if(Y&&tt(Y.formMethod)){await c0(v,D,Q,J,O,ae,H.active,M,Ne,Y);return}te.set(v,{routeId:D,path:Q}),await d0(v,D,Q,J,O,ae,H.active,M,Ne,Y)}async function c0(v,D,F,P,M,U,K,O,H,Q){Di(),te.delete(v);function Y(Se){if(!Se.route.action&&!Se.route.lazy){let Qr=st(405,{method:Q.formMethod,pathname:F,routeId:D});return Pt(v,D,Qr,{flushSync:O}),!0}return!1}if(!K&&Y(P))return;let W=g.fetchers.get(v);Qt(v,rg(Q,W),{flushSync:O});let J=new AbortController,ae=Jr(e.history,F,J.signal,Q);if(K){let Se=await Ol(M,F,ae.signal,v);if(Se.type==="aborted")return;if(Se.type==="error"){Pt(v,D,Se.error,{flushSync:O});return}else if(Se.matches){if(M=Se.matches,P=Xn(M,F),Y(P))return}else{Pt(v,D,st(404,{pathname:F}),{flushSync:O});return}}Z.set(v,J);let Ne=Ce,Le=gn(l,o,ae,M,P,n,U),ne=(await An(ae,Le,U,v))[P.route.id];if(ae.signal.aborted){Z.get(v)===J&&Z.delete(v);return}if(ke.has(v)){if(Pr(ne)||et(ne)){Qt(v,Jt(void 0));return}}else{if(Pr(ne))if(Z.delete(v),I>Ne){Qt(v,Jt(void 0));return}else return V.add(v),Qt(v,Hn(Q)),Er(ae,ne,!1,{fetcherSubmission:Q,preventScrollReset:H});if(et(ne)){Pt(v,D,ne.error);return}}let bn=g.navigation.location||g.location,Cr=Jr(e.history,bn,J.signal),Pn=u||i,at=g.navigation.state!=="idle"?tr(Pn,g.navigation.location,a):g.matches;X(at,"Didn't find any matches after fetcher action");let _n=++Ce;j.set(v,_n);let Ln=Hn(Q,ne.data);g.fetchers.set(v,Ln);let{dsMatches:Ri,revalidatingFetchers:Nr}=Qc(Cr,U,l,o,e.history,g,at,Q,bn,n,!1,R,L,ke,te,V,Pn,a,e.patchRoutesOnNavigation!=null,[P.route.id,ne]);Nr.filter(Se=>Se.key!==v).forEach(Se=>{let Qr=Se.key,Bs=g.fetchers.get(Qr),v0=Hn(void 0,Bs?Bs.data:void 0);g.fetchers.set(Qr,v0),Kt(Qr),Se.controller&&Z.set(Qr,Se.controller)}),Ue({fetchers:new Map(g.fetchers)});let $l=()=>Nr.forEach(Se=>Kt(Se.key));J.signal.addEventListener("abort",$l);let{loaderResults:ie,fetcherResults:ut}=await xs(Ri,Nr,Cr,U);if(J.signal.aborted)return;if(J.signal.removeEventListener("abort",$l),j.delete(v),Z.delete(v),Nr.forEach(Se=>Z.delete(Se.key)),g.fetchers.has(v)){let Se=Jt(ne.data);g.fetchers.set(v,Se)}let Yt=oo(ie);if(Yt)return Er(Cr,Yt.result,!1,{preventScrollReset:H});if(Yt=oo(ut),Yt)return V.add(Yt.key),Er(Cr,Yt.result,!1,{preventScrollReset:H});let{loaderData:jl,errors:Ai}=Zc(g,at,ie,void 0,Nr,ut);Es(_n),g.navigation.state==="loading"&&_n>I?(X(S,"Expected pending action"),z&&z.abort(),Wr(g.navigation.location,{matches:at,loaderData:jl,errors:Ai,fetchers:new Map(g.fetchers)})):(Ue({errors:Ai,loaderData:ed(g.loaderData,jl,at,Ai),fetchers:new Map(g.fetchers)}),R=!1)}async function d0(v,D,F,P,M,U,K,O,H,Q){let Y=g.fetchers.get(v);Qt(v,Hn(Q,Y?Y.data:void 0),{flushSync:O});let W=new AbortController,J=Jr(e.history,F,W.signal);if(K){let ne=await Ol(M,F,J.signal,v);if(ne.type==="aborted")return;if(ne.type==="error"){Pt(v,D,ne.error,{flushSync:O});return}else if(ne.matches)M=ne.matches,P=Xn(M,F);else{Pt(v,D,st(404,{pathname:F}),{flushSync:O});return}}Z.set(v,W);let ae=Ce,Ne=gn(l,o,J,M,P,n,U),qe=(await An(J,Ne,U,v))[P.route.id];if(Z.get(v)===W&&Z.delete(v),!J.signal.aborted){if(ke.has(v)){Qt(v,Jt(void 0));return}if(Pr(qe))if(I>ae){Qt(v,Jt(void 0));return}else{V.add(v),await Er(J,qe,!1,{preventScrollReset:H});return}if(et(qe)){Pt(v,D,qe.error);return}Qt(v,Jt(qe.data))}}async function Er(v,D,F,{submission:P,fetcherSubmission:M,preventScrollReset:U,replace:K}={}){D.response.headers.has("X-Remix-Revalidate")&&(R=!0);let O=D.response.headers.get("Location");X(O,"Expected a Location header on the redirect Response"),O=Xc(O,new URL(v.url),a);let H=Cl(g.location,O,{_isRedirect:!0});if(r){let Ne=!1;if(D.response.headers.has("X-Remix-Reload-Document"))Ne=!0;else if(us.test(O)){const Le=Nh(O,!0);Ne=Le.origin!==t.location.origin||yt(Le.pathname,a)==null}if(Ne){K?t.location.replace(O):t.location.assign(O);return}}z=null;let Q=K===!0||D.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:Y,formAction:W,formEncType:J}=g.navigation;!P&&!M&&Y&&W&&J&&(P=ld(g.navigation));let ae=P||M;if(zy.has(D.response.status)&&ae&&tt(ae.formMethod))await Sr(Q,H,{submission:{...ae,formAction:O},preventScrollReset:U||b,enableViewTransition:F?$:void 0});else{let Ne=ia(H,P);await Sr(Q,H,{overrideNavigation:Ne,fetcherSubmission:M,preventScrollReset:U||b,enableViewTransition:F?$:void 0})}}async function An(v,D,F,P){let M,U={};try{M=await Ky(s,v,D,P,F,!1)}catch(K){return D.filter(O=>O.shouldLoad).forEach(O=>{U[O.route.id]={type:"error",error:K}}),U}if(v.signal.aborted)return U;for(let[K,O]of Object.entries(M))if(Zy(O)){let H=O.result;U[K]={type:"redirect",response:Jy(H,v,K,D,a)}}else U[K]=await Gy(O);return U}async function xs(v,D,F,P){let M=An(F,v,P,null),U=Promise.all(D.map(async H=>{if(H.matches&&H.match&&H.request&&H.controller){let Y=(await An(H.request,H.matches,P,H.key))[H.match.route.id];return{[H.key]:Y}}else return Promise.resolve({[H.key]:{type:"error",error:st(404,{pathname:H.path})}})})),K=await M,O=(await U).reduce((H,Q)=>Object.assign(H,Q),{});return{loaderResults:K,fetcherResults:O}}function Di(){R=!0,te.forEach((v,D)=>{Z.has(D)&&L.add(D),Kt(D)})}function Qt(v,D,F={}){g.fetchers.set(v,D),Ue({fetchers:new Map(g.fetchers)},{flushSync:(F&&F.flushSync)===!0})}function Pt(v,D,F,P={}){let M=Br(g.matches,D);Fi(v),Ue({errors:{[M.route.id]:F},fetchers:new Map(g.fetchers)},{flushSync:(P&&P.flushSync)===!0})}function ws(v){return le.set(v,(le.get(v)||0)+1),ke.has(v)&&ke.delete(v),g.fetchers.get(v)||Oy}function Fi(v){let D=g.fetchers.get(v);Z.has(v)&&!(D&&D.state==="loading"&&j.has(v))&&Kt(v),te.delete(v),j.delete(v),V.delete(v),ke.delete(v),L.delete(v),g.fetchers.delete(v)}function f0(v){let D=(le.get(v)||0)-1;D<=0?(le.delete(v),ke.add(v)):le.set(v,D),Ue({fetchers:new Map(g.fetchers)})}function Kt(v){let D=Z.get(v);D&&(D.abort(),Z.delete(v))}function ks(v){for(let D of v){let F=ws(D),P=Jt(F.data);g.fetchers.set(D,P)}}function Ss(){let v=[],D=!1;for(let F of V){let P=g.fetchers.get(F);X(P,`Expected fetcher: ${F}`),P.state==="loading"&&(V.delete(F),v.push(F),D=!0)}return ks(v),D}function Es(v){let D=[];for(let[F,P]of j)if(P<v){let M=g.fetchers.get(F);X(M,`Expected fetcher: ${F}`),M.state==="loading"&&(Kt(F),j.delete(F),D.push(F))}return ks(D),D.length>0}function h0(v,D){let F=g.blockers.get(v)||Vn;return $e.get(v)!==D&&$e.set(v,D),F}function Cs(v){g.blockers.delete(v),$e.delete(v)}function Tl(v,D){let F=g.blockers.get(v)||Vn;X(F.state==="unblocked"&&D.state==="blocked"||F.state==="blocked"&&D.state==="blocked"||F.state==="blocked"&&D.state==="proceeding"||F.state==="blocked"&&D.state==="unblocked"||F.state==="proceeding"&&D.state==="unblocked",`Invalid blocker state transition: ${F.state} -> ${D.state}`);let P=new Map(g.blockers);P.set(v,D),Ue({blockers:P})}function Ns({currentLocation:v,nextLocation:D,historyAction:F}){if($e.size===0)return;$e.size>1&&xe(!1,"A router only supports one blocker at a time");let P=Array.from($e.entries()),[M,U]=P[P.length-1],K=g.blockers.get(M);if(!(K&&K.state==="proceeding")&&U({currentLocation:v,nextLocation:D,historyAction:F}))return M}function Bi(v){let D=st(404,{pathname:v}),F=u||i,{matches:P,route:M}=rd(F);return{notFoundMatches:P,route:M,error:D}}function p0(v,D,F){if(x=v,N=D,w=F||null,!A&&g.navigation===oa){A=!0;let P=Fs(g.location,g.matches);P!=null&&Ue({restoreScrollPosition:P})}return()=>{x=null,N=null,w=null}}function Ds(v,D){return w&&w(v,D.map(P=>yy(P,g.loaderData)))||v.key}function m0(v,D){if(x&&N){let F=Ds(v,D);x[F]=N()}}function Fs(v,D){if(x){let F=Ds(v,D),P=x[F];if(typeof P=="number")return P}return null}function zl(v,D,F){if(e.patchRoutesOnNavigation)if(v){if(Object.keys(v[0].params).length>0)return{active:!0,matches:ko(D,F,a,!0)}}else return{active:!0,matches:ko(D,F,a,!0)||[]};return{active:!1,matches:null}}async function Ol(v,D,F,P){if(!e.patchRoutesOnNavigation)return{type:"success",matches:v};let M=v;for(;;){let U=u==null,K=u||i,O=o;try{await e.patchRoutesOnNavigation({signal:F,path:D,matches:M,fetcherKey:P,patch:(Y,W)=>{F.aborted||Kc(Y,W,K,O,l)}})}catch(Y){return{type:"error",error:Y,partialMatches:M}}finally{U&&!F.aborted&&(i=[...i])}if(F.aborted)return{type:"aborted"};let H=tr(K,D,a);if(H)return{type:"success",matches:H};let Q=ko(K,D,a,!0);if(!Q||M.length===Q.length&&M.every((Y,W)=>Y.route.id===Q[W].route.id))return{type:"success",matches:null};M=Q}}function y0(v){o={},u=Jo(v,l,void 0,o)}function g0(v,D){let F=u==null;Kc(v,D,u||i,o,l),F&&(i=[...i],Ue({}))}return B={get basename(){return a},get future(){return d},get state(){return g},get routes(){return i},get window(){return t},initialize:Hr,subscribe:l0,enableScrollRestoration:p0,navigate:gs,fetch:s0,revalidate:o0,createHref:v=>e.history.createHref(v),encodeLocation:v=>e.history.encodeLocation(v),getFetcher:ws,deleteFetcher:f0,dispose:n0,getBlocker:h0,deleteBlocker:Cs,patchRoutes:g0,_internalFetchControllers:Z,_internalSetRoutes:y0},B}function Uy(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function tu(e,t,r,n,l,o){let i,u;if(l){i=[];for(let s of t)if(i.push(s),s.route.id===l){u=s;break}}else i=t,u=t[t.length-1];let a=Ci(n||".",Ei(i),yt(e.pathname,r)||e.pathname,o==="path");if(n==null&&(a.search=e.search,a.hash=e.hash),(n==null||n===""||n===".")&&u){let s=ss(a.search);if(u.route.index&&!s)a.search=a.search?a.search.replace(/^\?/,"?index&"):"?index";else if(!u.route.index&&s){let d=new URLSearchParams(a.search),y=d.getAll("index");d.delete("index"),y.filter(x=>x).forEach(x=>d.append("index",x));let p=d.toString();a.search=p?`?${p}`:""}}return r!=="/"&&(a.pathname=a.pathname==="/"?r:bt([r,a.pathname])),pr(a)}function Wc(e,t,r){if(!r||!Uy(r))return{path:t};if(r.formMethod&&!eg(r.formMethod))return{path:t,error:st(405,{method:r.formMethod})};let n=()=>({path:t,error:st(400,{type:"invalid-body"})}),o=(r.formMethod||"get").toUpperCase(),i=Th(t);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!tt(o))return n();let y=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((p,[x,w])=>`${p}${x}=${w}
`,""):String(r.body);return{path:t,submission:{formMethod:o,formAction:i,formEncType:r.formEncType,formData:void 0,json:void 0,text:y}}}else if(r.formEncType==="application/json"){if(!tt(o))return n();try{let y=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:o,formAction:i,formEncType:r.formEncType,formData:void 0,json:y,text:void 0}}}catch{return n()}}}X(typeof FormData=="function","FormData is not available in this environment");let u,a;if(r.formData)u=lu(r.formData),a=r.formData;else if(r.body instanceof FormData)u=lu(r.body),a=r.body;else if(r.body instanceof URLSearchParams)u=r.body,a=qc(u);else if(r.body==null)u=new URLSearchParams,a=new FormData;else try{u=new URLSearchParams(r.body),a=qc(u)}catch{return n()}let s={formMethod:o,formAction:i,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:a,json:void 0,text:void 0};if(tt(s.formMethod))return{path:t,submission:s};let d=Vt(t);return e&&d.search&&ss(d.search)&&u.append("index",""),d.search=`?${u}`,{path:pr(d),submission:s}}function Qc(e,t,r,n,l,o,i,u,a,s,d,y,p,x,w,N,A,m,h,f){var we;let k=f?et(f[1])?f[1].error:f[1].data:void 0,B=l.createURL(o.location),g=l.createURL(a),S;if(d&&o.errors){let R=Object.keys(o.errors)[0];S=i.findIndex(L=>L.route.id===R)}else if(f&&et(f[1])){let R=f[0];S=i.findIndex(L=>L.route.id===R)-1}let b=f?f[1].statusCode:void 0,z=b&&b>=400,$={currentUrl:B,currentParams:((we=o.matches[0])==null?void 0:we.params)||{},nextUrl:g,nextParams:i[0].params,...u,actionResult:k,actionStatus:b},G=i.map((R,L)=>{let{route:Z}=R,Ce=null;if(S!=null&&L>S?Ce=!1:Z.lazy?Ce=!0:Z.loader==null?Ce=!1:d?Ce=ru(Z,o.loaderData,o.errors):Vy(o.loaderData,o.matches[L],R)&&(Ce=!0),Ce!==null)return nu(r,n,e,R,s,t,Ce);let I=z?!1:y||B.pathname+B.search===g.pathname+g.search||B.search!==g.search||Hy(o.matches[L],R),j={...$,defaultShouldRevalidate:I},V=Zo(R,j);return nu(r,n,e,R,s,t,V,j)}),ge=[];return w.forEach((R,L)=>{if(d||!i.some(ke=>ke.route.id===R.routeId)||x.has(L))return;let Z=o.fetchers.get(L),Ce=Z&&Z.state!=="idle"&&Z.data===void 0,I=tr(A,R.path,m);if(!I){if(h&&Ce)return;ge.push({key:L,routeId:R.routeId,path:R.path,matches:null,match:null,request:null,controller:null});return}if(N.has(L))return;let j=Xn(I,R.path),V=new AbortController,te=Jr(l,R.path,V.signal),le=null;if(p.has(L))p.delete(L),le=gn(r,n,te,I,j,s,t);else if(Ce)y&&(le=gn(r,n,te,I,j,s,t));else{let ke={...$,defaultShouldRevalidate:z?!1:y};Zo(j,ke)&&(le=gn(r,n,te,I,j,s,t,ke))}le&&ge.push({key:L,routeId:R.routeId,path:R.path,matches:le,match:j,request:te,controller:V})}),{dsMatches:G,revalidatingFetchers:ge}}function ru(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=t!=null&&e.id in t,l=r!=null&&r[e.id]!==void 0;return!n&&l?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!n&&!l}function Vy(e,t,r){let n=!t||r.route.id!==t.route.id,l=!e.hasOwnProperty(r.route.id);return n||l}function Hy(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function Zo(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function Kc(e,t,r,n,l){let o;if(e){let a=n[e];X(a,`No route found to patch children into: routeId = ${e}`),a.children||(a.children=[]),o=a.children}else o=r;let i=t.filter(a=>!o.some(s=>Ph(a,s))),u=Jo(i,l,[e||"_","patch",String((o==null?void 0:o.length)||"0")],n);o.push(...u)}function Ph(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,n)=>{var l;return(l=t.children)==null?void 0:l.some(o=>Ph(r,o))}):!1}var Yc=new WeakMap,_h=({key:e,route:t,manifest:r,mapRouteProperties:n})=>{let l=r[t.id];if(X(l,"No route found in manifest"),!l.lazy||typeof l.lazy!="object")return;let o=l.lazy[e];if(!o)return;let i=Yc.get(l);i||(i={},Yc.set(l,i));let u=i[e];if(u)return u;let a=(async()=>{let s=fy(e),y=l[e]!==void 0&&e!=="hasErrorBoundary";if(s)xe(!s,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),i[e]=Promise.resolve();else if(y)xe(!1,`Route "${l.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let p=await o();p!=null&&(Object.assign(l,{[e]:p}),Object.assign(l,n(l)))}typeof l.lazy=="object"&&(l.lazy[e]=void 0,Object.values(l.lazy).every(p=>p===void 0)&&(l.lazy=void 0))})();return i[e]=a,a},Gc=new WeakMap;function Wy(e,t,r,n,l){let o=r[e.id];if(X(o,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let d=Gc.get(o);if(d)return{lazyRoutePromise:d,lazyHandlerPromise:d};let y=(async()=>{X(typeof e.lazy=="function","No lazy route function found");let p=await e.lazy(),x={};for(let w in p){let N=p[w];if(N===void 0)continue;let A=py(w),h=o[w]!==void 0&&w!=="hasErrorBoundary";A?xe(!A,"Route property "+w+" is not a supported property to be returned from a lazy route function. This property will be ignored."):h?xe(!h,`Route "${o.id}" has a static property "${w}" defined but its lazy function is also returning a value for this property. The lazy route property "${w}" will be ignored.`):x[w]=N}Object.assign(o,x),Object.assign(o,{...n(o),lazy:void 0})})();return Gc.set(o,y),y.catch(()=>{}),{lazyRoutePromise:y,lazyHandlerPromise:y}}let i=Object.keys(e.lazy),u=[],a;for(let d of i){if(l&&l.includes(d))continue;let y=_h({key:d,route:e,manifest:r,mapRouteProperties:n});y&&(u.push(y),d===t&&(a=y))}let s=u.length>0?Promise.all(u).then(()=>{}):void 0;return s==null||s.catch(()=>{}),a==null||a.catch(()=>{}),{lazyRoutePromise:s,lazyHandlerPromise:a}}async function Jc(e){let t=e.matches.filter(l=>l.shouldLoad),r={};return(await Promise.all(t.map(l=>l.resolve()))).forEach((l,o)=>{r[t[o].route.id]=l}),r}async function Qy(e){return e.matches.some(t=>t.route.unstable_middleware)?Lh(e,!1,()=>Jc(e),(t,r)=>({[r]:{type:"error",result:t}})):Jc(e)}async function Lh(e,t,r,n){let{matches:l,request:o,params:i,context:u}=e,a={handlerResult:void 0};try{let s=l.flatMap(y=>y.route.unstable_middleware?y.route.unstable_middleware.map(p=>[y.route.id,p]):[]),d=await Ih({request:o,params:i,context:u},s,t,a,r);return t?d:a.handlerResult}catch(s){if(!a.middlewareError)throw s;let d=await n(a.middlewareError.error,a.middlewareError.routeId);return t||!a.handlerResult?d:Object.assign(a.handlerResult,d)}}async function Ih(e,t,r,n,l,o=0){let{request:i}=e;if(i.signal.aborted)throw i.signal.reason?i.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${i.method} ${i.url}`);let u=t[o];if(!u)return n.handlerResult=await l(),n.handlerResult;let[a,s]=u,d=!1,y,p=async()=>{if(d)throw new Error("You may only call `next()` once per middleware");d=!0;let x=await Ih(e,t,r,n,l,o+1);if(r)return y=x,y};try{let x=await s({request:e.request,params:e.params,context:e.context},p);return d?x===void 0?y:x:p()}catch(x){throw n.middlewareError?n.middlewareError.error!==x&&(n.middlewareError={routeId:a,error:x}):n.middlewareError={routeId:a,error:x},x}}function Mh(e,t,r,n,l){let o=_h({key:"unstable_middleware",route:n.route,manifest:t,mapRouteProperties:e}),i=Wy(n.route,tt(r.method)?"action":"loader",t,e,l);return{middleware:o,route:i.lazyRoutePromise,handler:i.lazyHandlerPromise}}function nu(e,t,r,n,l,o,i,u=null){let a=!1,s=Mh(e,t,r,n,l);return{...n,_lazyPromises:s,shouldLoad:i,unstable_shouldRevalidateArgs:u,unstable_shouldCallHandler(d){return a=!0,u?typeof d=="boolean"?Zo(n,{...u,defaultShouldRevalidate:d}):Zo(n,u):i},resolve(d){return a||i||d&&r.method==="GET"&&(n.route.lazy||n.route.loader)?Yy({request:r,match:n,lazyHandlerPromise:s==null?void 0:s.handler,lazyRoutePromise:s==null?void 0:s.route,handlerOverride:d,scopedContext:o}):Promise.resolve({type:"data",result:void 0})}}}function gn(e,t,r,n,l,o,i,u=null){return n.map(a=>a.route.id!==l.route.id?{...a,shouldLoad:!1,unstable_shouldRevalidateArgs:u,unstable_shouldCallHandler:()=>!1,_lazyPromises:Mh(e,t,r,a,o),resolve:()=>Promise.resolve({type:"data",result:void 0})}:nu(e,t,r,a,o,i,!0,u))}async function Ky(e,t,r,n,l,o){r.some(s=>{var d;return(d=s._lazyPromises)==null?void 0:d.middleware})&&await Promise.all(r.map(s=>{var d;return(d=s._lazyPromises)==null?void 0:d.middleware}));let i={request:t,params:r[0].params,context:l,matches:r},a=await e({...i,fetcherKey:n,unstable_runClientMiddleware:o?()=>{throw new Error("You cannot call `unstable_runClientMiddleware()` from a static handler `dataStrategy`. Middleware is run outside of `dataStrategy` during SSR in order to bubble up the Response.  You can enable middleware via the `respond` API in `query`/`queryRoute`")}:s=>{let d=i;return Lh(d,!1,()=>s({...d,fetcherKey:n,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(y,p)=>({[p]:{type:"error",result:y}}))}});try{await Promise.all(r.flatMap(s=>{var d,y;return[(d=s._lazyPromises)==null?void 0:d.handler,(y=s._lazyPromises)==null?void 0:y.route]}))}catch{}return a}async function Yy({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:n,handlerOverride:l,scopedContext:o}){let i,u,a=tt(e.method),s=a?"action":"loader",d=y=>{let p,x=new Promise((A,m)=>p=m);u=()=>p(),e.signal.addEventListener("abort",u);let w=A=>typeof y!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${s}" [routeId: ${t.route.id}]`)):y({request:e,params:t.params,context:o},...A!==void 0?[A]:[]),N=(async()=>{try{return{type:"data",result:await(l?l(m=>w(m)):w())}}catch(A){return{type:"error",result:A}}})();return Promise.race([N,x])};try{let y=a?t.route.action:t.route.loader;if(r||n)if(y){let p,[x]=await Promise.all([d(y).catch(w=>{p=w}),r,n]);if(p!==void 0)throw p;i=x}else{await r;let p=a?t.route.action:t.route.loader;if(p)[i]=await Promise.all([d(p),n]);else if(s==="action"){let x=new URL(e.url),w=x.pathname+x.search;throw st(405,{method:e.method,pathname:w,routeId:t.route.id})}else return{type:"data",result:void 0}}else if(y)i=await d(y);else{let p=new URL(e.url),x=p.pathname+p.search;throw st(404,{pathname:x})}}catch(y){return{type:"error",result:y}}finally{u&&e.signal.removeEventListener("abort",u)}return i}async function Gy(e){var n,l,o,i,u,a;let{result:t,type:r}=e;if(zh(t)){let s;try{let d=t.headers.get("Content-Type");d&&/\bapplication\/json\b/.test(d)?t.body==null?s=null:s=await t.json():s=await t.text()}catch(d){return{type:"error",error:d}}return r==="error"?{type:"error",error:new qo(t.status,t.statusText,s),statusCode:t.status,headers:t.headers}:{type:"data",data:s,statusCode:t.status,headers:t.headers}}return r==="error"?nd(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:(n=t.init)==null?void 0:n.status,headers:(l=t.init)!=null&&l.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new qo(((o=t.init)==null?void 0:o.status)||500,void 0,t.data),statusCode:Nl(t)?t.status:void 0,headers:(i=t.init)!=null&&i.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:Nl(t)?t.status:void 0}:nd(t)?{type:"data",data:t.data,statusCode:(u=t.init)==null?void 0:u.status,headers:(a=t.init)!=null&&a.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function Jy(e,t,r,n,l){let o=e.headers.get("Location");if(X(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!us.test(o)){let i=n.slice(0,n.findIndex(u=>u.route.id===r)+1);o=tu(new URL(t.url),i,l,o),e.headers.set("Location",o)}return e}function Xc(e,t,r){if(us.test(e)){let n=e,l=n.startsWith("//")?new URL(t.protocol+n):new URL(n),o=yt(l.pathname,r)!=null;if(l.origin===t.origin&&o)return l.pathname+l.search+l.hash}return e}function Jr(e,t,r,n){let l=e.createURL(Th(t)).toString(),o={signal:r};if(n&&tt(n.formMethod)){let{formMethod:i,formEncType:u}=n;o.method=i.toUpperCase(),u==="application/json"?(o.headers=new Headers({"Content-Type":u}),o.body=JSON.stringify(n.json)):u==="text/plain"?o.body=n.text:u==="application/x-www-form-urlencoded"&&n.formData?o.body=lu(n.formData):o.body=n.formData}return new Request(l,o)}function lu(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function qc(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function Xy(e,t,r,n=!1,l=!1){let o={},i=null,u,a=!1,s={},d=r&&et(r[1])?r[1].error:void 0;return e.forEach(y=>{if(!(y.route.id in t))return;let p=y.route.id,x=t[p];if(X(!Pr(x),"Cannot handle redirect results in processLoaderData"),et(x)){let w=x.error;if(d!==void 0&&(w=d,d=void 0),i=i||{},l)i[p]=w;else{let N=Br(e,p);i[N.route.id]==null&&(i[N.route.id]=w)}n||(o[p]=bh),a||(a=!0,u=Nl(x.error)?x.error.status:500),x.headers&&(s[p]=x.headers)}else o[p]=x.data,x.statusCode&&x.statusCode!==200&&!a&&(u=x.statusCode),x.headers&&(s[p]=x.headers)}),d!==void 0&&r&&(i={[r[0]]:d},r[2]&&(o[r[2]]=void 0)),{loaderData:o,errors:i,statusCode:u||200,loaderHeaders:s}}function Zc(e,t,r,n,l,o){let{loaderData:i,errors:u}=Xy(t,r,n);return l.filter(a=>!a.matches||a.matches.some(s=>s.shouldLoad)).forEach(a=>{let{key:s,match:d,controller:y}=a,p=o[s];if(X(p,"Did not find corresponding fetcher result"),!(y&&y.signal.aborted))if(et(p)){let x=Br(e.matches,d==null?void 0:d.route.id);u&&u[x.route.id]||(u={...u,[x.route.id]:p.error}),e.fetchers.delete(s)}else if(Pr(p))X(!1,"Unhandled fetcher revalidation redirect");else{let x=Jt(p.data);e.fetchers.set(s,x)}}),{loaderData:i,errors:u}}function ed(e,t,r,n){let l=Object.entries(t).filter(([,o])=>o!==bh).reduce((o,[i,u])=>(o[i]=u,o),{});for(let o of r){let i=o.route.id;if(!t.hasOwnProperty(i)&&e.hasOwnProperty(i)&&o.route.loader&&(l[i]=e[i]),n&&n.hasOwnProperty(i))break}return l}function td(e){return e?et(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function Br(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function rd(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function st(e,{pathname:t,routeId:r,method:n,type:l,message:o}={}){let i="Unknown Server Error",u="Unknown @remix-run/router error";return e===400?(i="Bad Request",n&&t&&r?u=`You made a ${n} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:l==="invalid-body"&&(u="Unable to encode submission body")):e===403?(i="Forbidden",u=`Route "${r}" does not match URL "${t}"`):e===404?(i="Not Found",u=`No route matches URL "${t}"`):e===405&&(i="Method Not Allowed",n&&t&&r?u=`You made a ${n.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:n&&(u=`Invalid request method "${n.toUpperCase()}"`)),new qo(e||500,i,new Error(u),!0)}function oo(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[n,l]=t[r];if(Pr(l))return{key:n,result:l}}}function Th(e){let t=typeof e=="string"?Vt(e):e;return pr({...t,hash:""})}function qy(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function Zy(e){return zh(e.result)&&Ty.has(e.result.status)}function et(e){return e.type==="error"}function Pr(e){return(e&&e.type)==="redirect"}function nd(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function zh(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function eg(e){return My.has(e.toUpperCase())}function tt(e){return Ly.has(e.toUpperCase())}function ss(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function Xn(e,t){let r=typeof t=="string"?Vt(t).search:t.search;if(e[e.length-1].route.index&&ss(r||""))return e[e.length-1];let n=Bh(e);return n[n.length-1]}function ld(e){let{formMethod:t,formAction:r,formEncType:n,text:l,formData:o,json:i}=e;if(!(!t||!r||!n)){if(l!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:l};if(o!=null)return{formMethod:t,formAction:r,formEncType:n,formData:o,json:void 0,text:void 0};if(i!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:i,text:void 0}}}function ia(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function tg(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function Hn(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function rg(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Jt(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function ng(e,t){try{let r=e.sessionStorage.getItem(Ah);if(r){let n=JSON.parse(r);for(let[l,o]of Object.entries(n||{}))o&&Array.isArray(o)&&t.set(l,new Set(o||[]))}}catch{}}function lg(e,t){if(t.size>0){let r={};for(let[n,l]of t)r[n]=[...l];try{e.sessionStorage.setItem(Ah,JSON.stringify(r))}catch(n){xe(!1,`Failed to save applied view transitions in sessionStorage (${n}).`)}}}function og(){let e,t,r=new Promise((n,l)=>{e=async o=>{n(o);try{await r}catch{}},t=async o=>{l(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var Ur=C.exports.createContext(null);Ur.displayName="DataRouter";var Pl=C.exports.createContext(null);Pl.displayName="DataRouterState";var cs=C.exports.createContext({isTransitioning:!1});cs.displayName="ViewTransition";var Oh=C.exports.createContext(new Map);Oh.displayName="Fetchers";var ig=C.exports.createContext(null);ig.displayName="Await";var Ct=C.exports.createContext(null);Ct.displayName="Navigation";var _l=C.exports.createContext(null);_l.displayName="Location";var Nt=C.exports.createContext({outlet:null,matches:[],isDataRoute:!1});Nt.displayName="Route";var ds=C.exports.createContext(null);ds.displayName="RouteError";function ag(e,{relative:t}={}){X(Rn(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:n}=C.exports.useContext(Ct),{hash:l,pathname:o,search:i}=Ll(e,{relative:t}),u=o;return r!=="/"&&(u=o==="/"?r:bt([r,o])),n.createHref({pathname:u,search:i,hash:l})}function Rn(){return C.exports.useContext(_l)!=null}function vr(){return X(Rn(),"useLocation() may be used only in the context of a <Router> component."),C.exports.useContext(_l).location}var $h="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function jh(e){C.exports.useContext(Ct).static||C.exports.useLayoutEffect(e)}function Uh(){let{isDataRoute:e}=C.exports.useContext(Nt);return e?Sg():ug()}function ug(){X(Rn(),"useNavigate() may be used only in the context of a <Router> component.");let e=C.exports.useContext(Ur),{basename:t,navigator:r}=C.exports.useContext(Ct),{matches:n}=C.exports.useContext(Nt),{pathname:l}=vr(),o=JSON.stringify(Ei(n)),i=C.exports.useRef(!1);return jh(()=>{i.current=!0}),C.exports.useCallback((a,s={})=>{if(xe(i.current,$h),!i.current)return;if(typeof a=="number"){r.go(a);return}let d=Ci(a,JSON.parse(o),l,s.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:bt([t,d.pathname])),(s.replace?r.replace:r.push)(d,s.state,s)},[t,r,o,l,e])}var sg=C.exports.createContext(null);function cg(e){let t=C.exports.useContext(Nt).outlet;return t&&C.exports.createElement(sg.Provider,{value:e},t)}function Ll(e,{relative:t}={}){let{matches:r}=C.exports.useContext(Nt),{pathname:n}=vr(),l=JSON.stringify(Ei(r));return C.exports.useMemo(()=>Ci(e,JSON.parse(l),n,t==="path"),[e,l,n,t])}function dg(e,t,r,n){var m;X(Rn(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:l}=C.exports.useContext(Ct),{matches:o}=C.exports.useContext(Nt),i=o[o.length-1],u=i?i.params:{},a=i?i.pathname:"/",s=i?i.pathnameBase:"/",d=i&&i.route;{let h=d&&d.path||"";Vh(a,!d||h.endsWith("*")||h.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${a}" (under <Route path="${h}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${h}"> to <Route path="${h==="/"?"*":`${h}/*`}">.`)}let y=vr(),p;if(t){let h=typeof t=="string"?Vt(t):t;X(s==="/"||((m=h.pathname)==null?void 0:m.startsWith(s)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${s}" but pathname "${h.pathname}" was given in the \`location\` prop.`),p=h}else p=y;let x=p.pathname||"/",w=x;if(s!=="/"){let h=s.replace(/^\//,"").split("/");w="/"+x.replace(/^\//,"").split("/").slice(h.length).join("/")}let N=tr(e,{pathname:w});xe(d||N!=null,`No routes matched location "${p.pathname}${p.search}${p.hash}" `),xe(N==null||N[N.length-1].route.element!==void 0||N[N.length-1].route.Component!==void 0||N[N.length-1].route.lazy!==void 0,`Matched leaf route at location "${p.pathname}${p.search}${p.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let A=yg(N&&N.map(h=>Object.assign({},h,{params:Object.assign({},u,h.params),pathname:bt([s,l.encodeLocation?l.encodeLocation(h.pathname).pathname:h.pathname]),pathnameBase:h.pathnameBase==="/"?s:bt([s,l.encodeLocation?l.encodeLocation(h.pathnameBase).pathname:h.pathnameBase])})),o,r,n);return t&&A?C.exports.createElement(_l.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...p},navigationType:"POP"}},A):A}function fg(){let e=kg(),t=Nl(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",l={padding:"0.5rem",backgroundColor:n},o={padding:"2px 4px",backgroundColor:n},i=null;return console.error("Error handled by React Router default ErrorBoundary:",e),i=C.exports.createElement(C.exports.Fragment,null,C.exports.createElement("p",null,"\u{1F4BF} Hey developer \u{1F44B}"),C.exports.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",C.exports.createElement("code",{style:o},"ErrorBoundary")," or"," ",C.exports.createElement("code",{style:o},"errorElement")," prop on your route.")),C.exports.createElement(C.exports.Fragment,null,C.exports.createElement("h2",null,"Unexpected Application Error!"),C.exports.createElement("h3",{style:{fontStyle:"italic"}},t),r?C.exports.createElement("pre",{style:l},r):null,i)}var hg=C.exports.createElement(fg,null),pg=class extends C.exports.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?C.exports.createElement(Nt.Provider,{value:this.props.routeContext},C.exports.createElement(ds.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function mg({routeContext:e,match:t,children:r}){let n=C.exports.useContext(Ur);return n&&n.static&&n.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=t.route.id),C.exports.createElement(Nt.Provider,{value:e},r)}function yg(e,t=[],r=null,n=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let l=e,o=r==null?void 0:r.errors;if(o!=null){let a=l.findIndex(s=>s.route.id&&(o==null?void 0:o[s.route.id])!==void 0);X(a>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),l=l.slice(0,Math.min(l.length,a+1))}let i=!1,u=-1;if(r)for(let a=0;a<l.length;a++){let s=l[a];if((s.route.HydrateFallback||s.route.hydrateFallbackElement)&&(u=a),s.route.id){let{loaderData:d,errors:y}=r,p=s.route.loader&&!d.hasOwnProperty(s.route.id)&&(!y||y[s.route.id]===void 0);if(s.route.lazy||p){i=!0,u>=0?l=l.slice(0,u+1):l=[l[0]];break}}}return l.reduceRight((a,s,d)=>{let y,p=!1,x=null,w=null;r&&(y=o&&s.route.id?o[s.route.id]:void 0,x=s.route.errorElement||hg,i&&(u<0&&d===0?(Vh("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),p=!0,w=null):u===d&&(p=!0,w=s.route.hydrateFallbackElement||null)));let N=t.concat(l.slice(0,d+1)),A=()=>{let m;return y?m=x:p?m=w:s.route.Component?m=C.exports.createElement(s.route.Component,null):s.route.element?m=s.route.element:m=a,C.exports.createElement(mg,{match:s,routeContext:{outlet:a,matches:N,isDataRoute:r!=null},children:m})};return r&&(s.route.ErrorBoundary||s.route.errorElement||d===0)?C.exports.createElement(pg,{location:r.location,revalidation:r.revalidation,component:x,error:y,children:A(),routeContext:{outlet:null,matches:N,isDataRoute:!0}}):A()},null)}function fs(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function gg(e){let t=C.exports.useContext(Ur);return X(t,fs(e)),t}function vg(e){let t=C.exports.useContext(Pl);return X(t,fs(e)),t}function xg(e){let t=C.exports.useContext(Nt);return X(t,fs(e)),t}function hs(e){let t=xg(e),r=t.matches[t.matches.length-1];return X(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function wg(){return hs("useRouteId")}function kg(){var n;let e=C.exports.useContext(ds),t=vg("useRouteError"),r=hs("useRouteError");return e!==void 0?e:(n=t.errors)==null?void 0:n[r]}function Sg(){let{router:e}=gg("useNavigate"),t=hs("useNavigate"),r=C.exports.useRef(!1);return jh(()=>{r.current=!0}),C.exports.useCallback(async(l,o={})=>{xe(r.current,$h),r.current&&(typeof l=="number"?e.navigate(l):await e.navigate(l,{fromRouteId:t,...o}))},[e,t])}var od={};function Vh(e,t,r){!t&&!od[e]&&(od[e]=!0,xe(!1,r))}var id={};function ad(e,t){!e&&!id[t]&&(id[t]=!0,console.warn(t))}function Eg(e){let t={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&xe(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:C.exports.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&xe(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:C.exports.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&xe(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:C.exports.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var Cg=["HydrateFallback","hydrateFallbackElement"],Ng=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",e(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",t(r))}})}};function Dg({router:e,flushSync:t}){let[r,n]=C.exports.useState(e.state),[l,o]=C.exports.useState(),[i,u]=C.exports.useState({isTransitioning:!1}),[a,s]=C.exports.useState(),[d,y]=C.exports.useState(),[p,x]=C.exports.useState(),w=C.exports.useRef(new Map),N=C.exports.useCallback((f,{deletedFetchers:k,flushSync:B,viewTransitionOpts:g})=>{f.fetchers.forEach((b,z)=>{b.data!==void 0&&w.current.set(z,b.data)}),k.forEach(b=>w.current.delete(b)),ad(B===!1||t!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let S=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(ad(g==null||S,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!g||!S){t&&B?t(()=>n(f)):C.exports.startTransition(()=>n(f));return}if(t&&B){t(()=>{d&&(a&&a.resolve(),d.skipTransition()),u({isTransitioning:!0,flushSync:!0,currentLocation:g.currentLocation,nextLocation:g.nextLocation})});let b=e.window.document.startViewTransition(()=>{t(()=>n(f))});b.finished.finally(()=>{t(()=>{s(void 0),y(void 0),o(void 0),u({isTransitioning:!1})})}),t(()=>y(b));return}d?(a&&a.resolve(),d.skipTransition(),x({state:f,currentLocation:g.currentLocation,nextLocation:g.nextLocation})):(o(f),u({isTransitioning:!0,flushSync:!1,currentLocation:g.currentLocation,nextLocation:g.nextLocation}))},[e.window,t,d,a]);C.exports.useLayoutEffect(()=>e.subscribe(N),[e,N]),C.exports.useEffect(()=>{i.isTransitioning&&!i.flushSync&&s(new Ng)},[i]),C.exports.useEffect(()=>{if(a&&l&&e.window){let f=l,k=a.promise,B=e.window.document.startViewTransition(async()=>{C.exports.startTransition(()=>n(f)),await k});B.finished.finally(()=>{s(void 0),y(void 0),o(void 0),u({isTransitioning:!1})}),y(B)}},[l,a,e.window]),C.exports.useEffect(()=>{a&&l&&r.location.key===l.location.key&&a.resolve()},[a,d,r.location,l]),C.exports.useEffect(()=>{!i.isTransitioning&&p&&(o(p.state),u({isTransitioning:!0,flushSync:!1,currentLocation:p.currentLocation,nextLocation:p.nextLocation}),x(void 0))},[i.isTransitioning,p]);let A=C.exports.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:f=>e.navigate(f),push:(f,k,B)=>e.navigate(f,{state:k,preventScrollReset:B==null?void 0:B.preventScrollReset}),replace:(f,k,B)=>e.navigate(f,{replace:!0,state:k,preventScrollReset:B==null?void 0:B.preventScrollReset})}),[e]),m=e.basename||"/",h=C.exports.useMemo(()=>({router:e,navigator:A,static:!1,basename:m}),[e,A,m]);return c(as,{children:c(Ur.Provider,{value:h,children:c(Pl.Provider,{value:r,children:c(Oh.Provider,{value:w.current,children:c(cs.Provider,{value:i,children:c(bg,{basename:m,location:r.location,navigationType:r.historyAction,navigator:A,children:c(Fg,{routes:e.routes,future:e.future,state:r})})})})})})})}var Fg=C.exports.memo(Bg);function Bg({routes:e,future:t,state:r}){return dg(e,void 0,r,t)}function Rg({to:e,replace:t,state:r,relative:n}){X(Rn(),"<Navigate> may be used only in the context of a <Router> component.");let{static:l}=C.exports.useContext(Ct);xe(!l,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:o}=C.exports.useContext(Nt),{pathname:i}=vr(),u=Uh(),a=Ci(e,Ei(o),i,n==="path"),s=JSON.stringify(a);return C.exports.useEffect(()=>{u(JSON.parse(s),{replace:t,state:r,relative:n})},[u,s,n,t,r]),null}function Ag(e){return cg(e.context)}function bg({basename:e="/",children:t=null,location:r,navigationType:n="POP",navigator:l,static:o=!1}){X(!Rn(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let i=e.replace(/^\/*/,"/"),u=C.exports.useMemo(()=>({basename:i,navigator:l,static:o,future:{}}),[i,l,o]);typeof r=="string"&&(r=Vt(r));let{pathname:a="/",search:s="",hash:d="",state:y=null,key:p="default"}=r,x=C.exports.useMemo(()=>{let w=yt(a,i);return w==null?null:{location:{pathname:w,search:s,hash:d,state:y,key:p},navigationType:n}},[i,a,s,d,y,p,n]);return xe(x!=null,`<Router basename="${i}"> is not able to match the URL "${a}${s}${d}" because it does not start with the basename, so the <Router> won't render anything.`),x==null?null:c(Ct.Provider,{value:u,children:c(_l.Provider,{children:t,value:x})})}var So="get",Eo="application/x-www-form-urlencoded";function Ni(e){return e!=null&&typeof e.tagName=="string"}function Pg(e){return Ni(e)&&e.tagName.toLowerCase()==="button"}function _g(e){return Ni(e)&&e.tagName.toLowerCase()==="form"}function Lg(e){return Ni(e)&&e.tagName.toLowerCase()==="input"}function Ig(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Mg(e,t){return e.button===0&&(!t||t==="_self")&&!Ig(e)}var io=null;function Tg(){if(io===null)try{new FormData(document.createElement("form"),0),io=!1}catch{io=!0}return io}var zg=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function aa(e){return e!=null&&!zg.has(e)?(xe(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Eo}"`),null):e}function Og(e,t){let r,n,l,o,i;if(_g(e)){let u=e.getAttribute("action");n=u?yt(u,t):null,r=e.getAttribute("method")||So,l=aa(e.getAttribute("enctype"))||Eo,o=new FormData(e)}else if(Pg(e)||Lg(e)&&(e.type==="submit"||e.type==="image")){let u=e.form;if(u==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let a=e.getAttribute("formaction")||u.getAttribute("action");if(n=a?yt(a,t):null,r=e.getAttribute("formmethod")||u.getAttribute("method")||So,l=aa(e.getAttribute("formenctype"))||aa(u.getAttribute("enctype"))||Eo,o=new FormData(u,e),!Tg()){let{name:s,type:d,value:y}=e;if(d==="image"){let p=s?`${s}.`:"";o.append(`${p}x`,"0"),o.append(`${p}y`,"0")}else s&&o.append(s,y)}}else{if(Ni(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=So,n=null,l=Eo,i=e}return o&&l==="text/plain"&&(i=o,o=void 0),{action:n,method:r.toLowerCase(),encType:l,formData:o,body:i}}function ps(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function $g(e,t){if(e.id in t)return t[e.id];try{let r=await Vm(()=>import(e.module),[]);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function jg(e){return e!=null&&typeof e.page=="string"}function Ug(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Vg(e,t,r){let n=await Promise.all(e.map(async l=>{let o=t.routes[l.route.id];if(o){let i=await $g(o,r);return i.links?i.links():[]}return[]}));return Kg(n.flat(1).filter(Ug).filter(l=>l.rel==="stylesheet"||l.rel==="preload").map(l=>l.rel==="stylesheet"?{...l,rel:"prefetch",as:"style"}:{...l,rel:"prefetch"}))}function ud(e,t,r,n,l,o){let i=(a,s)=>r[s]?a.route.id!==r[s].route.id:!0,u=(a,s)=>{var d;return r[s].pathname!==a.pathname||((d=r[s].route.path)==null?void 0:d.endsWith("*"))&&r[s].params["*"]!==a.params["*"]};return o==="assets"?t.filter((a,s)=>i(a,s)||u(a,s)):o==="data"?t.filter((a,s)=>{var y;let d=n.routes[a.route.id];if(!d||!d.hasLoader)return!1;if(i(a,s)||u(a,s))return!0;if(a.route.shouldRevalidate){let p=a.route.shouldRevalidate({currentUrl:new URL(l.pathname+l.search+l.hash,window.origin),currentParams:((y=r[0])==null?void 0:y.params)||{},nextUrl:new URL(e,window.origin),nextParams:a.params,defaultShouldRevalidate:!0});if(typeof p=="boolean")return p}return!0}):[]}function Hg(e,t,{includeHydrateFallback:r}={}){return Wg(e.map(n=>{let l=t.routes[n.route.id];if(!l)return[];let o=[l.module];return l.clientActionModule&&(o=o.concat(l.clientActionModule)),l.clientLoaderModule&&(o=o.concat(l.clientLoaderModule)),r&&l.hydrateFallbackModule&&(o=o.concat(l.hydrateFallbackModule)),l.imports&&(o=o.concat(l.imports)),o}).flat(1))}function Wg(e){return[...new Set(e)]}function Qg(e){let t={},r=Object.keys(e).sort();for(let n of r)t[n]=e[n];return t}function Kg(e,t){let r=new Set,n=new Set(t);return e.reduce((l,o)=>{if(t&&!jg(o)&&o.as==="script"&&o.href&&n.has(o.href))return l;let u=JSON.stringify(Qg(o));return r.has(u)||(r.add(u),l.push({key:u,link:o})),l},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Yg=new Set([100,101,204,205]);function Gg(e,t){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname="_root.data":t&&yt(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function Hh(){let e=C.exports.useContext(Ur);return ps(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Jg(){let e=C.exports.useContext(Pl);return ps(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var ms=C.exports.createContext(void 0);ms.displayName="FrameworkContext";function Wh(){let e=C.exports.useContext(ms);return ps(e,"You must render this element inside a <HydratedRouter> element"),e}function Xg(e,t){let r=C.exports.useContext(ms),[n,l]=C.exports.useState(!1),[o,i]=C.exports.useState(!1),{onFocus:u,onBlur:a,onMouseEnter:s,onMouseLeave:d,onTouchStart:y}=t,p=C.exports.useRef(null);C.exports.useEffect(()=>{if(e==="render"&&i(!0),e==="viewport"){let N=m=>{m.forEach(h=>{i(h.isIntersecting)})},A=new IntersectionObserver(N,{threshold:.5});return p.current&&A.observe(p.current),()=>{A.disconnect()}}},[e]),C.exports.useEffect(()=>{if(n){let N=setTimeout(()=>{i(!0)},100);return()=>{clearTimeout(N)}}},[n]);let x=()=>{l(!0)},w=()=>{l(!1),i(!1)};return r?e!=="intent"?[o,p,{}]:[o,p,{onFocus:Wn(u,x),onBlur:Wn(a,w),onMouseEnter:Wn(s,x),onMouseLeave:Wn(d,w),onTouchStart:Wn(y,x)}]:[!1,p,{}]}function Wn(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function qg({page:e,...t}){let{router:r}=Hh(),n=C.exports.useMemo(()=>tr(r.routes,e,r.basename),[r.routes,e,r.basename]);return n?C.exports.createElement(ev,{page:e,matches:n,...t}):null}function Zg(e){let{manifest:t,routeModules:r}=Wh(),[n,l]=C.exports.useState([]);return C.exports.useEffect(()=>{let o=!1;return Vg(e,t,r).then(i=>{o||l(i)}),()=>{o=!0}},[e,t,r]),n}function ev({page:e,matches:t,...r}){let n=vr(),{manifest:l,routeModules:o}=Wh(),{basename:i}=Hh(),{loaderData:u,matches:a}=Jg(),s=C.exports.useMemo(()=>ud(e,t,a,l,n,"data"),[e,t,a,l,n]),d=C.exports.useMemo(()=>ud(e,t,a,l,n,"assets"),[e,t,a,l,n]),y=C.exports.useMemo(()=>{if(e===n.pathname+n.search+n.hash)return[];let w=new Set,N=!1;if(t.forEach(m=>{var f;let h=l.routes[m.route.id];!h||!h.hasLoader||(!s.some(k=>k.route.id===m.route.id)&&m.route.id in u&&((f=o[m.route.id])==null?void 0:f.shouldRevalidate)||h.hasClientLoader?N=!0:w.add(m.route.id))}),w.size===0)return[];let A=Gg(e,i);return N&&w.size>0&&A.searchParams.set("_routes",t.filter(m=>w.has(m.route.id)).map(m=>m.route.id).join(",")),[A.pathname+A.search]},[i,u,n,l,s,t,e,o]),p=C.exports.useMemo(()=>Hg(d,l),[d,l]),x=Zg(d);return C.exports.createElement(C.exports.Fragment,null,y.map(w=>C.exports.createElement("link",{key:w,rel:"prefetch",as:"fetch",href:w,...r})),p.map(w=>C.exports.createElement("link",{key:w,rel:"modulepreload",href:w,...r})),x.map(({key:w,link:N})=>C.exports.createElement("link",{key:w,...N})))}function tv(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var Qh=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Qh&&(window.__reactRouterVersion="7.6.3")}catch{}function rv(e,t){return jy({basename:t==null?void 0:t.basename,unstable_getContext:t==null?void 0:t.unstable_getContext,future:t==null?void 0:t.future,history:uy({window:t==null?void 0:t.window}),hydrationData:(t==null?void 0:t.hydrationData)||nv(),routes:e,mapRouteProperties:Eg,hydrationRouteProperties:Cg,dataStrategy:t==null?void 0:t.dataStrategy,patchRoutesOnNavigation:t==null?void 0:t.patchRoutesOnNavigation,window:t==null?void 0:t.window}).initialize()}function nv(){let e=window==null?void 0:window.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:lv(e.errors)}),e}function lv(e){if(!e)return null;let t=Object.entries(e),r={};for(let[n,l]of t)if(l&&l.__type==="RouteErrorResponse")r[n]=new qo(l.status,l.statusText,l.data,l.internal===!0);else if(l&&l.__type==="Error"){if(l.__subType){let o=window[l.__subType];if(typeof o=="function")try{let i=new o(l.message);i.stack="",r[n]=i}catch{}}if(r[n]==null){let o=new Error(l.message);o.stack="",r[n]=o}}else r[n]=l;return r}var Kh=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Yh=C.exports.forwardRef(function({onClick:t,discover:r="render",prefetch:n="none",relative:l,reloadDocument:o,replace:i,state:u,target:a,to:s,preventScrollReset:d,viewTransition:y,...p},x){let{basename:w}=C.exports.useContext(Ct),N=typeof s=="string"&&Kh.test(s),A,m=!1;if(typeof s=="string"&&N&&(A=s,Qh))try{let z=new URL(window.location.href),$=s.startsWith("//")?new URL(z.protocol+s):new URL(s),G=yt($.pathname,w);$.origin===z.origin&&G!=null?s=G+$.search+$.hash:m=!0}catch{xe(!1,`<Link to="${s}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let h=ag(s,{relative:l}),[f,k,B]=Xg(n,p),g=av(s,{replace:i,state:u,target:a,preventScrollReset:d,relative:l,viewTransition:y});function S(z){t&&t(z),z.defaultPrevented||g(z)}let b=C.exports.createElement("a",{...p,...B,href:A||h,onClick:m||o?t:S,ref:tv(x,k),target:a,"data-discover":!N&&r==="render"?"true":void 0});return f&&!N?C.exports.createElement(C.exports.Fragment,null,b,C.exports.createElement(qg,{page:h})):b});Yh.displayName="Link";var Gh=C.exports.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:n="",end:l=!1,style:o,to:i,viewTransition:u,children:a,...s},d){let y=Ll(i,{relative:s.relative}),p=vr(),x=C.exports.useContext(Pl),{navigator:w,basename:N}=C.exports.useContext(Ct),A=x!=null&&fv(y)&&u===!0,m=w.encodeLocation?w.encodeLocation(y).pathname:y.pathname,h=p.pathname,f=x&&x.navigation&&x.navigation.location?x.navigation.location.pathname:null;r||(h=h.toLowerCase(),f=f?f.toLowerCase():null,m=m.toLowerCase()),f&&N&&(f=yt(f,N)||f);const k=m!=="/"&&m.endsWith("/")?m.length-1:m.length;let B=h===m||!l&&h.startsWith(m)&&h.charAt(k)==="/",g=f!=null&&(f===m||!l&&f.startsWith(m)&&f.charAt(m.length)==="/"),S={isActive:B,isPending:g,isTransitioning:A},b=B?t:void 0,z;typeof n=="function"?z=n(S):z=[n,B?"active":null,g?"pending":null,A?"transitioning":null].filter(Boolean).join(" ");let $=typeof o=="function"?o(S):o;return C.exports.createElement(Yh,{...s,"aria-current":b,className:z,ref:d,style:$,to:i,viewTransition:u},typeof a=="function"?a(S):a)});Gh.displayName="NavLink";var ov=C.exports.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:n,replace:l,state:o,method:i=So,action:u,onSubmit:a,relative:s,preventScrollReset:d,viewTransition:y,...p},x)=>{let w=cv(),N=dv(u,{relative:s}),A=i.toLowerCase()==="get"?"get":"post",m=typeof u=="string"&&Kh.test(u),h=f=>{if(a&&a(f),f.defaultPrevented)return;f.preventDefault();let k=f.nativeEvent.submitter,B=(k==null?void 0:k.getAttribute("formmethod"))||i;w(k||f.currentTarget,{fetcherKey:t,method:B,navigate:r,replace:l,state:o,relative:s,preventScrollReset:d,viewTransition:y})};return C.exports.createElement("form",{ref:x,method:A,action:N,onSubmit:n?a:h,...p,"data-discover":!m&&e==="render"?"true":void 0})});ov.displayName="Form";function iv(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Jh(e){let t=C.exports.useContext(Ur);return X(t,iv(e)),t}function av(e,{target:t,replace:r,state:n,preventScrollReset:l,relative:o,viewTransition:i}={}){let u=Uh(),a=vr(),s=Ll(e,{relative:o});return C.exports.useCallback(d=>{if(Mg(d,t)){d.preventDefault();let y=r!==void 0?r:pr(a)===pr(s);u(e,{replace:y,state:n,preventScrollReset:l,relative:o,viewTransition:i})}},[a,u,s,r,n,t,e,l,o,i])}var uv=0,sv=()=>`__${String(++uv)}__`;function cv(){let{router:e}=Jh("useSubmit"),{basename:t}=C.exports.useContext(Ct),r=wg();return C.exports.useCallback(async(n,l={})=>{let{action:o,method:i,encType:u,formData:a,body:s}=Og(n,t);if(l.navigate===!1){let d=l.fetcherKey||sv();await e.fetch(d,r,l.action||o,{preventScrollReset:l.preventScrollReset,formData:a,body:s,formMethod:l.method||i,formEncType:l.encType||u,flushSync:l.flushSync})}else await e.navigate(l.action||o,{preventScrollReset:l.preventScrollReset,formData:a,body:s,formMethod:l.method||i,formEncType:l.encType||u,replace:l.replace,state:l.state,fromRouteId:r,flushSync:l.flushSync,viewTransition:l.viewTransition})},[e,t,r])}function dv(e,{relative:t}={}){let{basename:r}=C.exports.useContext(Ct),n=C.exports.useContext(Nt);X(n,"useFormAction must be used inside a RouteContext");let[l]=n.matches.slice(-1),o={...Ll(e||".",{relative:t})},i=vr();if(e==null){o.search=i.search;let u=new URLSearchParams(o.search),a=u.getAll("index");if(a.some(d=>d==="")){u.delete("index"),a.filter(y=>y).forEach(y=>u.append("index",y));let d=u.toString();o.search=d?`?${d}`:""}}return(!e||e===".")&&l.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(o.pathname=o.pathname==="/"?r:bt([r,o.pathname])),pr(o)}function fv(e,t={}){let r=C.exports.useContext(cs);X(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:n}=Jh("useViewTransitionState"),l=Ll(e,{relative:t.relative});if(!r.isTransitioning)return!1;let o=yt(r.currentLocation.pathname,n)||r.currentLocation.pathname,i=yt(r.nextLocation.pathname,n)||r.nextLocation.pathname;return Xo(l.pathname,i)!=null||Xo(l.pathname,o)!=null}[...Yg];/**
 * react-router v7.6.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function hv(e){return c(Dg,{flushSync:mu.exports.flushSync,...e})}function Xh(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var l=e.length;for(t=0;t<l;t++)e[t]&&(r=Xh(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function ye(){for(var e,t,r=0,n="",l=arguments.length;r<l;r++)(e=arguments[r])&&(t=Xh(e))&&(n&&(n+=" "),n+=t);return n}/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pv=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),mv=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,r,n)=>n?n.toUpperCase():r.toLowerCase()),sd=e=>{const t=mv(e);return t.charAt(0).toUpperCase()+t.slice(1)},qh=(...e)=>e.filter((t,r,n)=>Boolean(t)&&t.trim()!==""&&n.indexOf(t)===r).join(" ").trim(),yv=e=>{for(const t in e)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var gv={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vv=C.exports.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:l="",children:o,iconNode:i,...u},a)=>C.exports.createElement("svg",{ref:a,...gv,width:t,height:t,stroke:e,strokeWidth:n?Number(r)*24/Number(t):r,className:qh("lucide",l),...!o&&!yv(u)&&{"aria-hidden":"true"},...u},[...i.map(([s,d])=>C.exports.createElement(s,d)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=(e,t)=>{const r=C.exports.forwardRef(({className:n,...l},o)=>C.exports.createElement(vv,{ref:o,iconNode:t,className:qh(`lucide-${pv(sd(e))}`,`lucide-${e}`,n),...l}));return r.displayName=sd(e),r};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xv=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],cd=q("activity",xv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wv=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],ei=q("building",wv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kv=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],ti=q("calendar",kv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sv=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Zh=q("chevron-down",Sv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ev=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],Cv=q("chevron-left",Ev);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nv=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Dv=q("chevron-right",Nv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fv=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],dd=q("chevron-up",Fv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bv=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],Rv=q("circle-alert",Bv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Av=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],fd=q("clock",Av);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bv=[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]],Pv=q("coins",bv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _v=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],Lv=q("copy",_v);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Iv=[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]],ri=q("database",Iv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mv=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],ou=q("dollar-sign",Mv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tv=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],iu=q("download",Tv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zv=[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]],Ov=q("external-link",zv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $v=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],jv=q("file-text",$v);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uv=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],Ht=q("funnel",Uv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vv=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]],au=q("grid-3x3",Vv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hv=[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]],ni=q("hash",Hv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wv=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],Qv=q("info",Wv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kv=[["path",{d:"M9 17H7A5 5 0 0 1 7 7h2",key:"8i5ue5"}],["path",{d:"M15 7h2a5 5 0 1 1 0 10h-2",key:"1b9ql8"}],["line",{x1:"8",x2:"16",y1:"12",y2:"12",key:"1jonct"}]],ua=q("link-2",Kv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yv=[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]],Gv=q("link",Yv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jv=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Xv=q("menu",Jv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qv=[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]],Zv=q("pause",qv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e1=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],t1=q("play",e1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r1=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],n1=q("plus",r1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l1=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],Il=q("refresh-cw",l1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const o1=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],i1=q("rotate-ccw",o1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a1=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],li=q("search",a1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u1=[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]],s1=q("server",u1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c1=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Dl=q("settings",c1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d1=[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]],oi=q("shopping-cart",d1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f1=[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]],h1=q("tag",f1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p1=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],uu=q("user",p1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m1=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],su=q("users",m1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y1=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],g1=q("wifi-off",y1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v1=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]],x1=q("wifi",v1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w1=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],cu=q("x",w1),k1=[{name:"\u7528\u6237\u670D\u52A1",href:"/user-services",icon:su,description:"\u7528\u6237\u670D\u52A1\u8BB0\u5F55\u7BA1\u7406"},{name:"\u8BA2\u5355",href:"/orders",icon:oi,description:"\u8BA2\u5355\u4FE1\u606F\u7BA1\u7406"},{name:"\u8BA2\u5355\u670D\u52A1",href:"/order-services",icon:Gv,description:"\u8BA2\u5355\u670D\u52A1\u5173\u8054"},{name:"CLI\u7248\u672C",href:"/cli-versions",icon:iu,description:"CLI\u7248\u672C\u7BA1\u7406"},{name:"\u8D27\u5E01",href:"/currencies",icon:ou,description:"\u8D27\u5E01\u4FE1\u606F\u7BA1\u7406"},{name:"\u670D\u52A1\u5206\u7C7B",href:"/service-categories",icon:au,description:"\u670D\u52A1\u5206\u7C7B\u7BA1\u7406"},{name:"\u670D\u52A1\u63D0\u4F9B\u5546",href:"/providers",icon:ei,description:"\u670D\u52A1\u63D0\u4F9B\u5546\u7BA1\u7406"},{name:"\u670D\u52A1\u7C7B\u578B",href:"/service-types",icon:Dl,description:"\u670D\u52A1\u7C7B\u578B\u7BA1\u7406"}],S1=({isOpen:e,onToggle:t})=>E("div",{className:ye("fixed left-0 top-0 h-full bg-white shadow-lg transition-all duration-300 z-30",e?"w-64":"w-16"),children:[E("div",{className:"flex items-center justify-between p-4 border-b",children:[E("div",{className:ye("flex items-center space-x-2",!e&&"justify-center"),children:[c(ri,{className:"h-8 w-8 text-blue-600"}),e&&c("span",{className:"text-xl font-bold text-gray-900",children:"VCloud DB"})]}),c("button",{onClick:t,className:"p-1 rounded-md hover:bg-gray-100 transition-colors",children:e?c(Cv,{className:"h-5 w-5 text-gray-500"}):c(Dv,{className:"h-5 w-5 text-gray-500"})})]}),c("nav",{className:"mt-4 px-2",children:c("ul",{className:"space-y-1",children:k1.map(r=>{const n=r.icon;return c("li",{children:E(Gh,{to:r.href,className:({isActive:l})=>ye("flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors","hover:bg-gray-100 hover:text-gray-900",l?"bg-blue-100 text-blue-700":"text-gray-600",!e&&"justify-center"),title:e?void 0:r.name,children:[c(n,{className:ye("h-5 w-5",e?"mr-3":"mx-auto")}),e&&E("div",{className:"flex-1",children:[c("div",{className:"font-medium",children:r.name}),c("div",{className:"text-xs text-gray-500 mt-0.5",children:r.description})]})]})},r.href)})})})]});function E1(e){return window.go.backend.VCloudDBService.CountOrders(e)}function C1(e,t){return window.go.backend.VCloudDBService.CountRecords(e,t)}function N1(e){return window.go.backend.VCloudDBService.CountUserServices(e)}function D1(e){return window.go.backend.VCloudDBService.FindCliVersions(e)}function F1(e){return window.go.backend.VCloudDBService.FindCurrencies(e)}function B1(e){return window.go.backend.VCloudDBService.FindOrderServices(e)}function R1(e){return window.go.backend.VCloudDBService.FindOrders(e)}function A1(e,t){return window.go.backend.VCloudDBService.FindRecords(e,t)}function b1(e){return window.go.backend.VCloudDBService.FindUserServices(e)}function e0(){return window.go.backend.VCloudDBService.GetConnectionStatus()}function P1(e,t){return window.go.backend.VCloudDBService.GetRecordById(e,t)}function _1(){return window.go.backend.VCloudDBService.GetTableNames()}function L1(e){return window.go.backend.VCloudDBService.SetRPCURL(e)}const Gr={USER_SERVICE:"user_service",ORDER:"order",ORDER_SERVICE:"order_service",CLI_VERSION:"cli_version",CURRENCY:"currency",SERVICE_CATEGORY:"service_category",PROVIDER:"provider",SERVICE_TYPE:"service_type"},cn=class{constructor(){Kr(this,"cache",new Map);Kr(this,"CACHE_TTL",5*60*1e3)}static getInstance(){return cn.instance||(cn.instance=new cn),cn.instance}getCacheKey(t,r){return`${t}_${JSON.stringify(r)}`}getFromCache(t){const r=this.cache.get(t);return r&&Date.now()-r.timestamp<this.CACHE_TTL?r.data:(this.cache.delete(t),null)}setCache(t,r){this.cache.set(t,{data:r,timestamp:Date.now()})}clearCache(){this.cache.clear()}async handleApiCall(t,r,n=!1,l){try{if(n&&l){const i=this.getFromCache(l);if(i)return{data:i,success:!0}}const o=await t();return n&&l&&this.setCache(l,o),{data:o,success:!0}}catch(o){return console.error(`${r}:`,o),{data:null,success:!1,error:o instanceof Error?o.message:String(o)}}}async getConnectionStatus(){return this.handleApiCall(async()=>await e0(),"\u83B7\u53D6\u8FDE\u63A5\u72B6\u6001\u5931\u8D25")}async setRPCURL(t){return this.clearCache(),this.handleApiCall(()=>L1(t),"\u8BBE\u7F6ERPC URL\u5931\u8D25")}async findRecords(t,r={}){const n=this.getCacheKey("findRecords",{tableName:t,filter:r});return this.handleApiCall(async()=>(await A1(t,r)).map(o=>JSON.parse(o)),`\u67E5\u8BE2${t}\u8868\u8BB0\u5F55\u5931\u8D25`,!0,n)}async getRecordById(t,r){const n=this.getCacheKey("getRecordById",{tableName:t,id:r});return this.handleApiCall(async()=>{const l=await P1(t,r);return JSON.parse(l)},`\u83B7\u53D6${t}\u8868\u8BB0\u5F55\u5931\u8D25`,!0,n)}async countRecords(t,r={}){const n=this.getCacheKey("countRecords",{tableName:t,filter:r});return this.handleApiCall(()=>C1(t,r),`\u7EDF\u8BA1${t}\u8868\u8BB0\u5F55\u5931\u8D25`,!0,n)}async findUserServices(t={}){const r=this.getCacheKey("findUserServices",t);try{const n=this.getFromCache(r);if(n)return n;const[l,o]=await Promise.all([this.handleApiCall(async()=>(await b1(t)).map(a=>JSON.parse(a)),"\u67E5\u8BE2\u7528\u6237\u670D\u52A1\u5931\u8D25"),this.handleApiCall(()=>N1(t),"\u7EDF\u8BA1\u7528\u6237\u670D\u52A1\u5931\u8D25")]);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const i={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,i),i}catch(n){return console.error("\u67E5\u8BE2\u7528\u6237\u670D\u52A1\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findOrders(t={}){const r=this.getCacheKey("findOrders",t);try{const n=this.getFromCache(r);if(n)return n;const[l,o]=await Promise.all([this.handleApiCall(async()=>(await R1(t)).map(a=>JSON.parse(a)),"\u67E5\u8BE2\u8BA2\u5355\u5931\u8D25"),this.handleApiCall(()=>E1(t),"\u7EDF\u8BA1\u8BA2\u5355\u5931\u8D25")]);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const i={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,i),i}catch(n){return console.error("\u67E5\u8BE2\u8BA2\u5355\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findOrderServices(t={}){const r=this.getCacheKey("findOrderServices",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await B1(t)).map(a=>JSON.parse(a)),"\u67E5\u8BE2\u8BA2\u5355\u670D\u52A1\u5931\u8D25"),o=await this.countRecords(Gr.ORDER_SERVICE,t);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const i={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,i),i}catch(n){return console.error("\u67E5\u8BE2\u8BA2\u5355\u670D\u52A1\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findCliVersions(t={}){const r=this.getCacheKey("findCliVersions",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await D1(t)).map(a=>JSON.parse(a)),"\u67E5\u8BE2CLI\u7248\u672C\u5931\u8D25"),o=await this.countRecords(Gr.CLI_VERSION,t);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const i={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,i),i}catch(n){return console.error("\u67E5\u8BE2CLI\u7248\u672C\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findCurrencies(t={}){const r=this.getCacheKey("findCurrencies",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await F1(t)).map(a=>JSON.parse(a)),"\u67E5\u8BE2\u8D27\u5E01\u5931\u8D25"),o=await this.countRecords(Gr.CURRENCY,t);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const i={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,i),i}catch(n){return console.error("\u67E5\u8BE2\u8D27\u5E01\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findServiceCategories(t){const r=this.getCacheKey("findServiceCategories",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await this.findRecords(Gr.SERVICE_CATEGORY,t)).data,"\u67E5\u8BE2\u670D\u52A1\u5206\u7C7B\u5931\u8D25");if(!l.success)return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:l.error};const o={data:l.data,total:l.data.length,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,o),o}catch(n){return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findProviders(t){const r=this.getCacheKey("findProviders",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await this.findRecords(Gr.PROVIDER,t)).data,"\u67E5\u8BE2\u670D\u52A1\u63D0\u4F9B\u5546\u5931\u8D25");if(!l.success)return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:l.error};const o={data:l.data,total:l.data.length,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,o),o}catch(n){return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findServiceTypes(t){const r=this.getCacheKey("findServiceTypes",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await this.findRecords(Gr.SERVICE_TYPE,t)).data,"\u67E5\u8BE2\u670D\u52A1\u7C7B\u578B\u5931\u8D25");if(!l.success)return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:l.error};const o={data:l.data,total:l.data.length,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,o),o}catch(n){return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async getTableNames(){return this.handleApiCall(()=>_1(),"\u83B7\u53D6\u8868\u540D\u5217\u8868\u5931\u8D25",!0,"tableNames")}};let Co=cn;Kr(Co,"instance");const Oe=Co.getInstance();class I1{constructor(){Kr(this,"listeners",new Set);Kr(this,"globalAutoRefresh",{enabled:!1,interval:30})}subscribe(t){return this.listeners.add(t),()=>this.listeners.delete(t)}triggerGlobalRefresh(){this.listeners.forEach(t=>t())}setGlobalAutoRefresh(t){this.globalAutoRefresh=t}getGlobalAutoRefresh(){return this.globalAutoRefresh}}const No=new I1;function xr(e,t,r=[],n){const[l,o]=C.exports.useState({data:[],total:0,loading:!1,error:null,page:1,pageSize:20,lastRefresh:void 0,autoRefresh:(n==null?void 0:n.enabled)||!1,refreshInterval:(n==null?void 0:n.interval)||30}),[i,u]=C.exports.useState(t),a=C.exports.useRef(),s=C.exports.useRef(),d=C.exports.useRef(null),y=C.exports.useCallback(async(A,m=!1)=>{a.current&&a.current.abort(),a.current=new AbortController,o(h=>({...h,loading:!m,error:null}));try{const h=await e(A);h.success?o(f=>({...f,data:h.data,total:h.total,page:h.page,pageSize:h.pageSize,loading:!1,error:null,lastRefresh:new Date})):o(f=>({...f,data:[],total:0,loading:!1,error:h.error||"\u67E5\u8BE2\u5931\u8D25"}))}catch(h){h instanceof Error&&h.name!=="AbortError"&&o(f=>({...f,data:[],total:0,loading:!1,error:h instanceof Error?h.message:String(h)}))}},[e]),p=C.exports.useCallback((A=!1)=>{y(i,A)},[y,i]),x=C.exports.useCallback(A=>{const m={...i,...A};u(m),y(m)},[i,y]),w=C.exports.useCallback((A,m)=>{const h=m||l.pageSize,f=(A-1)*h;x({offset:f,limit:h})},[l.pageSize,x]),N=C.exports.useCallback((A,m)=>{o(h=>({...h,autoRefresh:A,refreshInterval:m||h.refreshInterval}))},[]);return C.exports.useEffect(()=>(l.autoRefresh&&l.refreshInterval&&l.refreshInterval>0?s.current=setInterval(()=>{p(!0)},l.refreshInterval*1e3):s.current&&(clearInterval(s.current),s.current=void 0),()=>{s.current&&clearInterval(s.current)}),[l.autoRefresh,l.refreshInterval,p]),C.exports.useEffect(()=>(d.current=No.subscribe(()=>{p()}),()=>{d.current&&d.current()}),[p]),C.exports.useEffect(()=>{y(i)},r),C.exports.useEffect(()=>()=>{a.current&&a.current.abort(),s.current&&clearInterval(s.current)},[]),{...l,params:i,refetch:p,updateParams:x,changePage:w,setAutoRefresh:N}}function M1(e={},t){return xr(Oe.findUserServices.bind(Oe),{limit:20,offset:0,...e},[],t)}function T1(e={},t){return xr(Oe.findOrders.bind(Oe),{limit:20,offset:0,...e},[],t)}function z1(e={},t){return xr(Oe.findOrderServices.bind(Oe),{limit:20,offset:0,...e},[],t)}function O1(e={},t){return xr(Oe.findCliVersions.bind(Oe),{limit:20,offset:0,...e},[],t)}function $1(e={},t){return xr(Oe.findCurrencies.bind(Oe),{limit:20,offset:0,...e},[],t)}function j1(e={},t){return xr(Oe.findServiceCategories.bind(Oe),{limit:20,offset:0,...e},[],t)}function U1(e={},t){return xr(Oe.findProviders.bind(Oe),{limit:20,offset:0,...e},[],t)}function V1(e={},t){return xr(Oe.findServiceTypes.bind(Oe),{limit:20,offset:0,...e},[],t)}function H1(){const[e,t]=C.exports.useState(No.getGlobalAutoRefresh()),r=C.exports.useCallback(()=>{No.triggerGlobalRefresh()},[]),n=C.exports.useCallback(l=>{t(l),No.setGlobalAutoRefresh(l)},[]);return{globalConfig:e,triggerGlobalRefresh:r,setGlobalAutoRefresh:n}}const W1=({onMenuClick:e,sidebarOpen:t})=>{const[r,n]=C.exports.useState({connected:!1,rpcUrl:""}),[l,o]=C.exports.useState(!1),{triggerGlobalRefresh:i}=H1(),u=async()=>{o(!0);try{const a=await e0();n(a)}catch(a){n({connected:!1,rpcUrl:"",error:a instanceof Error?a.message:"\u8FDE\u63A5\u68C0\u67E5\u5931\u8D25"})}finally{o(!1)}};return C.exports.useEffect(()=>{u();const a=setInterval(u,3e4);return()=>clearInterval(a)},[]),c("header",{className:"bg-white shadow-sm border-b border-gray-200",children:E("div",{className:"flex items-center justify-between px-6 py-4",children:[E("div",{className:"flex items-center space-x-4",children:[c("button",{onClick:e,className:"p-2 rounded-md hover:bg-gray-100 transition-colors",children:c(Xv,{className:"h-5 w-5 text-gray-600"})}),E("nav",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[c("span",{children:"VCloud DB"}),c("span",{children:"/"}),c("span",{className:"text-gray-900 font-medium",children:"\u6570\u636E\u7BA1\u7406"})]})]}),E("div",{className:"flex items-center space-x-4",children:[E("button",{onClick:i,className:"flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-medium bg-blue-50 text-blue-700 hover:bg-blue-100 transition-colors",title:"\u5237\u65B0\u6240\u6709\u6570\u636E",children:[c(i1,{className:"h-4 w-4"}),c("span",{children:"\u5168\u5C40\u5237\u65B0"})]}),E("div",{className:"flex items-center space-x-2",children:[E("button",{onClick:u,disabled:l,className:ye("flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors",r.connected?"bg-green-100 text-green-700 hover:bg-green-200":"bg-red-100 text-red-700 hover:bg-red-200"),title:r.rpcUrl||"\u70B9\u51FB\u68C0\u67E5\u8FDE\u63A5\u72B6\u6001",children:[l?c(Il,{className:"h-4 w-4 animate-spin"}):r.connected?c(x1,{className:"h-4 w-4"}):c(g1,{className:"h-4 w-4"}),c("span",{children:r.connected?"\u5DF2\u8FDE\u63A5":"\u672A\u8FDE\u63A5"})]}),r.error&&E("div",{className:"flex items-center space-x-1 text-red-600",children:[c(Rv,{className:"h-4 w-4"}),c("span",{className:"text-xs",title:r.error,children:"\u9519\u8BEF"})]})]}),c("button",{className:"p-2 rounded-md hover:bg-gray-100 transition-colors",title:"\u8BBE\u7F6E",children:c(Dl,{className:"h-5 w-5 text-gray-600"})})]})]})})},Q1=({children:e})=>{const[t,r]=C.exports.useState(!0);return E("div",{className:"min-h-screen bg-gray-50 flex",children:[c(S1,{isOpen:t,onToggle:()=>r(!t)}),E("div",{className:ye("flex-1 flex flex-col transition-all duration-300",t?"ml-64":"ml-16"),children:[c(W1,{onMenuClick:()=>r(!t),sidebarOpen:t}),c("main",{className:"flex-1 p-6 overflow-auto",children:e||c(Ag,{})})]})]})},ao=({title:e,value:t,icon:r,description:n,trend:l})=>c("div",{className:"bg-white rounded-lg shadow p-6",children:E("div",{className:"flex items-center justify-between",children:[E("div",{children:[c("p",{className:"text-sm font-medium text-gray-600",children:e}),c("p",{className:"text-3xl font-bold text-gray-900",children:t}),n&&c("p",{className:"text-sm text-gray-500 mt-1",children:n}),l&&E("div",{className:`flex items-center mt-2 text-sm ${l.isPositive?"text-green-600":"text-red-600"}`,children:[E("span",{children:[l.isPositive?"+":"",l.value,"%"]}),c("span",{className:"text-gray-500 ml-1",children:"vs \u4E0A\u6708"})]})]}),c("div",{className:"p-3 bg-blue-50 rounded-full",children:c(r,{className:"h-8 w-8 text-blue-600"})})]})}),K1=()=>E("div",{className:"space-y-6",children:[E("div",{children:[c("h1",{className:"text-2xl font-bold text-gray-900",children:"\u4EEA\u8868\u677F"}),c("p",{className:"text-gray-600 mt-1",children:"VCloud DB \u6570\u636E\u6982\u89C8"})]}),E("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[c(ao,{title:"\u7528\u6237\u670D\u52A1",value:"1,234",icon:su,description:"\u6D3B\u8DC3\u670D\u52A1\u6570\u91CF",trend:{value:12,isPositive:!0}}),c(ao,{title:"\u8BA2\u5355\u603B\u6570",value:"5,678",icon:oi,description:"\u7D2F\u8BA1\u8BA2\u5355\u6570\u91CF",trend:{value:8,isPositive:!0}}),c(ao,{title:"\u6570\u636E\u8868",value:"8",icon:ri,description:"\u53EF\u7528\u6570\u636E\u8868"}),c(ao,{title:"\u7CFB\u7EDF\u72B6\u6001",value:"\u6B63\u5E38",icon:cd,description:"\u670D\u52A1\u8FD0\u884C\u72B6\u6001"})]}),E("div",{className:"bg-white rounded-lg shadow p-6",children:[c("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"\u5FEB\u901F\u64CD\u4F5C"}),E("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[E("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[c(su,{className:"h-6 w-6 text-blue-600 mb-2"}),c("div",{className:"font-medium text-gray-900",children:"\u67E5\u770B\u7528\u6237\u670D\u52A1"}),c("div",{className:"text-sm text-gray-500",children:"\u7BA1\u7406\u7528\u6237\u670D\u52A1\u8BB0\u5F55"})]}),E("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[c(oi,{className:"h-6 w-6 text-green-600 mb-2"}),c("div",{className:"font-medium text-gray-900",children:"\u67E5\u770B\u8BA2\u5355"}),c("div",{className:"text-sm text-gray-500",children:"\u7BA1\u7406\u8BA2\u5355\u4FE1\u606F"})]}),E("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[c(ri,{className:"h-6 w-6 text-purple-600 mb-2"}),c("div",{className:"font-medium text-gray-900",children:"\u6570\u636E\u67E5\u8BE2"}),c("div",{className:"text-sm text-gray-500",children:"\u6267\u884C\u81EA\u5B9A\u4E49\u67E5\u8BE2"})]}),E("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[c(cd,{className:"h-6 w-6 text-orange-600 mb-2"}),c("div",{className:"font-medium text-gray-900",children:"\u7CFB\u7EDF\u76D1\u63A7"}),c("div",{className:"text-sm text-gray-500",children:"\u67E5\u770B\u7CFB\u7EDF\u72B6\u6001"})]})]})]}),E("div",{className:"bg-white rounded-lg shadow p-6",children:[c("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"\u6700\u8FD1\u6D3B\u52A8"}),E("div",{className:"space-y-3",children:[E("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[c("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),E("div",{className:"flex-1",children:[c("p",{className:"text-sm font-medium text-gray-900",children:"\u65B0\u7528\u6237\u670D\u52A1\u521B\u5EFA"}),c("p",{className:"text-xs text-gray-500",children:"2\u5206\u949F\u524D"})]})]}),E("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[c("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),E("div",{className:"flex-1",children:[c("p",{className:"text-sm font-medium text-gray-900",children:"\u8BA2\u5355\u72B6\u6001\u66F4\u65B0"}),c("p",{className:"text-xs text-gray-500",children:"5\u5206\u949F\u524D"})]})]}),E("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[c("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),E("div",{className:"flex-1",children:[c("p",{className:"text-sm font-medium text-gray-900",children:"\u7CFB\u7EDF\u914D\u7F6E\u66F4\u65B0"}),c("p",{className:"text-xs text-gray-500",children:"10\u5206\u949F\u524D"})]})]})]})]})]});function wr({columns:e,data:t,loading:r=!1,pagination:n,sortConfig:l,onSort:o,rowKey:i="_id",onRowClick:u,emptyText:a="\u6682\u65E0\u6570\u636E",className:s}){const d=(w,N)=>typeof i=="function"?i(w):w[i]||N.toString(),y=w=>{if(!o)return;let N="asc";(l==null?void 0:l.key)===w&&l.direction==="asc"&&(N="desc"),o(w,N)},p=w=>(l==null?void 0:l.key)!==w?c(dd,{className:"h-4 w-4 text-gray-300"}):l.direction==="asc"?c(dd,{className:"h-4 w-4 text-gray-600"}):c(Zh,{className:"h-4 w-4 text-gray-600"}),x=(w,N)=>{if(N.render){const A=t.indexOf(w),m=N.dataIndex?w[N.dataIndex]:w;return N.render(m,w,A)}return N.dataIndex?w[N.dataIndex]:""};return r?c("div",{className:ye("bg-white rounded-lg shadow",s),children:E("div",{className:"p-8 text-center",children:[c("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),c("p",{className:"text-gray-500 mt-2",children:"\u52A0\u8F7D\u4E2D..."})]})}):E("div",{className:ye("bg-white rounded-lg shadow overflow-hidden",s),children:[c("div",{className:"overflow-x-auto",children:E("table",{className:"min-w-full divide-y divide-gray-200",children:[c("thead",{className:"bg-gray-50",children:c("tr",{children:e.map(w=>c("th",{className:ye("px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",w.align==="center"&&"text-center",w.align==="right"&&"text-right",w.sortable&&"cursor-pointer hover:bg-gray-100"),style:{width:w.width},onClick:()=>w.sortable&&y(w.key),children:E("div",{className:"flex items-center space-x-1",children:[c("span",{children:w.title}),w.sortable&&p(w.key)]})},w.key))})}),c("tbody",{className:"bg-white divide-y divide-gray-200",children:t.length===0?c("tr",{children:c("td",{colSpan:e.length,className:"px-6 py-8 text-center text-gray-500",children:a})}):t.map((w,N)=>c("tr",{className:ye("hover:bg-gray-50 transition-colors",u&&"cursor-pointer"),onClick:()=>u==null?void 0:u(w,N),children:e.map(A=>c("td",{className:ye("px-6 py-4 whitespace-nowrap text-sm text-gray-900",A.align==="center"&&"text-center",A.align==="right"&&"text-right"),children:x(w,A)},A.key))},d(w,N)))})]})}),n&&c("div",{className:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6",children:E("div",{className:"flex items-center justify-between",children:[E("div",{className:"flex-1 flex justify-between sm:hidden",children:[c("button",{disabled:n.current<=1,onClick:()=>n.onChange(n.current-1,n.pageSize),className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"\u4E0A\u4E00\u9875"}),c("button",{disabled:n.current*n.pageSize>=n.total,onClick:()=>n.onChange(n.current+1,n.pageSize),className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"\u4E0B\u4E00\u9875"})]}),E("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[c("div",{children:E("p",{className:"text-sm text-gray-700",children:["\u663E\u793A\u7B2C"," ",c("span",{className:"font-medium",children:(n.current-1)*n.pageSize+1})," ","\u5230"," ",c("span",{className:"font-medium",children:Math.min(n.current*n.pageSize,n.total)})," ","\u6761\uFF0C\u5171"," ",c("span",{className:"font-medium",children:n.total})," \u6761\u8BB0\u5F55"]})}),c("div",{children:E("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px",children:[c("button",{disabled:n.current<=1,onClick:()=>n.onChange(n.current-1,n.pageSize),className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"\u4E0A\u4E00\u9875"}),c("button",{disabled:n.current*n.pageSize>=n.total,onClick:()=>n.onChange(n.current+1,n.pageSize),className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"\u4E0B\u4E00\u9875"})]})})]})]})})]})}function Y1(e){const{filename:t="export",columns:r,data:n}=e;if(!n||n.length===0){alert("\u6CA1\u6709\u6570\u636E\u53EF\u5BFC\u51FA");return}const l=r||Object.keys(n[0]).map(d=>({key:d,title:d})),o=l.map(d=>`"${d.title}"`).join(","),i=n.map(d=>l.map(y=>{let p=d[y.key];return y.render?p=y.render(p,d):p==null?p="":typeof p=="object"?p=JSON.stringify(p):typeof p=="boolean"?p=p?"\u662F":"\u5426":typeof p=="number"&&p>1e9&&(p=new Date(p*1e3).toLocaleString()),`"${String(p).replace(/"/g,'""')}"`}).join(",")),u=[o,...i].join(`
`),a="\uFEFF",s=new Blob([a+u],{type:"text/csv;charset=utf-8;"});t0(s,`${t}.csv`)}function G1(e){const{filename:t="export",columns:r,data:n}=e;if(!n||n.length===0){alert("\u6CA1\u6709\u6570\u636E\u53EF\u5BFC\u51FA");return}let l=n;r&&(l=n.map(u=>{const a={};return r.forEach(s=>{let d=u[s.key];s.render&&(d=s.render(d,u)),a[s.title]=d}),a}));const o=JSON.stringify(l,null,2),i=new Blob([o],{type:"application/json;charset=utf-8;"});t0(i,`${t}.json`)}function t0(e,t){const r=window.URL.createObjectURL(e),n=document.createElement("a");n.href=r,n.download=t,n.style.display="none",document.body.appendChild(n),n.click(),document.body.removeChild(n),window.URL.revokeObjectURL(r)}function Be(e){return!e||e===0?"-":new Date(e*1e3).toLocaleString()}function r0(e){return e?"\u662F":"\u5426"}function vn(e){return e?typeof e=="object"?JSON.stringify(e):String(e):"-"}const kr=({data:e,columns:t,filename:r="export",disabled:n=!1,className:l=""})=>{const[o,i]=C.exports.useState(!1),u=a=>{const s={data:e,columns:t,filename:`${r}_${new Date().toISOString().split("T")[0]}`};a==="csv"?Y1(s):G1(s),i(!1)};return n||!e||e.length===0?E("button",{disabled:!0,className:`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-400 bg-gray-100 cursor-not-allowed ${l}`,children:[c(iu,{className:"h-4 w-4 mr-2"}),"\u5BFC\u51FA\u6570\u636E"]}):E("div",{className:"relative",children:[E("button",{onClick:()=>i(!o),className:`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${l}`,children:[c(iu,{className:"h-4 w-4 mr-2"}),"\u5BFC\u51FA\u6570\u636E",c("svg",{className:"ml-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:c("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),o&&E(as,{children:[c("div",{className:"fixed inset-0 z-10",onClick:()=>i(!1)}),E("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-20",children:[E("div",{className:"py-1",children:[E("button",{onClick:()=>u("csv"),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900",children:[c(jv,{className:"h-4 w-4 mr-3 text-green-500"}),"\u5BFC\u51FA\u4E3A CSV",c("span",{className:"ml-auto text-xs text-gray-500",children:"Excel\u517C\u5BB9"})]}),E("button",{onClick:()=>u("json"),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900",children:[c(ri,{className:"h-4 w-4 mr-3 text-blue-500"}),"\u5BFC\u51FA\u4E3A JSON",c("span",{className:"ml-auto text-xs text-gray-500",children:"\u7ED3\u6784\u5316\u6570\u636E"})]})]}),c("div",{className:"border-t border-gray-100 px-4 py-2",children:E("div",{className:"text-xs text-gray-500",children:["\u5171 ",e.length," \u6761\u8BB0\u5F55"]})})]})]})]})},ys=({isOpen:e,onClose:t,title:r,data:n,fields:l,icon:o})=>{if(!e||!n)return null;const i=s=>{navigator.clipboard.writeText(s).then(()=>{console.log("\u5DF2\u590D\u5236\u5230\u526A\u8D34\u677F")})},u=(s,d)=>{if(d==null)return c("span",{className:"text-gray-400",children:"-"});if(s.render)return s.render(d,n);switch(s.type){case"boolean":return c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${d?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:d?"\u662F":"\u5426"});case"timestamp":return E("div",{className:"flex items-center",children:[c(ti,{className:"h-4 w-4 text-gray-400 mr-2"}),c("span",{children:d?new Date(d*1e3).toLocaleString():"-"})]});case"json":return c("pre",{className:"bg-gray-50 p-2 rounded text-xs overflow-x-auto max-w-md",children:JSON.stringify(d,null,2)});case"address":return E("div",{className:"flex items-center",children:[c(uu,{className:"h-4 w-4 text-blue-500 mr-2"}),c("span",{className:"font-mono text-sm",children:d})]});case"id":return E("div",{className:"flex items-center",children:[c(ni,{className:"h-4 w-4 text-gray-500 mr-2"}),c("span",{className:"font-mono text-sm",children:d})]});case"amount":return E("div",{className:"flex items-center",children:[c(ou,{className:"h-4 w-4 text-green-500 mr-2"}),c("span",{className:"font-medium",children:Number(d).toLocaleString()})]});case"number":return c("span",{className:"font-medium",children:Number(d).toLocaleString()});default:return c("span",{children:String(d)})}},a=s=>{switch(s.type){case"timestamp":return c(ti,{className:"h-4 w-4 text-gray-400"});case"address":return c(uu,{className:"h-4 w-4 text-blue-500"});case"id":return c(ni,{className:"h-4 w-4 text-gray-500"});case"amount":return c(ou,{className:"h-4 w-4 text-green-500"});case"json":return c(Dl,{className:"h-4 w-4 text-purple-500"});default:return c(Qv,{className:"h-4 w-4 text-gray-400"})}};return E("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[c("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:t}),c("div",{className:"flex min-h-full items-center justify-center p-4",children:E("div",{className:"relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[E("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[E("div",{className:"flex items-center",children:[o&&c("div",{className:"mr-3",children:o}),c("h3",{className:"text-lg font-medium text-gray-900",children:r})]}),c("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:c(cu,{className:"h-6 w-6"})})]}),c("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:c("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:l.map(s=>{const d=n[s.key],y=u(s,d);return E("div",{className:"space-y-2",children:[E("div",{className:"flex items-center justify-between",children:[E("label",{className:"flex items-center text-sm font-medium text-gray-700",children:[a(s),c("span",{className:"ml-2",children:s.label})]}),E("div",{className:"flex space-x-1",children:[s.copyable&&d&&c("button",{onClick:()=>i(String(d)),className:"p-1 text-gray-400 hover:text-gray-600 transition-colors",title:"\u590D\u5236",children:c(Lv,{className:"h-4 w-4"})}),s.linkable&&d&&c("button",{onClick:()=>window.open(String(d),"_blank"),className:"p-1 text-gray-400 hover:text-gray-600 transition-colors",title:"\u6253\u5F00\u94FE\u63A5",children:c(Ov,{className:"h-4 w-4"})})]})]}),c("div",{className:"bg-gray-50 rounded-md p-3 min-h-[2.5rem] flex items-center",children:y})]},s.key)})})}),c("div",{className:"flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50",children:c("button",{onClick:t,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"\u5173\u95ED"})})]})})]})},hd=[{label:"10\u79D2",value:10},{label:"30\u79D2",value:30},{label:"1\u5206\u949F",value:60},{label:"5\u5206\u949F",value:300},{label:"10\u5206\u949F",value:600}],Ml=({onRefresh:e,loading:t=!1,lastRefresh:r,autoRefresh:n=!1,refreshInterval:l=30,onAutoRefreshChange:o,className:i})=>{var p;const[u,a]=C.exports.useState(!1),s=x=>{const w=new Date,N=Math.floor((w.getTime()-x.getTime())/1e3);return N<60?`${N}\u79D2\u524D`:N<3600?`${Math.floor(N/60)}\u5206\u949F\u524D`:`${Math.floor(N/3600)}\u5C0F\u65F6\u524D`},d=()=>{o&&o(!n,l)},y=x=>{o&&o(n,x),a(!1)};return E("div",{className:ye("flex items-center space-x-2",i),children:[E("button",{onClick:e,disabled:t,className:ye("flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors","bg-blue-50 text-blue-700 hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed"),title:"\u624B\u52A8\u5237\u65B0\u6570\u636E",children:[c(Il,{className:ye("h-4 w-4",t&&"animate-spin")}),c("span",{children:"\u5237\u65B0"})]}),o&&E("button",{onClick:d,className:ye("flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors",n?"bg-green-50 text-green-700 hover:bg-green-100":"bg-gray-50 text-gray-700 hover:bg-gray-100"),title:n?"\u505C\u6B62\u81EA\u52A8\u5237\u65B0":"\u5F00\u542F\u81EA\u52A8\u5237\u65B0",children:[n?c(Zv,{className:"h-4 w-4"}):c(t1,{className:"h-4 w-4"}),c("span",{children:n?"\u81EA\u52A8":"\u624B\u52A8"})]}),o&&E("div",{className:"relative",children:[E("button",{onClick:()=>a(!u),className:ye("flex items-center space-x-1 px-2 py-1.5 rounded-md text-sm text-gray-600 hover:bg-gray-100 transition-colors"),title:"\u8BBE\u7F6E\u5237\u65B0\u95F4\u9694",children:[c(fd,{className:"h-4 w-4"}),c("span",{children:(p=hd.find(x=>x.value===l))==null?void 0:p.label}),c(Zh,{className:"h-3 w-3"})]}),u&&c("div",{className:"absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]",children:c("div",{className:"py-1",children:hd.map(x=>c("button",{onClick:()=>y(x.value),className:ye("w-full text-left px-3 py-2 text-sm hover:bg-gray-100 transition-colors",l===x.value&&"bg-blue-50 text-blue-700"),children:x.label},x.value))})})]}),r&&E("div",{className:"flex items-center space-x-1 text-xs text-gray-500",children:[c(fd,{className:"h-3 w-3"}),E("span",{children:["\u66F4\u65B0\u4E8E ",s(r)]})]}),u&&c("div",{className:"fixed inset-0 z-0",onClick:()=>a(!1)})]})},J1=()=>{const[e,t]=C.exports.useState(""),[r,n]=C.exports.useState({}),[l,o]=C.exports.useState(null),[i,u]=C.exports.useState(!1),{data:a,total:s,loading:d,error:y,page:p,pageSize:x,params:w,refetch:N,updateParams:A,changePage:m,lastRefresh:h,autoRefresh:f,refreshInterval:k,setAutoRefresh:B}=M1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),g=[{key:"serviceID",title:"\u670D\u52A1ID",dataIndex:"serviceID",width:200,sortable:!0,render:L=>c("span",{className:"font-mono text-sm text-blue-600",children:L})},{key:"address",title:"\u7528\u6237\u5730\u5740",dataIndex:"address",width:180,render:L=>c("span",{className:"font-mono text-xs text-gray-600",children:L?`${L.slice(0,8)}...${L.slice(-6)}`:"-"})},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546",dataIndex:"provider",width:120,sortable:!0},{key:"status",title:"\u72B6\u6001",dataIndex:"status",width:100,render:L=>c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${{active:"bg-green-100 text-green-800",inactive:"bg-gray-100 text-gray-800",pending:"bg-yellow-100 text-yellow-800",error:"bg-red-100 text-red-800"}[L]||"bg-gray-100 text-gray-800"}`,children:L})},{key:"serviceActivated",title:"\u670D\u52A1\u6FC0\u6D3B",dataIndex:"serviceActivated",width:100,align:"center",render:L=>c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${L?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:L?"\u5DF2\u6FC0\u6D3B":"\u672A\u6FC0\u6D3B"})},{key:"amount",title:"\u91D1\u989D",dataIndex:"amount",width:120,align:"right",sortable:!0,render:L=>c("span",{className:"font-medium",children:(L==null?void 0:L.toLocaleString())||0})},{key:"duration",title:"\u6301\u7EED\u65F6\u95F4",dataIndex:"duration",width:100,align:"right",render:L=>c("span",{children:L?`${L}\u5929`:"-"})},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:L=>c("span",{className:"text-sm text-gray-600",children:L?new Date(L*1e3).toLocaleString():"-"})}],S=(L,Z)=>{A({sortBy:L,sortDesc:Z==="desc",offset:0})},b=(L,Z)=>{m(L,Z)},z=L=>{o(L),u(!0)},$=()=>{u(!1),o(null)},G=[{key:"_id",label:"ID",type:"id",copyable:!0},{key:"serviceID",label:"\u670D\u52A1ID",type:"id",copyable:!0},{key:"address",label:"\u7528\u6237\u5730\u5740",type:"address",copyable:!0},{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text"},{key:"providerAddress",label:"\u63D0\u4F9B\u5546\u5730\u5740",type:"address",copyable:!0},{key:"status",label:"\u72B6\u6001",type:"text"},{key:"serviceActivated",label:"\u670D\u52A1\u5DF2\u6FC0\u6D3B",type:"boolean"},{key:"duration",label:"\u6301\u7EED\u65F6\u95F4\uFF08\u79D2\uFF09",type:"number"},{key:"amount",label:"\u91D1\u989D",type:"amount"},{key:"service",label:"\u670D\u52A1\u540D\u79F0",type:"text"},{key:"createdAt",label:"\u521B\u5EFA\u65F6\u95F4",type:"timestamp"},{key:"updatedAt",label:"\u66F4\u65B0\u65F6\u95F4",type:"timestamp"},{key:"endAt",label:"\u7ED3\u675F\u65F6\u95F4",type:"timestamp"},{key:"deletedAt",label:"\u5220\u9664\u65F6\u95F4",type:"timestamp"}],ge=()=>{const L={...r,offset:0};e.trim()&&(e.startsWith("0x")||e.length===42?L.address=e:L.serviceID=e),A(L)},we=()=>{N()},R=()=>{t(""),n({}),A({offset:0,limit:20,sortBy:"createdAt",sortDesc:!0})};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[c("h1",{className:"text-2xl font-bold text-gray-900",children:"\u7528\u6237\u670D\u52A1"}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u548C\u67E5\u770B\u7528\u6237\u670D\u52A1\u8BB0\u5F55"})]}),E("div",{className:"flex items-center space-x-3",children:[c(Ml,{onRefresh:we,loading:d,lastRefresh:h,autoRefresh:f,refreshInterval:k,onAutoRefreshChange:B}),c(kr,{data:a,columns:[{key:"_id",title:"ID"},{key:"serviceID",title:"\u670D\u52A1ID"},{key:"address",title:"\u7528\u6237\u5730\u5740"},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546"},{key:"providerAddress",title:"\u63D0\u4F9B\u5546\u5730\u5740"},{key:"status",title:"\u72B6\u6001"},{key:"serviceActivated",title:"\u670D\u52A1\u5DF2\u6FC0\u6D3B",render:L=>r0(L)},{key:"duration",title:"\u6301\u7EED\u65F6\u95F4\uFF08\u79D2\uFF09"},{key:"amount",title:"\u91D1\u989D"},{key:"service",title:"\u670D\u52A1\u540D\u79F0"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:L=>Be(L)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:L=>Be(L)},{key:"endAt",title:"\u7ED3\u675F\u65F6\u95F4",render:L=>Be(L)}],filename:"user_services",disabled:d||a.length===0}),E("button",{className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:[c(n1,{className:"h-4 w-4 mr-2"}),"\u65B0\u5EFA"]})]})]}),c("div",{className:"bg-white rounded-lg shadow p-6",children:E("div",{className:"flex items-center space-x-4",children:[c("div",{className:"flex-1",children:E("div",{className:"relative",children:[c(li,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),c("input",{type:"text",placeholder:"\u641C\u7D22\u670D\u52A1ID\u3001\u7528\u6237\u5730\u5740...",value:e,onChange:L=>t(L.target.value),onKeyPress:L=>L.key==="Enter"&&ge(),className:"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]})}),E("button",{onClick:ge,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:[c(li,{className:"h-4 w-4 mr-2"}),"\u641C\u7D22"]}),E("button",{onClick:R,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[c(Ht,{className:"h-4 w-4 mr-2"}),"\u6E05\u9664\u7B5B\u9009"]})]})}),y&&c("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:c("div",{className:"flex",children:E("div",{className:"ml-3",children:[c("h3",{className:"text-sm font-medium text-red-800",children:"\u52A0\u8F7D\u6570\u636E\u65F6\u51FA\u9519"}),c("div",{className:"mt-2 text-sm text-red-700",children:c("p",{children:y})}),c("div",{className:"mt-4",children:c("button",{onClick:we,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"\u91CD\u8BD5"})})]})})}),c(wr,{columns:g,data:a,loading:d,pagination:{current:p,pageSize:x,total:s,onChange:b},sortConfig:{key:w.sortBy||"createdAt",direction:w.sortDesc?"desc":"asc"},onSort:S,rowKey:"_id",onRowClick:z,emptyText:y?"\u52A0\u8F7D\u5931\u8D25":"\u6682\u65E0\u6570\u636E"}),c(ys,{isOpen:i,onClose:$,title:"\u7528\u6237\u670D\u52A1\u8BE6\u60C5",data:l,fields:G,icon:c(s1,{className:"h-6 w-6 text-blue-600"})})]})},Vr=({fields:e,onSubmit:t,onReset:r,loading:n=!1,className:l,showAdvanced:o=!1,initialValues:i={}})=>{const[u,a]=C.exports.useState(()=>{const k={};return e.forEach(B=>{B.defaultValue!==void 0&&(k[B.key]=B.defaultValue)}),{...k,...i}}),[s,d]=C.exports.useState({}),[y,p]=C.exports.useState(o),x=(k,B)=>{a(g=>({...g,[k]:B})),s[k]&&d(g=>{const S={...g};return delete S[k],S})},w=()=>{const k={};return e.forEach(B=>{if(B.validation){const g=B.validation(u[B.key]);g&&(k[B.key]=g)}}),d(k),Object.keys(k).length===0},N=k=>{if(k.preventDefault(),w()){const B=Object.entries(u).reduce((g,[S,b])=>(b!=null&&b!==""&&(g[S]=b),g),{});t(B)}},A=()=>{const k={};e.forEach(B=>{B.defaultValue!==void 0&&(k[B.key]=B.defaultValue)}),a(k),d({}),r==null||r()},m=k=>{var $;const B=u[k.key]||"",g=s[k.key],S=!!g,b=ye("block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 sm:text-sm",S?"border-red-300 focus:border-red-500 focus:ring-red-500":"border-gray-300 focus:border-blue-500 focus:ring-blue-500"),z=()=>{switch(k.type){case"text":return k.key.includes("address")?c(uu,{className:"h-4 w-4"}):k.key.includes("provider")?c(ei,{className:"h-4 w-4"}):c(ni,{className:"h-4 w-4"});case"date":case"dateRange":return c(ti,{className:"h-4 w-4"});case"number":return c(ni,{className:"h-4 w-4"});default:return c(li,{className:"h-4 w-4"})}};switch(k.type){case"text":case"number":return E("div",{className:"relative",children:[c("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:k.label}),E("div",{className:"relative",children:[c("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400",children:z()}),c("input",{type:k.type,value:B,onChange:G=>x(k.key,G.target.value),placeholder:k.placeholder,className:ye(b,"pl-10")})]}),S&&c("p",{className:"mt-1 text-sm text-red-600",children:g})]},k.key);case"select":return E("div",{children:[c("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:k.label}),E("select",{value:B,onChange:G=>x(k.key,G.target.value),className:b,children:[c("option",{value:"",children:"\u8BF7\u9009\u62E9..."}),($=k.options)==null?void 0:$.map(G=>c("option",{value:String(G.value),children:G.label},String(G.value)))]}),S&&c("p",{className:"mt-1 text-sm text-red-600",children:g})]},k.key);case"boolean":return E("div",{children:[E("label",{className:"flex items-center",children:[c("input",{type:"checkbox",checked:B||!1,onChange:G=>x(k.key,G.target.checked),className:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"}),c("span",{className:"ml-2 text-sm font-medium text-gray-700",children:k.label})]}),S&&c("p",{className:"mt-1 text-sm text-red-600",children:g})]},k.key);case"date":return E("div",{children:[c("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:k.label}),E("div",{className:"relative",children:[c("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400",children:c(ti,{className:"h-4 w-4"})}),c("input",{type:"date",value:B,onChange:G=>x(k.key,G.target.value),className:ye(b,"pl-10")})]}),S&&c("p",{className:"mt-1 text-sm text-red-600",children:g})]},k.key);default:return null}},h=e.slice(0,3),f=e.slice(3);return c("div",{className:ye("bg-white rounded-lg shadow p-6",l),children:E("form",{onSubmit:N,className:"space-y-4",children:[c("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:h.map(m)}),f.length>0&&E(as,{children:[c("div",{className:"flex items-center justify-between pt-2",children:E("button",{type:"button",onClick:()=>p(!y),className:"flex items-center text-sm text-blue-600 hover:text-blue-800",children:[c(Ht,{className:"h-4 w-4 mr-1"}),"\u9AD8\u7EA7\u7B5B\u9009",y?c(cu,{className:"h-4 w-4 ml-1"}):E("span",{className:"ml-1",children:["(",f.length,")"]})]})}),y&&c("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t border-gray-200",children:f.map(m)})]}),E("div",{className:"flex items-center justify-between pt-4 border-t border-gray-200",children:[E("button",{type:"button",onClick:A,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[c(cu,{className:"h-4 w-4 mr-2"}),"\u91CD\u7F6E"]}),E("button",{type:"submit",disabled:n,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:[c(li,{className:ye("h-4 w-4 mr-2",n&&"animate-spin")}),n?"\u67E5\u8BE2\u4E2D...":"\u67E5\u8BE2"]})]})]})})},X1=()=>{const[e,t]=C.exports.useState(!1),[r,n]=C.exports.useState(null),[l,o]=C.exports.useState(!1),{data:i,total:u,loading:a,error:s,page:d,pageSize:y,params:p,refetch:x,updateParams:w,changePage:N,lastRefresh:A,autoRefresh:m,refreshInterval:h,setAutoRefresh:f}=T1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),k=[{key:"type",title:"\u8BA2\u5355\u7C7B\u578B",dataIndex:"type",width:120,sortable:!0,render:R=>c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${R==="purchase"?"bg-green-100 text-green-800":R==="refund"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:R==="purchase"?"\u8D2D\u4E70":R==="refund"?"\u9000\u6B3E":R})},{key:"address",title:"\u7528\u6237\u5730\u5740",dataIndex:"address",width:180,render:R=>c("span",{className:"font-mono text-xs text-gray-600",children:R?`${R.slice(0,8)}...${R.slice(-6)}`:"-"})},{key:"recipient",title:"\u63A5\u6536\u5730\u5740",dataIndex:"recipient",width:180,render:R=>c("span",{className:"font-mono text-xs text-gray-600",children:R?`${R.slice(0,8)}...${R.slice(-6)}`:"-"})},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546",dataIndex:"provider",width:120,sortable:!0},{key:"status",title:"\u8BA2\u5355\u72B6\u6001",dataIndex:"status",width:100,render:R=>{const L={pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800",refunded:"bg-purple-100 text-purple-800"},Z={pending:"\u5F85\u652F\u4ED8",paid:"\u5DF2\u652F\u4ED8",completed:"\u5DF2\u5B8C\u6210",cancelled:"\u5DF2\u53D6\u6D88",refunded:"\u5DF2\u9000\u6B3E"};return c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${L[R]||"bg-gray-100 text-gray-800"}`,children:Z[R]||R})}},{key:"amount",title:"\u8BA2\u5355\u91D1\u989D",dataIndex:"amount",width:120,align:"right",sortable:!0,render:R=>c("span",{className:"font-medium",children:(R==null?void 0:R.toLocaleString())||0})},{key:"amountPaid",title:"\u5DF2\u652F\u4ED8\u91D1\u989D",dataIndex:"amountPaid",width:120,align:"right",render:R=>c("span",{className:"font-medium text-green-600",children:(R==null?void 0:R.toLocaleString())||0})},{key:"userServiceIDs",title:"\u5173\u8054\u670D\u52A1",dataIndex:"userServiceIDs",width:100,align:"center",render:R=>E("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:[(R==null?void 0:R.length)||0," \u4E2A"]})},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:R=>c("span",{className:"text-sm text-gray-600",children:R?new Date(R*1e3).toLocaleString():"-"})},{key:"paidTS",title:"\u652F\u4ED8\u65F6\u95F4",dataIndex:"paidTS",width:160,render:R=>c("span",{className:"text-sm text-gray-600",children:R?new Date(R*1e3).toLocaleString():"-"})}],B=[{key:"address",label:"\u7528\u6237\u5730\u5740",type:"text",placeholder:"\u8F93\u5165\u7528\u6237\u5730\u5740..."},{key:"recipient",label:"\u63A5\u6536\u5730\u5740",type:"text",placeholder:"\u8F93\u5165\u63A5\u6536\u5730\u5740..."},{key:"type",label:"\u8BA2\u5355\u7C7B\u578B",type:"select",options:[{label:"\u8D2D\u4E70",value:"purchase"},{label:"\u9000\u6B3E",value:"refund"}]},{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u63D0\u4F9B\u5546..."},{key:"status",label:"\u8BA2\u5355\u72B6\u6001",type:"select",options:[{label:"\u5F85\u652F\u4ED8",value:"pending"},{label:"\u5DF2\u652F\u4ED8",value:"paid"},{label:"\u5DF2\u5B8C\u6210",value:"completed"},{label:"\u5DF2\u53D6\u6D88",value:"cancelled"},{label:"\u5DF2\u9000\u6B3E",value:"refunded"}]},{key:"minAmount",label:"\u6700\u5C0F\u91D1\u989D",type:"number",placeholder:"\u8F93\u5165\u6700\u5C0F\u91D1\u989D..."},{key:"maxAmount",label:"\u6700\u5927\u91D1\u989D",type:"number",placeholder:"\u8F93\u5165\u6700\u5927\u91D1\u989D..."}],g=(R,L)=>{w({sortBy:R,sortDesc:L==="desc",offset:0})},S=(R,L)=>{N(R,L)},b=R=>{n(R),o(!0)},z=()=>{o(!1),n(null)},$=[{key:"_id",label:"ID",type:"id",copyable:!0},{key:"type",label:"\u8BA2\u5355\u7C7B\u578B",type:"text"},{key:"address",label:"\u7528\u6237\u5730\u5740",type:"address",copyable:!0},{key:"recipient",label:"\u63A5\u6536\u5730\u5740",type:"address",copyable:!0},{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text"},{key:"status",label:"\u8BA2\u5355\u72B6\u6001",type:"text"},{key:"amount",label:"\u8BA2\u5355\u91D1\u989D",type:"amount"},{key:"amountPaid",label:"\u5DF2\u652F\u4ED8\u91D1\u989D",type:"amount"},{key:"createdAt",label:"\u521B\u5EFA\u65F6\u95F4",type:"timestamp"},{key:"updatedAt",label:"\u66F4\u65B0\u65F6\u95F4",type:"timestamp"},{key:"paidTS",label:"\u652F\u4ED8\u65F6\u95F4",type:"timestamp"},{key:"filedTS",label:"\u63D0\u4EA4\u65F6\u95F4",type:"timestamp"},{key:"deletedAt",label:"\u5220\u9664\u65F6\u95F4",type:"timestamp"}],G=R=>{const L={offset:0,...R};R.minAmount&&(L.tsStart=R.minAmount),R.maxAmount&&(L.tsEnd=R.maxAmount),w(L),t(!1)},ge=()=>{x()},we=()=>{w({offset:0,limit:20,sortBy:"createdAt",sortDesc:!0}),t(!1)};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[c("h1",{className:"text-2xl font-bold text-gray-900",children:"\u8BA2\u5355\u7BA1\u7406"}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u548C\u67E5\u770B\u8BA2\u5355\u4FE1\u606F"})]}),E("div",{className:"flex items-center space-x-3",children:[c(Ml,{onRefresh:ge,loading:a,lastRefresh:A,autoRefresh:m,refreshInterval:h,onAutoRefreshChange:f}),E("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[c(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u9AD8\u7EA7\u7B5B\u9009"]}),c(kr,{data:i,columns:[{key:"_id",title:"ID"},{key:"type",title:"\u8BA2\u5355\u7C7B\u578B"},{key:"address",title:"\u7528\u6237\u5730\u5740"},{key:"recipient",title:"\u63A5\u6536\u5730\u5740"},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546"},{key:"status",title:"\u8BA2\u5355\u72B6\u6001"},{key:"amount",title:"\u8BA2\u5355\u91D1\u989D"},{key:"amountPaid",title:"\u5DF2\u652F\u4ED8\u91D1\u989D"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:R=>Be(R)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:R=>Be(R)},{key:"paidTS",title:"\u652F\u4ED8\u65F6\u95F4",render:R=>Be(R)},{key:"filedTS",title:"\u63D0\u4EA4\u65F6\u95F4",render:R=>Be(R)}],filename:"orders",disabled:a||i.length===0})]})]}),e&&c(Vr,{fields:B,onSubmit:G,onReset:we,loading:a,showAdvanced:!0}),s&&c("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:c("div",{className:"flex",children:E("div",{className:"ml-3",children:[c("h3",{className:"text-sm font-medium text-red-800",children:"\u52A0\u8F7D\u6570\u636E\u65F6\u51FA\u9519"}),c("div",{className:"mt-2 text-sm text-red-700",children:c("p",{children:s})}),c("div",{className:"mt-4",children:c("button",{onClick:ge,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"\u91CD\u8BD5"})})]})})}),c(wr,{columns:k,data:i,loading:a,pagination:{current:d,pageSize:y,total:u,onChange:S},sortConfig:{key:p.sortBy||"createdAt",direction:p.sortDesc?"desc":"asc"},onSort:g,rowKey:"_id",onRowClick:b,emptyText:s?"\u52A0\u8F7D\u5931\u8D25":"\u6682\u65E0\u8BA2\u5355\u6570\u636E"}),c(ys,{isOpen:l,onClose:z,title:"\u8BA2\u5355\u8BE6\u60C5",data:r,fields:$,icon:c(oi,{className:"h-6 w-6 text-blue-600"})})]})},q1=()=>{const[e,t]=C.exports.useState(!1),[r,n]=C.exports.useState(null),[l,o]=C.exports.useState(!1),{data:i,total:u,loading:a,error:s,page:d,pageSize:y,params:p,refetch:x,updateParams:w,changePage:N,lastRefresh:A,autoRefresh:m,refreshInterval:h,setAutoRefresh:f}=z1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),k=[{key:"_id",title:"ID",dataIndex:"_id",width:120,render:R=>E("span",{className:"font-mono text-xs text-gray-600",children:[R.substring(0,8),"..."]})},{key:"orderID",title:"\u8BA2\u5355ID",dataIndex:"orderID",width:150,sortable:!0,render:R=>E("span",{className:"font-mono text-xs text-blue-600",children:[R.substring(0,12),"..."]})},{key:"userServiceID",title:"\u7528\u6237\u670D\u52A1ID",dataIndex:"userServiceID",width:150,sortable:!0,render:R=>E("span",{className:"font-mono text-xs text-green-600",children:[R.substring(0,12),"..."]})},{key:"orderStatus",title:"\u8BA2\u5355\u72B6\u6001",dataIndex:"orderStatus",width:120,sortable:!0,render:R=>c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${R==="paid"?"bg-green-100 text-green-800":R==="pending"?"bg-yellow-100 text-yellow-800":R==="cancelled"?"bg-red-100 text-red-800":R==="completed"?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:R==="paid"?"\u5DF2\u652F\u4ED8":R==="pending"?"\u5F85\u652F\u4ED8":R==="cancelled"?"\u5DF2\u53D6\u6D88":R==="completed"?"\u5DF2\u5B8C\u6210":R})},{key:"orderType",title:"\u8BA2\u5355\u7C7B\u578B",dataIndex:"orderType",width:120,sortable:!0,render:R=>c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${R==="purchase"?"bg-green-100 text-green-800":R==="refund"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:R==="purchase"?"\u8D2D\u4E70":R==="refund"?"\u9000\u6B3E":R})},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:R=>c("span",{className:"text-sm text-gray-600",children:new Date(R*1e3).toLocaleString()})},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",dataIndex:"updatedAt",width:160,sortable:!0,render:R=>c("span",{className:"text-sm text-gray-600",children:new Date(R*1e3).toLocaleString()})}],B=[{key:"orderID",label:"\u8BA2\u5355ID",type:"text",placeholder:"\u8F93\u5165\u8BA2\u5355ID..."},{key:"userServiceID",label:"\u7528\u6237\u670D\u52A1ID",type:"text",placeholder:"\u8F93\u5165\u7528\u6237\u670D\u52A1ID..."},{key:"orderStatus",label:"\u8BA2\u5355\u72B6\u6001",type:"select",options:[{label:"\u5F85\u652F\u4ED8",value:"pending"},{label:"\u5DF2\u652F\u4ED8",value:"paid"},{label:"\u5DF2\u5B8C\u6210",value:"completed"},{label:"\u5DF2\u53D6\u6D88",value:"cancelled"},{label:"\u5DF2\u9000\u6B3E",value:"refunded"}]},{key:"orderType",label:"\u8BA2\u5355\u7C7B\u578B",type:"select",options:[{label:"\u8D2D\u4E70",value:"purchase"},{label:"\u9000\u6B3E",value:"refund"}]}],g=(R,L)=>{w({sortBy:R,sortDesc:L==="desc",offset:0})},S=(R,L)=>{N(R,L)},b=R=>{n(R),o(!0)},z=()=>{o(!1),n(null)},$=[{key:"_id",label:"ID",type:"id",copyable:!0},{key:"orderID",label:"\u8BA2\u5355ID",type:"id",copyable:!0},{key:"userServiceID",label:"\u7528\u6237\u670D\u52A1ID",type:"id",copyable:!0},{key:"orderStatus",label:"\u8BA2\u5355\u72B6\u6001",type:"text"},{key:"orderType",label:"\u8BA2\u5355\u7C7B\u578B",type:"text"},{key:"createdAt",label:"\u521B\u5EFA\u65F6\u95F4",type:"timestamp"},{key:"updatedAt",label:"\u66F4\u65B0\u65F6\u95F4",type:"timestamp"},{key:"deletedAt",label:"\u5220\u9664\u65F6\u95F4",type:"timestamp"}],G=R=>{const L={offset:0,...R};w(L),t(!1)},ge=()=>{x()},we=()=>{w({orderID:void 0,userServiceID:void 0,orderStatus:void 0,orderType:void 0,offset:0}),t(!1)};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[E("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[c(ua,{className:"h-6 w-6 mr-2 text-blue-600"}),"\u8BA2\u5355\u670D\u52A1\u5173\u8054"]}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u8BA2\u5355\u4E0E\u670D\u52A1\u7684\u5173\u8054\u5173\u7CFB"})]}),E("div",{className:"flex items-center space-x-3",children:[c(Ml,{onRefresh:ge,loading:a,lastRefresh:A,autoRefresh:m,refreshInterval:h,onAutoRefreshChange:f}),E("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[c(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u663E\u793A\u7B5B\u9009"]}),c(kr,{data:i,columns:[{key:"_id",title:"ID"},{key:"orderID",title:"\u8BA2\u5355ID"},{key:"userServiceID",title:"\u7528\u6237\u670D\u52A1ID"},{key:"orderStatus",title:"\u8BA2\u5355\u72B6\u6001"},{key:"orderType",title:"\u8BA2\u5355\u7C7B\u578B"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:R=>Be(R)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:R=>Be(R)}],filename:"order_services",disabled:a||i.length===0})]})]}),e&&c("div",{className:"bg-white rounded-lg shadow p-6",children:c(Vr,{fields:B,onSubmit:G,onReset:we,loading:a,initialValues:p})}),c("div",{className:"bg-white rounded-lg shadow p-6",children:c("div",{className:"flex items-center justify-between",children:E("div",{className:"flex items-center space-x-4",children:[E("div",{className:"flex items-center",children:[c(ua,{className:"h-5 w-5 text-blue-500 mr-2"}),E("span",{className:"text-sm font-medium text-gray-900",children:["\u603B\u5173\u8054\u6570: ",u.toLocaleString()]})]}),p.orderID&&E("div",{className:"text-sm text-gray-600",children:["\u8BA2\u5355ID: ",p.orderID]}),p.userServiceID&&E("div",{className:"text-sm text-gray-600",children:["\u670D\u52A1ID: ",p.userServiceID]}),p.orderStatus&&E("div",{className:"text-sm text-gray-600",children:["\u72B6\u6001: ",p.orderStatus]})]})})}),c("div",{className:"bg-white rounded-lg shadow",children:c(wr,{columns:k,data:i,loading:a,error:s||void 0,pagination:{current:d,pageSize:y,total:u,onChange:S,showSizeChanger:!0,showQuickJumper:!0,showTotal:(R,L)=>`\u663E\u793A ${L[0]}-${L[1]} \u6761\uFF0C\u5171 ${R} \u6761\u8BB0\u5F55`},onSort:g,sortBy:p.sortBy,sortDesc:p.sortDesc,onRowClick:b})}),c(ys,{isOpen:l,onClose:z,title:"\u8BA2\u5355\u670D\u52A1\u5173\u8054\u8BE6\u60C5",data:r,fields:$,icon:c(ua,{className:"h-6 w-6 text-blue-600"})})]})},Z1=()=>{const[e,t]=C.exports.useState(!1),{data:r,total:n,loading:l,error:o,page:i,pageSize:u,params:a,refetch:s,updateParams:d,changePage:y,lastRefresh:p,autoRefresh:x,refreshInterval:w,setAutoRefresh:N}=O1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),A=[{key:"version",title:"\u7248\u672C\u53F7",dataIndex:"version",width:150,sortable:!0,render:S=>E("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-sm font-mono bg-blue-100 text-blue-800",children:[c(h1,{className:"h-3 w-3 mr-1"}),S]})},{key:"minimalSupported",title:"\u6700\u4F4E\u652F\u6301\u7248\u672C",dataIndex:"minimalSupported",width:150,render:S=>c("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-sm font-mono bg-gray-100 text-gray-800",children:S||"-"})},{key:"changeLog",title:"\u66F4\u65B0\u65E5\u5FD7",dataIndex:"changeLog",render:S=>c("div",{className:"max-w-md",children:c("p",{className:"text-sm text-gray-900 line-clamp-3",children:S||"\u6682\u65E0\u66F4\u65B0\u65E5\u5FD7"})})},{key:"createdAt",title:"\u53D1\u5E03\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:S=>c("span",{className:"text-sm text-gray-600",children:S?new Date(S*1e3).toLocaleString():"-"})},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",dataIndex:"updatedAt",width:160,render:S=>c("span",{className:"text-sm text-gray-600",children:S?new Date(S*1e3).toLocaleString():"-"})}],m=[{key:"version",label:"\u7248\u672C\u53F7",type:"text",placeholder:"\u8F93\u5165\u7248\u672C\u53F7..."},{key:"minimalSupported",label:"\u6700\u4F4E\u652F\u6301\u7248\u672C",type:"text",placeholder:"\u8F93\u5165\u6700\u4F4E\u652F\u6301\u7248\u672C..."}],h=(S,b)=>{d({sortBy:S,sortDesc:b==="desc",offset:0})},f=(S,b)=>{y(S,b)},k=S=>{d({offset:0,...S}),t(!1)},B=()=>{s()},g=()=>{d({offset:0,limit:20,sortBy:"createdAt",sortDesc:!0}),t(!1)};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[c("h1",{className:"text-2xl font-bold text-gray-900",children:"CLI\u7248\u672C\u7BA1\u7406"}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406CLI\u7248\u672C\u4FE1\u606F\u548C\u66F4\u65B0\u65E5\u5FD7"})]}),E("div",{className:"flex items-center space-x-3",children:[c(Ml,{onRefresh:B,loading:l,lastRefresh:p,autoRefresh:x,refreshInterval:w,onAutoRefreshChange:N}),E("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[c(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u7B5B\u9009"]}),c(kr,{data:r,columns:[{key:"_id",title:"ID"},{key:"version",title:"\u7248\u672C\u53F7"},{key:"minimalSupported",title:"\u6700\u4F4E\u652F\u6301\u7248\u672C"},{key:"changeLog",title:"\u66F4\u65B0\u65E5\u5FD7"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:S=>Be(S)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:S=>Be(S)}],filename:"cli_versions",disabled:l||r.length===0})]})]}),e&&c(Vr,{fields:m,onSubmit:k,onReset:g,loading:l}),o&&c("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:c("div",{className:"flex",children:E("div",{className:"ml-3",children:[c("h3",{className:"text-sm font-medium text-red-800",children:"\u52A0\u8F7D\u6570\u636E\u65F6\u51FA\u9519"}),c("div",{className:"mt-2 text-sm text-red-700",children:c("p",{children:o})}),c("div",{className:"mt-4",children:c("button",{onClick:B,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"\u91CD\u8BD5"})})]})})}),c(wr,{columns:A,data:r,loading:l,pagination:{current:i,pageSize:u,total:n,onChange:f},sortConfig:{key:a.sortBy||"createdAt",direction:a.sortDesc?"desc":"asc"},onSort:h,rowKey:"_id",onRowClick:S=>{console.log("\u70B9\u51FBCLI\u7248\u672C:",S)},emptyText:o?"\u52A0\u8F7D\u5931\u8D25":"\u6682\u65E0CLI\u7248\u672C\u6570\u636E"})]})},ex=()=>{const[e,t]=C.exports.useState(!1),{data:r,total:n,loading:l,error:o,page:i,pageSize:u,params:a,refetch:s,updateParams:d,changePage:y,lastRefresh:p,autoRefresh:x,refreshInterval:w,setAutoRefresh:N}=$1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),A=[{key:"nameOrId",title:"\u8D27\u5E01\u540D\u79F0/ID",dataIndex:"nameOrId",width:150,sortable:!0,render:S=>E("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-sm font-medium bg-green-100 text-green-800",children:[c(Pv,{className:"h-3 w-3 mr-1"}),S]})},{key:"symbolName",title:"\u7B26\u53F7\u540D\u79F0",dataIndex:"symbolName",width:120,render:S=>c("span",{className:"font-mono text-sm font-bold text-blue-600",children:S||"-"})},{key:"contractId",title:"\u5408\u7EA6ID",dataIndex:"contractId",width:200,render:S=>c("span",{className:"font-mono text-xs text-gray-600",children:S?`${S.slice(0,10)}...${S.slice(-8)}`:"-"})},{key:"contractType",title:"\u5408\u7EA6\u7C7B\u578B",dataIndex:"contractType",width:120,render:S=>c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${S==="token"?"bg-blue-100 text-blue-800":S==="nft"?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"}`,children:S||"-"})},{key:"unit",title:"\u5355\u4F4D",dataIndex:"unit",width:100,align:"right",render:S=>c("span",{className:"font-medium",children:S||0})},{key:"exchangeRate",title:"\u6C47\u7387",dataIndex:"exchangeRate",width:120,align:"right",render:S=>c("span",{className:"font-medium text-green-600",children:S?S.toFixed(6):"0.000000"})},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:S=>c("span",{className:"text-sm text-gray-600",children:S?new Date(S*1e3).toLocaleString():"-"})}],m=[{key:"nameOrId",label:"\u8D27\u5E01\u540D\u79F0/ID",type:"text",placeholder:"\u8F93\u5165\u8D27\u5E01\u540D\u79F0\u6216ID..."},{key:"symbolName",label:"\u7B26\u53F7\u540D\u79F0",type:"text",placeholder:"\u8F93\u5165\u7B26\u53F7\u540D\u79F0..."},{key:"contractType",label:"\u5408\u7EA6\u7C7B\u578B",type:"select",options:[{label:"Token",value:"token"},{label:"NFT",value:"nft"}]},{key:"contractId",label:"\u5408\u7EA6ID",type:"text",placeholder:"\u8F93\u5165\u5408\u7EA6ID..."}],h=(S,b)=>{d({sortBy:S,sortDesc:b==="desc",offset:0})},f=(S,b)=>{y(S,b)},k=S=>{d({offset:0,...S}),t(!1)},B=()=>{s()},g=()=>{d({offset:0,limit:20,sortBy:"createdAt",sortDesc:!0}),t(!1)};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[c("h1",{className:"text-2xl font-bold text-gray-900",children:"\u8D27\u5E01\u7BA1\u7406"}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u8D27\u5E01\u4FE1\u606F\u548C\u6C47\u7387\u8BBE\u7F6E"})]}),E("div",{className:"flex items-center space-x-3",children:[c(Ml,{onRefresh:B,loading:l,lastRefresh:p,autoRefresh:x,refreshInterval:w,onAutoRefreshChange:N}),E("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[c(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u7B5B\u9009"]}),c(kr,{data:r,columns:[{key:"_id",title:"ID"},{key:"nameOrId",title:"\u8D27\u5E01\u540D\u79F0/ID"},{key:"contractId",title:"\u5408\u7EA6ID"},{key:"symbolName",title:"\u7B26\u53F7\u540D\u79F0"},{key:"contractType",title:"\u5408\u7EA6\u7C7B\u578B"},{key:"unit",title:"\u5355\u4F4D"},{key:"exchangeRate",title:"\u6C47\u7387"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:S=>Be(S)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:S=>Be(S)}],filename:"currencies",disabled:l||r.length===0})]})]}),e&&c(Vr,{fields:m,onSubmit:k,onReset:g,loading:l}),o&&c("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:c("div",{className:"flex",children:E("div",{className:"ml-3",children:[c("h3",{className:"text-sm font-medium text-red-800",children:"\u52A0\u8F7D\u6570\u636E\u65F6\u51FA\u9519"}),c("div",{className:"mt-2 text-sm text-red-700",children:c("p",{children:o})}),c("div",{className:"mt-4",children:c("button",{onClick:B,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"\u91CD\u8BD5"})})]})})}),c(wr,{columns:A,data:r,loading:l,pagination:{current:i,pageSize:u,total:n,onChange:f},sortConfig:{key:a.sortBy||"createdAt",direction:a.sortDesc?"desc":"asc"},onSort:h,rowKey:"_id",onRowClick:S=>{console.log("\u70B9\u51FB\u8D27\u5E01:",S)},emptyText:o?"\u52A0\u8F7D\u5931\u8D25":"\u6682\u65E0\u8D27\u5E01\u6570\u636E"})]})},tx=()=>{const[e,t]=C.exports.useState(!1);C.exports.useState(null),C.exports.useState(!1);const{data:r,total:n,loading:l,error:o,page:i,pageSize:u,params:a,refetch:s,updateParams:d,changePage:y}=j1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),p=[{key:"_id",title:"ID",dataIndex:"_id",width:120,render:f=>E("span",{className:"font-mono text-xs text-gray-600",children:[f.substring(0,8),"..."]})},{key:"name",title:"\u5206\u7C7B\u540D\u79F0",dataIndex:"name",width:150,sortable:!0,render:f=>c("span",{className:"font-medium text-gray-900",children:f})},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546",dataIndex:"provider",width:150,sortable:!0,render:f=>c("span",{className:"text-blue-600 font-medium",children:f})},{key:"description",title:"\u63CF\u8FF0",dataIndex:"description",width:200,render:f=>c("span",{className:"text-sm text-gray-600 truncate",title:f,children:f||"-"})},{key:"apiHost",title:"API\u4E3B\u673A",dataIndex:"apiHost",width:180,render:f=>c("span",{className:"font-mono text-xs text-green-600",children:f||"-"})},{key:"serviceOptions",title:"\u670D\u52A1\u9009\u9879",dataIndex:"serviceOptions",width:150,render:f=>{const k=f?Object.keys(f).length:0;return E("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:[k," \u4E2A\u9009\u9879"]})}},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:f=>c("span",{className:"text-sm text-gray-600",children:new Date(f*1e3).toLocaleString()})},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",dataIndex:"updatedAt",width:160,sortable:!0,render:f=>c("span",{className:"text-sm text-gray-600",children:new Date(f*1e3).toLocaleString()})}],x=[{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u63D0\u4F9B\u5546\u540D\u79F0..."},{key:"name",label:"\u5206\u7C7B\u540D\u79F0",type:"text",placeholder:"\u8F93\u5165\u5206\u7C7B\u540D\u79F0..."}],w=(f,k)=>{d({sortBy:f,sortDesc:k==="desc",offset:0})},N=(f,k)=>{y(f,k)},A=f=>{const k={offset:0,...f};d(k),t(!1)},m=()=>{s()},h=()=>{d({provider:void 0,name:void 0,offset:0}),t(!1)};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[E("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[c(au,{className:"h-6 w-6 mr-2 text-blue-600"}),"\u670D\u52A1\u5206\u7C7B\u7BA1\u7406"]}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u670D\u52A1\u5206\u7C7B\u548C\u914D\u7F6E\u9009\u9879"})]}),E("div",{className:"flex space-x-3",children:[E("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[c(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u663E\u793A\u7B5B\u9009"]}),E("button",{onClick:m,disabled:l,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[c(Il,{className:`h-4 w-4 mr-2 ${l?"animate-spin":""}`}),"\u5237\u65B0"]}),c(kr,{data:r,columns:[{key:"_id",title:"ID"},{key:"name",title:"\u5206\u7C7B\u540D\u79F0"},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546"},{key:"description",title:"\u63CF\u8FF0"},{key:"apiHost",title:"API\u4E3B\u673A"},{key:"serviceOptions",title:"\u670D\u52A1\u9009\u9879",render:f=>vn(f)},{key:"name2ID",title:"\u540D\u79F0\u6620\u5C04",render:f=>vn(f)},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:f=>Be(f)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:f=>Be(f)}],filename:"service_categories",disabled:l||r.length===0})]})]}),e&&c("div",{className:"bg-white rounded-lg shadow p-6",children:c(Vr,{fields:x,onSubmit:A,onReset:h,loading:l,initialValues:a})}),c("div",{className:"bg-white rounded-lg shadow p-6",children:c("div",{className:"flex items-center justify-between",children:E("div",{className:"flex items-center space-x-4",children:[E("div",{className:"flex items-center",children:[c(au,{className:"h-5 w-5 text-blue-500 mr-2"}),E("span",{className:"text-sm font-medium text-gray-900",children:["\u603B\u5206\u7C7B\u6570: ",n.toLocaleString()]})]}),a.provider&&E("div",{className:"text-sm text-gray-600",children:["\u63D0\u4F9B\u5546: ",a.provider]}),a.name&&E("div",{className:"text-sm text-gray-600",children:["\u5206\u7C7B: ",a.name]})]})})}),c("div",{className:"bg-white rounded-lg shadow",children:c(wr,{columns:p,data:r,loading:l,error:o||void 0,pagination:{current:i,pageSize:u,total:n,onChange:N,showSizeChanger:!0,showQuickJumper:!0,showTotal:(f,k)=>`\u663E\u793A ${k[0]}-${k[1]} \u6761\uFF0C\u5171 ${f} \u6761\u8BB0\u5F55`},onSort:w,sortBy:a.sortBy,sortDesc:a.sortDesc})})]})},rx=()=>{const[e,t]=C.exports.useState(!1),{data:r,total:n,loading:l,error:o,page:i,pageSize:u,params:a,refetch:s,updateParams:d,changePage:y,lastRefresh:p,autoRefresh:x,refreshInterval:w,setAutoRefresh:N}=U1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),A=[{key:"_id",title:"ID",dataIndex:"_id",width:120,render:S=>E("span",{className:"font-mono text-xs text-gray-600",children:[S.substring(0,8),"..."]})},{key:"name",title:"\u63D0\u4F9B\u5546\u540D\u79F0",dataIndex:"name",width:150,sortable:!0,render:S=>c("span",{className:"font-medium text-gray-900",children:S})},{key:"walletAddress",title:"\u94B1\u5305\u5730\u5740",dataIndex:"walletAddress",width:180,sortable:!0,render:S=>E("span",{className:"font-mono text-xs text-blue-600",children:[S.substring(0,12),"..."]})},{key:"publickey",title:"\u516C\u94A5",dataIndex:"publickey",width:150,render:S=>c("span",{className:"font-mono text-xs text-green-600",children:S?`${S.substring(0,12)}...`:"-"})},{key:"signAddress",title:"\u7B7E\u540D\u5730\u5740",dataIndex:"signAddress",width:150,render:S=>c("span",{className:"font-mono text-xs text-purple-600",children:S?`${S.substring(0,12)}...`:"-"})},{key:"apiHost",title:"API\u4E3B\u673A",dataIndex:"apiHost",width:180,render:S=>c("span",{className:"font-mono text-xs text-green-600",children:S||"-"})},{key:"category2ID",title:"\u5206\u7C7B\u6620\u5C04",dataIndex:"category2ID",width:120,render:S=>{const b=S?Object.keys(S).length:0;return E("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:[b," \u4E2A\u5206\u7C7B"]})}},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:S=>c("span",{className:"text-sm text-gray-600",children:new Date(S*1e3).toLocaleString()})}],m=[{key:"name",label:"\u63D0\u4F9B\u5546\u540D\u79F0",type:"text",placeholder:"\u8F93\u5165\u63D0\u4F9B\u5546\u540D\u79F0..."},{key:"walletAddress",label:"\u94B1\u5305\u5730\u5740",type:"text",placeholder:"\u8F93\u5165\u94B1\u5305\u5730\u5740..."},{key:"publicKey",label:"\u516C\u94A5",type:"text",placeholder:"\u8F93\u5165\u516C\u94A5..."},{key:"signAddress",label:"\u7B7E\u540D\u5730\u5740",type:"text",placeholder:"\u8F93\u5165\u7B7E\u540D\u5730\u5740..."},{key:"apiHost",label:"API\u4E3B\u673A",type:"text",placeholder:"\u8F93\u5165API\u4E3B\u673A\u5730\u5740..."}],h=(S,b)=>{d({sortBy:S,sortDesc:b==="desc",offset:0})},f=(S,b)=>{y(S,b)},k=S=>{const b={offset:0,...S};d(b),t(!1)},B=()=>{s()},g=()=>{d({name:void 0,walletAddress:void 0,publicKey:void 0,signAddress:void 0,apiHost:void 0,offset:0}),t(!1)};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[E("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[c(ei,{className:"h-6 w-6 mr-2 text-blue-600"}),"\u670D\u52A1\u63D0\u4F9B\u5546\u7BA1\u7406"]}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u670D\u52A1\u63D0\u4F9B\u5546\u4FE1\u606F\u548C\u914D\u7F6E"})]}),E("div",{className:"flex space-x-3",children:[E("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[c(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u663E\u793A\u7B5B\u9009"]}),E("button",{onClick:B,disabled:l,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[c(Il,{className:`h-4 w-4 mr-2 ${l?"animate-spin":""}`}),"\u5237\u65B0"]}),c(kr,{data:r,columns:[{key:"_id",title:"ID"},{key:"name",title:"\u63D0\u4F9B\u5546\u540D\u79F0"},{key:"walletAddress",title:"\u94B1\u5305\u5730\u5740"},{key:"publickey",title:"\u516C\u94A5"},{key:"signAddress",title:"\u7B7E\u540D\u5730\u5740"},{key:"apiHost",title:"API\u4E3B\u673A"},{key:"category2ID",title:"\u5206\u7C7B\u6620\u5C04",render:S=>vn(S)},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:S=>Be(S)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:S=>Be(S)}],filename:"providers",disabled:l||r.length===0})]})]}),e&&c("div",{className:"bg-white rounded-lg shadow p-6",children:c(Vr,{fields:m,onSubmit:k,onReset:g,loading:l,initialValues:a})}),c("div",{className:"bg-white rounded-lg shadow p-6",children:c("div",{className:"flex items-center justify-between",children:E("div",{className:"flex items-center space-x-4",children:[E("div",{className:"flex items-center",children:[c(ei,{className:"h-5 w-5 text-blue-500 mr-2"}),E("span",{className:"text-sm font-medium text-gray-900",children:["\u603B\u63D0\u4F9B\u5546\u6570: ",n.toLocaleString()]})]}),a.name&&E("div",{className:"text-sm text-gray-600",children:["\u540D\u79F0: ",a.name]}),a.walletAddress&&E("div",{className:"text-sm text-gray-600",children:["\u94B1\u5305: ",a.walletAddress]})]})})}),c("div",{className:"bg-white rounded-lg shadow",children:c(wr,{columns:A,data:r,loading:l,error:o||void 0,pagination:{current:i,pageSize:u,total:n,onChange:f,showSizeChanger:!0,showQuickJumper:!0,showTotal:(S,b)=>`\u663E\u793A ${b[0]}-${b[1]} \u6761\uFF0C\u5171 ${S} \u6761\u8BB0\u5F55`},onSort:h,sortBy:a.sortBy,sortDesc:a.sortDesc})})]})},nx=()=>{const[e,t]=C.exports.useState(!1),{data:r,total:n,loading:l,error:o,page:i,pageSize:u,params:a,refetch:s,updateParams:d,changePage:y}=V1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),p=[{key:"_id",title:"ID",dataIndex:"_id",width:120,render:f=>E("span",{className:"font-mono text-xs text-gray-600",children:[f.substring(0,8),"..."]})},{key:"name",title:"\u670D\u52A1\u7C7B\u578B\u540D\u79F0",dataIndex:"name",width:150,sortable:!0,render:f=>c("span",{className:"font-medium text-gray-900",children:f})},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546",dataIndex:"provider",width:150,sortable:!0,render:f=>c("span",{className:"text-blue-600 font-medium",children:f})},{key:"category",title:"\u670D\u52A1\u5206\u7C7B",dataIndex:"category",width:120,sortable:!0,render:f=>c("span",{className:"text-green-600 font-medium",children:f})},{key:"refundable",title:"\u53EF\u9000\u6B3E",dataIndex:"refundable",width:100,sortable:!0,render:f=>c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${f?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:f?"\u662F":"\u5426"})},{key:"description",title:"\u63CF\u8FF0",dataIndex:"description",width:200,render:f=>c("span",{className:"text-sm text-gray-600 truncate",title:f,children:f||"-"})},{key:"apiHost",title:"API\u4E3B\u673A",dataIndex:"apiHost",width:180,render:f=>c("span",{className:"font-mono text-xs text-green-600",children:f||"-"})},{key:"durationToPrice",title:"\u4EF7\u683C\u8BBE\u7F6E",dataIndex:"durationToPrice",width:120,render:f=>{const k=f?f.length:0;return E("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800",children:[k," \u4E2A\u4EF7\u683C"]})}},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:f=>c("span",{className:"text-sm text-gray-600",children:new Date(f*1e3).toLocaleString()})}],x=[{key:"name",label:"\u670D\u52A1\u7C7B\u578B\u540D\u79F0",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u7C7B\u578B\u540D\u79F0..."},{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u63D0\u4F9B\u5546..."},{key:"category",label:"\u670D\u52A1\u5206\u7C7B",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u5206\u7C7B..."},{key:"categoryID",label:"\u5206\u7C7BID",type:"text",placeholder:"\u8F93\u5165\u5206\u7C7BID..."},{key:"refundable",label:"\u53EF\u9000\u6B3E",type:"select",options:[{label:"\u662F",value:"true"},{label:"\u5426",value:"false"}]}],w=(f,k)=>{d({sortBy:f,sortDesc:k==="desc",offset:0})},N=(f,k)=>{y(f,k)},A=f=>{const k={offset:0,...f};f.refundable&&(k.refundable=f.refundable==="true"),d(k),t(!1)},m=()=>{s()},h=()=>{d({name:void 0,provider:void 0,category:void 0,categoryID:void 0,refundable:void 0,offset:0}),t(!1)};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[E("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[c(Dl,{className:"h-6 w-6 mr-2 text-blue-600"}),"\u670D\u52A1\u7C7B\u578B\u7BA1\u7406"]}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u670D\u52A1\u7C7B\u578B\u5B9A\u4E49\u548C\u914D\u7F6E"})]}),E("div",{className:"flex space-x-3",children:[E("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[c(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u663E\u793A\u7B5B\u9009"]}),E("button",{onClick:m,disabled:l,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[c(Il,{className:`h-4 w-4 mr-2 ${l?"animate-spin":""}`}),"\u5237\u65B0"]}),c(kr,{data:r,columns:[{key:"_id",title:"ID"},{key:"name",title:"\u670D\u52A1\u7C7B\u578B\u540D\u79F0"},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546"},{key:"category",title:"\u670D\u52A1\u5206\u7C7B"},{key:"categoryID",title:"\u5206\u7C7BID"},{key:"refundable",title:"\u53EF\u9000\u6B3E",render:f=>r0(f)},{key:"description",title:"\u63CF\u8FF0"},{key:"apiHost",title:"API\u4E3B\u673A"},{key:"serviceOptions",title:"\u670D\u52A1\u9009\u9879",render:f=>vn(f)},{key:"durationToPrice",title:"\u4EF7\u683C\u8BBE\u7F6E",render:f=>vn(f)},{key:"serviceOptionDesc",title:"\u9009\u9879\u63CF\u8FF0",render:f=>vn(f)},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:f=>Be(f)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:f=>Be(f)}],filename:"service_types",disabled:l||r.length===0})]})]}),e&&c("div",{className:"bg-white rounded-lg shadow p-6",children:c(Vr,{fields:x,onSubmit:A,onReset:h,loading:l,initialValues:a})}),c("div",{className:"bg-white rounded-lg shadow p-6",children:c("div",{className:"flex items-center justify-between",children:E("div",{className:"flex items-center space-x-4",children:[E("div",{className:"flex items-center",children:[c(Dl,{className:"h-5 w-5 text-blue-500 mr-2"}),E("span",{className:"text-sm font-medium text-gray-900",children:["\u603B\u670D\u52A1\u7C7B\u578B\u6570: ",n.toLocaleString()]})]}),a.name&&E("div",{className:"text-sm text-gray-600",children:["\u7C7B\u578B: ",a.name]}),a.provider&&E("div",{className:"text-sm text-gray-600",children:["\u63D0\u4F9B\u5546: ",a.provider]}),a.category&&E("div",{className:"text-sm text-gray-600",children:["\u5206\u7C7B: ",a.category]})]})})}),c("div",{className:"bg-white rounded-lg shadow",children:c(wr,{columns:p,data:r,loading:l,error:o||void 0,pagination:{current:i,pageSize:u,total:n,onChange:N,showSizeChanger:!0,showQuickJumper:!0,showTotal:(f,k)=>`\u663E\u793A ${k[0]}-${k[1]} \u6761\uFF0C\u5171 ${f} \u6761\u8BB0\u5F55`},onSort:w,sortBy:a.sortBy,sortDesc:a.sortDesc})})]})},lx=rv([{path:"/",element:c(Q1,{}),children:[{index:!0,element:c(Rg,{to:"/user-services",replace:!0})},{path:"dashboard",element:c(K1,{})},{path:"user-services",element:c(J1,{})},{path:"orders",element:c(X1,{})},{path:"order-services",element:c(q1,{})},{path:"cli-versions",element:c(Z1,{})},{path:"currencies",element:c(ex,{})},{path:"service-categories",element:c(tx,{})},{path:"providers",element:c(rx,{})},{path:"service-types",element:c(nx,{})}]}]);function ox(){return c(hv,{router:lx})}const ix=document.getElementById("root"),ax=Sh(ix);ax.render(c(T0.StrictMode,{children:c(ox,{})}));
