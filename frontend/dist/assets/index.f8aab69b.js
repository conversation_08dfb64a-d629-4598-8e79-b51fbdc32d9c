var x0=Object.defineProperty;var w0=(e,t,r)=>t in e?x0(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Yr=(e,t,r)=>(w0(e,typeof t!="symbol"?t+"":t,r),r);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))n(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&n(a)}).observe(document,{childList:!0,subtree:!0});function r(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerpolicy&&(o.referrerPolicy=l.referrerpolicy),l.crossorigin==="use-credentials"?o.credentials="include":l.crossorigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(l){if(l.ep)return;l.ep=!0;const o=r(l);fetch(l.href,o)}})();function k0(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var C={exports:{}},ee={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rl=Symbol.for("react.element"),S0=Symbol.for("react.portal"),E0=Symbol.for("react.fragment"),C0=Symbol.for("react.strict_mode"),N0=Symbol.for("react.profiler"),D0=Symbol.for("react.provider"),F0=Symbol.for("react.context"),R0=Symbol.for("react.forward_ref"),B0=Symbol.for("react.suspense"),A0=Symbol.for("react.memo"),P0=Symbol.for("react.lazy"),Rs=Symbol.iterator;function b0(e){return e===null||typeof e!="object"?null:(e=Rs&&e[Rs]||e["@@iterator"],typeof e=="function"?e:null)}var hd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},pd=Object.assign,md={};function Fn(e,t,r){this.props=e,this.context=t,this.refs=md,this.updater=r||hd}Fn.prototype.isReactComponent={};Fn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Fn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function yd(){}yd.prototype=Fn.prototype;function cu(e,t,r){this.props=e,this.context=t,this.refs=md,this.updater=r||hd}var du=cu.prototype=new yd;du.constructor=cu;pd(du,Fn.prototype);du.isPureReactComponent=!0;var Bs=Array.isArray,gd=Object.prototype.hasOwnProperty,fu={current:null},vd={key:!0,ref:!0,__self:!0,__source:!0};function xd(e,t,r){var n,l={},o=null,a=null;if(t!=null)for(n in t.ref!==void 0&&(a=t.ref),t.key!==void 0&&(o=""+t.key),t)gd.call(t,n)&&!vd.hasOwnProperty(n)&&(l[n]=t[n]);var u=arguments.length-2;if(u===1)l.children=r;else if(1<u){for(var i=Array(u),s=0;s<u;s++)i[s]=arguments[s+2];l.children=i}if(e&&e.defaultProps)for(n in u=e.defaultProps,u)l[n]===void 0&&(l[n]=u[n]);return{$$typeof:Rl,type:e,key:o,ref:a,props:l,_owner:fu.current}}function _0(e,t){return{$$typeof:Rl,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function hu(e){return typeof e=="object"&&e!==null&&e.$$typeof===Rl}function L0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var As=/\/+/g;function Aa(e,t){return typeof e=="object"&&e!==null&&e.key!=null?L0(""+e.key):t.toString(36)}function io(e,t,r,n,l){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var a=!1;if(e===null)a=!0;else switch(o){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case Rl:case S0:a=!0}}if(a)return a=e,l=l(a),e=n===""?"."+Aa(a,0):n,Bs(l)?(r="",e!=null&&(r=e.replace(As,"$&/")+"/"),io(l,t,r,"",function(s){return s})):l!=null&&(hu(l)&&(l=_0(l,r+(!l.key||a&&a.key===l.key?"":(""+l.key).replace(As,"$&/")+"/")+e)),t.push(l)),1;if(a=0,n=n===""?".":n+":",Bs(e))for(var u=0;u<e.length;u++){o=e[u];var i=n+Aa(o,u);a+=io(o,t,r,i,l)}else if(i=b0(e),typeof i=="function")for(e=i.call(e),u=0;!(o=e.next()).done;)o=o.value,i=n+Aa(o,u++),a+=io(o,t,r,i,l);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function jl(e,t,r){if(e==null)return e;var n=[],l=0;return io(e,n,"","",function(o){return t.call(r,o,l++)}),n}function I0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){(e._status===0||e._status===-1)&&(e._status=1,e._result=r)},function(r){(e._status===0||e._status===-1)&&(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var We={current:null},uo={transition:null},M0={ReactCurrentDispatcher:We,ReactCurrentBatchConfig:uo,ReactCurrentOwner:fu};function wd(){throw Error("act(...) is not supported in production builds of React.")}ee.Children={map:jl,forEach:function(e,t,r){jl(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return jl(e,function(){t++}),t},toArray:function(e){return jl(e,function(t){return t})||[]},only:function(e){if(!hu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};ee.Component=Fn;ee.Fragment=E0;ee.Profiler=N0;ee.PureComponent=cu;ee.StrictMode=C0;ee.Suspense=B0;ee.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=M0;ee.act=wd;ee.cloneElement=function(e,t,r){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var n=pd({},e.props),l=e.key,o=e.ref,a=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,a=fu.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(i in t)gd.call(t,i)&&!vd.hasOwnProperty(i)&&(n[i]=t[i]===void 0&&u!==void 0?u[i]:t[i])}var i=arguments.length-2;if(i===1)n.children=r;else if(1<i){u=Array(i);for(var s=0;s<i;s++)u[s]=arguments[s+2];n.children=u}return{$$typeof:Rl,type:e.type,key:l,ref:o,props:n,_owner:a}};ee.createContext=function(e){return e={$$typeof:F0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:D0,_context:e},e.Consumer=e};ee.createElement=xd;ee.createFactory=function(e){var t=xd.bind(null,e);return t.type=e,t};ee.createRef=function(){return{current:null}};ee.forwardRef=function(e){return{$$typeof:R0,render:e}};ee.isValidElement=hu;ee.lazy=function(e){return{$$typeof:P0,_payload:{_status:-1,_result:e},_init:I0}};ee.memo=function(e,t){return{$$typeof:A0,type:e,compare:t===void 0?null:t}};ee.startTransition=function(e){var t=uo.transition;uo.transition={};try{e()}finally{uo.transition=t}};ee.unstable_act=wd;ee.useCallback=function(e,t){return We.current.useCallback(e,t)};ee.useContext=function(e){return We.current.useContext(e)};ee.useDebugValue=function(){};ee.useDeferredValue=function(e){return We.current.useDeferredValue(e)};ee.useEffect=function(e,t){return We.current.useEffect(e,t)};ee.useId=function(){return We.current.useId()};ee.useImperativeHandle=function(e,t,r){return We.current.useImperativeHandle(e,t,r)};ee.useInsertionEffect=function(e,t){return We.current.useInsertionEffect(e,t)};ee.useLayoutEffect=function(e,t){return We.current.useLayoutEffect(e,t)};ee.useMemo=function(e,t){return We.current.useMemo(e,t)};ee.useReducer=function(e,t,r){return We.current.useReducer(e,t,r)};ee.useRef=function(e){return We.current.useRef(e)};ee.useState=function(e){return We.current.useState(e)};ee.useSyncExternalStore=function(e,t,r){return We.current.useSyncExternalStore(e,t,r)};ee.useTransition=function(){return We.current.useTransition()};ee.version="18.3.1";(function(e){e.exports=ee})(C);const T0=k0(C.exports);var pu={exports:{}},ot={},kd={exports:{}},Sd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(I,j){var V=I.length;I.push(j);e:for(;0<V;){var te=V-1>>>1,le=I[te];if(0<l(le,j))I[te]=j,I[V]=le,V=te;else break e}}function r(I){return I.length===0?null:I[0]}function n(I){if(I.length===0)return null;var j=I[0],V=I.pop();if(V!==j){I[0]=V;e:for(var te=0,le=I.length,ke=le>>>1;te<ke;){var $e=2*(te+1)-1,Wt=I[$e],je=$e+1,Wr=I[je];if(0>l(Wt,V))je<le&&0>l(Wr,Wt)?(I[te]=Wr,I[je]=V,te=je):(I[te]=Wt,I[$e]=V,te=$e);else if(je<le&&0>l(Wr,V))I[te]=Wr,I[je]=V,te=je;else break e}}return j}function l(I,j){var V=I.sortIndex-j.sortIndex;return V!==0?V:I.id-j.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var a=Date,u=a.now();e.unstable_now=function(){return a.now()-u}}var i=[],s=[],d=1,m=null,f=3,w=!1,k=!1,N=!1,P=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(I){for(var j=r(s);j!==null;){if(j.callback===null)n(s);else if(j.startTime<=I)n(s),j.sortIndex=j.expirationTime,t(i,j);else break;j=r(s)}}function S(I){if(N=!1,g(I),!k)if(r(i)!==null)k=!0,Z(F);else{var j=r(s);j!==null&&Ce(S,j.startTime-I)}}function F(I,j){k=!1,N&&(N=!1,p(B),B=-1),w=!0;var V=f;try{for(g(j),m=r(i);m!==null&&(!(m.expirationTime>j)||I&&!G());){var te=m.callback;if(typeof te=="function"){m.callback=null,f=m.priorityLevel;var le=te(m.expirationTime<=j);j=e.unstable_now(),typeof le=="function"?m.callback=le:m===r(i)&&n(i),g(j)}else n(i);m=r(i)}if(m!==null)var ke=!0;else{var $e=r(s);$e!==null&&Ce(S,$e.startTime-j),ke=!1}return ke}finally{m=null,f=V,w=!1}}var v=!1,y=null,B=-1,z=5,$=-1;function G(){return!(e.unstable_now()-$<z)}function ge(){if(y!==null){var I=e.unstable_now();$=I;var j=!0;try{j=y(!0,I)}finally{j?we():(v=!1,y=null)}}else v=!1}var we;if(typeof h=="function")we=function(){h(ge)};else if(typeof MessageChannel<"u"){var A=new MessageChannel,L=A.port2;A.port1.onmessage=ge,we=function(){L.postMessage(null)}}else we=function(){P(ge,0)};function Z(I){y=I,v||(v=!0,we())}function Ce(I,j){B=P(function(){I(e.unstable_now())},j)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(I){I.callback=null},e.unstable_continueExecution=function(){k||w||(k=!0,Z(F))},e.unstable_forceFrameRate=function(I){0>I||125<I?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):z=0<I?Math.floor(1e3/I):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return r(i)},e.unstable_next=function(I){switch(f){case 1:case 2:case 3:var j=3;break;default:j=f}var V=f;f=j;try{return I()}finally{f=V}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(I,j){switch(I){case 1:case 2:case 3:case 4:case 5:break;default:I=3}var V=f;f=I;try{return j()}finally{f=V}},e.unstable_scheduleCallback=function(I,j,V){var te=e.unstable_now();switch(typeof V=="object"&&V!==null?(V=V.delay,V=typeof V=="number"&&0<V?te+V:te):V=te,I){case 1:var le=-1;break;case 2:le=250;break;case 5:le=**********;break;case 4:le=1e4;break;default:le=5e3}return le=V+le,I={id:d++,callback:j,priorityLevel:I,startTime:V,expirationTime:le,sortIndex:-1},V>te?(I.sortIndex=V,t(s,I),r(i)===null&&I===r(s)&&(N?(p(B),B=-1):N=!0,Ce(S,V-te))):(I.sortIndex=le,t(i,I),k||w||(k=!0,Z(F))),I},e.unstable_shouldYield=G,e.unstable_wrapCallback=function(I){var j=f;return function(){var V=f;f=j;try{return I.apply(this,arguments)}finally{f=V}}}})(Sd);(function(e){e.exports=Sd})(kd);/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var z0=C.exports,lt=kd.exports;function _(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Ed=new Set,ul={};function jr(e,t){wn(e,t),wn(e+"Capture",t)}function wn(e,t){for(ul[e]=t,e=0;e<t.length;e++)Ed.add(t[e])}var zt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ui=Object.prototype.hasOwnProperty,O0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ps={},bs={};function $0(e){return ui.call(bs,e)?!0:ui.call(Ps,e)?!1:O0.test(e)?bs[e]=!0:(Ps[e]=!0,!1)}function j0(e,t,r,n){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return n?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function U0(e,t,r,n){if(t===null||typeof t>"u"||j0(e,t,r,n))return!0;if(n)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Qe(e,t,r,n,l,o,a){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=n,this.attributeNamespace=l,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=a}var _e={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){_e[e]=new Qe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];_e[t]=new Qe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){_e[e]=new Qe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){_e[e]=new Qe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){_e[e]=new Qe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){_e[e]=new Qe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){_e[e]=new Qe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){_e[e]=new Qe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){_e[e]=new Qe(e,5,!1,e.toLowerCase(),null,!1,!1)});var mu=/[\-:]([a-z])/g;function yu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(mu,yu);_e[t]=new Qe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(mu,yu);_e[t]=new Qe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(mu,yu);_e[t]=new Qe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){_e[e]=new Qe(e,1,!1,e.toLowerCase(),null,!1,!1)});_e.xlinkHref=new Qe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){_e[e]=new Qe(e,1,!1,e.toLowerCase(),null,!0,!0)});function gu(e,t,r,n){var l=_e.hasOwnProperty(t)?_e[t]:null;(l!==null?l.type!==0:n||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(U0(t,r,l,n)&&(r=null),n||l===null?$0(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):l.mustUseProperty?e[l.propertyName]=r===null?l.type===3?!1:"":r:(t=l.attributeName,n=l.attributeNamespace,r===null?e.removeAttribute(t):(l=l.type,r=l===3||l===4&&r===!0?"":""+r,n?e.setAttributeNS(n,t,r):e.setAttribute(t,r))))}var Ut=z0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ul=Symbol.for("react.element"),qr=Symbol.for("react.portal"),Zr=Symbol.for("react.fragment"),vu=Symbol.for("react.strict_mode"),si=Symbol.for("react.profiler"),Cd=Symbol.for("react.provider"),Nd=Symbol.for("react.context"),xu=Symbol.for("react.forward_ref"),ci=Symbol.for("react.suspense"),di=Symbol.for("react.suspense_list"),wu=Symbol.for("react.memo"),Xt=Symbol.for("react.lazy"),Dd=Symbol.for("react.offscreen"),_s=Symbol.iterator;function Mn(e){return e===null||typeof e!="object"?null:(e=_s&&e[_s]||e["@@iterator"],typeof e=="function"?e:null)}var pe=Object.assign,Pa;function Kn(e){if(Pa===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);Pa=t&&t[1]||""}return`
`+Pa+e}var ba=!1;function _a(e,t){if(!e||ba)return"";ba=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(s){var n=s}Reflect.construct(e,[],t)}else{try{t.call()}catch(s){n=s}e.call(t.prototype)}else{try{throw Error()}catch(s){n=s}e()}}catch(s){if(s&&n&&typeof s.stack=="string"){for(var l=s.stack.split(`
`),o=n.stack.split(`
`),a=l.length-1,u=o.length-1;1<=a&&0<=u&&l[a]!==o[u];)u--;for(;1<=a&&0<=u;a--,u--)if(l[a]!==o[u]){if(a!==1||u!==1)do if(a--,u--,0>u||l[a]!==o[u]){var i=`
`+l[a].replace(" at new "," at ");return e.displayName&&i.includes("<anonymous>")&&(i=i.replace("<anonymous>",e.displayName)),i}while(1<=a&&0<=u);break}}}finally{ba=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?Kn(e):""}function V0(e){switch(e.tag){case 5:return Kn(e.type);case 16:return Kn("Lazy");case 13:return Kn("Suspense");case 19:return Kn("SuspenseList");case 0:case 2:case 15:return e=_a(e.type,!1),e;case 11:return e=_a(e.type.render,!1),e;case 1:return e=_a(e.type,!0),e;default:return""}}function fi(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Zr:return"Fragment";case qr:return"Portal";case si:return"Profiler";case vu:return"StrictMode";case ci:return"Suspense";case di:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Nd:return(e.displayName||"Context")+".Consumer";case Cd:return(e._context.displayName||"Context")+".Provider";case xu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case wu:return t=e.displayName||null,t!==null?t:fi(e.type)||"Memo";case Xt:t=e._payload,e=e._init;try{return fi(e(t))}catch{}}return null}function H0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return fi(t);case 8:return t===vu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function fr(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Fd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function W0(e){var t=Fd(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var l=r.get,o=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(a){n=""+a,o.call(this,a)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return n},setValue:function(a){n=""+a},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Vl(e){e._valueTracker||(e._valueTracker=W0(e))}function Rd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),n="";return e&&(n=Fd(e)?e.checked?"true":"false":e.value),e=n,e!==r?(t.setValue(e),!0):!1}function No(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function hi(e,t){var r=t.checked;return pe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r!=null?r:e._wrapperState.initialChecked})}function Ls(e,t){var r=t.defaultValue==null?"":t.defaultValue,n=t.checked!=null?t.checked:t.defaultChecked;r=fr(t.value!=null?t.value:r),e._wrapperState={initialChecked:n,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Bd(e,t){t=t.checked,t!=null&&gu(e,"checked",t,!1)}function pi(e,t){Bd(e,t);var r=fr(t.value),n=t.type;if(r!=null)n==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(n==="submit"||n==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?mi(e,t.type,r):t.hasOwnProperty("defaultValue")&&mi(e,t.type,fr(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Is(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var n=t.type;if(!(n!=="submit"&&n!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function mi(e,t,r){(t!=="number"||No(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var Yn=Array.isArray;function fn(e,t,r,n){if(e=e.options,t){t={};for(var l=0;l<r.length;l++)t["$"+r[l]]=!0;for(r=0;r<e.length;r++)l=t.hasOwnProperty("$"+e[r].value),e[r].selected!==l&&(e[r].selected=l),l&&n&&(e[r].defaultSelected=!0)}else{for(r=""+fr(r),t=null,l=0;l<e.length;l++){if(e[l].value===r){e[l].selected=!0,n&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function yi(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(_(91));return pe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ms(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(_(92));if(Yn(r)){if(1<r.length)throw Error(_(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:fr(r)}}function Ad(e,t){var r=fr(t.value),n=fr(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),n!=null&&(e.defaultValue=""+n)}function Ts(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Pd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function gi(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Pd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Hl,bd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,n,l){MSApp.execUnsafeLocalFunction(function(){return e(t,r,n,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Hl=Hl||document.createElement("div"),Hl.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Hl.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function sl(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var Zn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Q0=["Webkit","ms","Moz","O"];Object.keys(Zn).forEach(function(e){Q0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Zn[t]=Zn[e]})});function _d(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||Zn.hasOwnProperty(e)&&Zn[e]?(""+t).trim():t+"px"}function Ld(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var n=r.indexOf("--")===0,l=_d(r,t[r],n);r==="float"&&(r="cssFloat"),n?e.setProperty(r,l):e[r]=l}}var K0=pe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function vi(e,t){if(t){if(K0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(_(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(_(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(_(61))}if(t.style!=null&&typeof t.style!="object")throw Error(_(62))}}function xi(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var wi=null;function ku(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ki=null,hn=null,pn=null;function zs(e){if(e=Pl(e)){if(typeof ki!="function")throw Error(_(280));var t=e.stateNode;t&&(t=sa(t),ki(e.stateNode,e.type,t))}}function Id(e){hn?pn?pn.push(e):pn=[e]:hn=e}function Md(){if(hn){var e=hn,t=pn;if(pn=hn=null,zs(e),t)for(e=0;e<t.length;e++)zs(t[e])}}function Td(e,t){return e(t)}function zd(){}var La=!1;function Od(e,t,r){if(La)return e(t,r);La=!0;try{return Td(e,t,r)}finally{La=!1,(hn!==null||pn!==null)&&(zd(),Md())}}function cl(e,t){var r=e.stateNode;if(r===null)return null;var n=sa(r);if(n===null)return null;r=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(_(231,t,typeof r));return r}var Si=!1;if(zt)try{var Tn={};Object.defineProperty(Tn,"passive",{get:function(){Si=!0}}),window.addEventListener("test",Tn,Tn),window.removeEventListener("test",Tn,Tn)}catch{Si=!1}function Y0(e,t,r,n,l,o,a,u,i){var s=Array.prototype.slice.call(arguments,3);try{t.apply(r,s)}catch(d){this.onError(d)}}var el=!1,Do=null,Fo=!1,Ei=null,G0={onError:function(e){el=!0,Do=e}};function J0(e,t,r,n,l,o,a,u,i){el=!1,Do=null,Y0.apply(G0,arguments)}function X0(e,t,r,n,l,o,a,u,i){if(J0.apply(this,arguments),el){if(el){var s=Do;el=!1,Do=null}else throw Error(_(198));Fo||(Fo=!0,Ei=s)}}function Ur(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function $d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Os(e){if(Ur(e)!==e)throw Error(_(188))}function q0(e){var t=e.alternate;if(!t){if(t=Ur(e),t===null)throw Error(_(188));return t!==e?null:e}for(var r=e,n=t;;){var l=r.return;if(l===null)break;var o=l.alternate;if(o===null){if(n=l.return,n!==null){r=n;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===r)return Os(l),e;if(o===n)return Os(l),t;o=o.sibling}throw Error(_(188))}if(r.return!==n.return)r=l,n=o;else{for(var a=!1,u=l.child;u;){if(u===r){a=!0,r=l,n=o;break}if(u===n){a=!0,n=l,r=o;break}u=u.sibling}if(!a){for(u=o.child;u;){if(u===r){a=!0,r=o,n=l;break}if(u===n){a=!0,n=o,r=l;break}u=u.sibling}if(!a)throw Error(_(189))}}if(r.alternate!==n)throw Error(_(190))}if(r.tag!==3)throw Error(_(188));return r.stateNode.current===r?e:t}function jd(e){return e=q0(e),e!==null?Ud(e):null}function Ud(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Ud(e);if(t!==null)return t;e=e.sibling}return null}var Vd=lt.unstable_scheduleCallback,$s=lt.unstable_cancelCallback,Z0=lt.unstable_shouldYield,ep=lt.unstable_requestPaint,ve=lt.unstable_now,tp=lt.unstable_getCurrentPriorityLevel,Su=lt.unstable_ImmediatePriority,Hd=lt.unstable_UserBlockingPriority,Ro=lt.unstable_NormalPriority,rp=lt.unstable_LowPriority,Wd=lt.unstable_IdlePriority,oa=null,Bt=null;function np(e){if(Bt&&typeof Bt.onCommitFiberRoot=="function")try{Bt.onCommitFiberRoot(oa,e,void 0,(e.current.flags&128)===128)}catch{}}var kt=Math.clz32?Math.clz32:ap,lp=Math.log,op=Math.LN2;function ap(e){return e>>>=0,e===0?32:31-(lp(e)/op|0)|0}var Wl=64,Ql=4194304;function Gn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Bo(e,t){var r=e.pendingLanes;if(r===0)return 0;var n=0,l=e.suspendedLanes,o=e.pingedLanes,a=r&268435455;if(a!==0){var u=a&~l;u!==0?n=Gn(u):(o&=a,o!==0&&(n=Gn(o)))}else a=r&~l,a!==0?n=Gn(a):o!==0&&(n=Gn(o));if(n===0)return 0;if(t!==0&&t!==n&&(t&l)===0&&(l=n&-n,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if((n&4)!==0&&(n|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=n;0<t;)r=31-kt(t),l=1<<r,n|=e[r],t&=~l;return n}function ip(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function up(e,t){for(var r=e.suspendedLanes,n=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var a=31-kt(o),u=1<<a,i=l[a];i===-1?((u&r)===0||(u&n)!==0)&&(l[a]=ip(u,t)):i<=t&&(e.expiredLanes|=u),o&=~u}}function Ci(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Qd(){var e=Wl;return Wl<<=1,(Wl&4194240)===0&&(Wl=64),e}function Ia(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function Bl(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-kt(t),e[t]=r}function sp(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var n=e.eventTimes;for(e=e.expirationTimes;0<r;){var l=31-kt(r),o=1<<l;t[l]=0,n[l]=-1,e[l]=-1,r&=~o}}function Eu(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var n=31-kt(r),l=1<<n;l&t|e[n]&t&&(e[n]|=t),r&=~l}}var oe=0;function Kd(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Yd,Cu,Gd,Jd,Xd,Ni=!1,Kl=[],lr=null,or=null,ar=null,dl=new Map,fl=new Map,Zt=[],cp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function js(e,t){switch(e){case"focusin":case"focusout":lr=null;break;case"dragenter":case"dragleave":or=null;break;case"mouseover":case"mouseout":ar=null;break;case"pointerover":case"pointerout":dl.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":fl.delete(t.pointerId)}}function zn(e,t,r,n,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:r,eventSystemFlags:n,nativeEvent:o,targetContainers:[l]},t!==null&&(t=Pl(t),t!==null&&Cu(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function dp(e,t,r,n,l){switch(t){case"focusin":return lr=zn(lr,e,t,r,n,l),!0;case"dragenter":return or=zn(or,e,t,r,n,l),!0;case"mouseover":return ar=zn(ar,e,t,r,n,l),!0;case"pointerover":var o=l.pointerId;return dl.set(o,zn(dl.get(o)||null,e,t,r,n,l)),!0;case"gotpointercapture":return o=l.pointerId,fl.set(o,zn(fl.get(o)||null,e,t,r,n,l)),!0}return!1}function qd(e){var t=Ar(e.target);if(t!==null){var r=Ur(t);if(r!==null){if(t=r.tag,t===13){if(t=$d(r),t!==null){e.blockedOn=t,Xd(e.priority,function(){Gd(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function so(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=Di(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var n=new r.constructor(r.type,r);wi=n,r.target.dispatchEvent(n),wi=null}else return t=Pl(r),t!==null&&Cu(t),e.blockedOn=r,!1;t.shift()}return!0}function Us(e,t,r){so(e)&&r.delete(t)}function fp(){Ni=!1,lr!==null&&so(lr)&&(lr=null),or!==null&&so(or)&&(or=null),ar!==null&&so(ar)&&(ar=null),dl.forEach(Us),fl.forEach(Us)}function On(e,t){e.blockedOn===t&&(e.blockedOn=null,Ni||(Ni=!0,lt.unstable_scheduleCallback(lt.unstable_NormalPriority,fp)))}function hl(e){function t(l){return On(l,e)}if(0<Kl.length){On(Kl[0],e);for(var r=1;r<Kl.length;r++){var n=Kl[r];n.blockedOn===e&&(n.blockedOn=null)}}for(lr!==null&&On(lr,e),or!==null&&On(or,e),ar!==null&&On(ar,e),dl.forEach(t),fl.forEach(t),r=0;r<Zt.length;r++)n=Zt[r],n.blockedOn===e&&(n.blockedOn=null);for(;0<Zt.length&&(r=Zt[0],r.blockedOn===null);)qd(r),r.blockedOn===null&&Zt.shift()}var mn=Ut.ReactCurrentBatchConfig,Ao=!0;function hp(e,t,r,n){var l=oe,o=mn.transition;mn.transition=null;try{oe=1,Nu(e,t,r,n)}finally{oe=l,mn.transition=o}}function pp(e,t,r,n){var l=oe,o=mn.transition;mn.transition=null;try{oe=4,Nu(e,t,r,n)}finally{oe=l,mn.transition=o}}function Nu(e,t,r,n){if(Ao){var l=Di(e,t,r,n);if(l===null)Wa(e,t,n,Po,r),js(e,n);else if(dp(l,e,t,r,n))n.stopPropagation();else if(js(e,n),t&4&&-1<cp.indexOf(e)){for(;l!==null;){var o=Pl(l);if(o!==null&&Yd(o),o=Di(e,t,r,n),o===null&&Wa(e,t,n,Po,r),o===l)break;l=o}l!==null&&n.stopPropagation()}else Wa(e,t,n,null,r)}}var Po=null;function Di(e,t,r,n){if(Po=null,e=ku(n),e=Ar(e),e!==null)if(t=Ur(e),t===null)e=null;else if(r=t.tag,r===13){if(e=$d(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Po=e,null}function Zd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(tp()){case Su:return 1;case Hd:return 4;case Ro:case rp:return 16;case Wd:return 536870912;default:return 16}default:return 16}}var rr=null,Du=null,co=null;function ef(){if(co)return co;var e,t=Du,r=t.length,n,l="value"in rr?rr.value:rr.textContent,o=l.length;for(e=0;e<r&&t[e]===l[e];e++);var a=r-e;for(n=1;n<=a&&t[r-n]===l[o-n];n++);return co=l.slice(e,1<n?1-n:void 0)}function fo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Yl(){return!0}function Vs(){return!1}function at(e){function t(r,n,l,o,a){this._reactName=r,this._targetInst=l,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null;for(var u in e)e.hasOwnProperty(u)&&(r=e[u],this[u]=r?r(o):o[u]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Yl:Vs,this.isPropagationStopped=Vs,this}return pe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=Yl)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=Yl)},persist:function(){},isPersistent:Yl}),t}var Rn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Fu=at(Rn),Al=pe({},Rn,{view:0,detail:0}),mp=at(Al),Ma,Ta,$n,aa=pe({},Al,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ru,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==$n&&($n&&e.type==="mousemove"?(Ma=e.screenX-$n.screenX,Ta=e.screenY-$n.screenY):Ta=Ma=0,$n=e),Ma)},movementY:function(e){return"movementY"in e?e.movementY:Ta}}),Hs=at(aa),yp=pe({},aa,{dataTransfer:0}),gp=at(yp),vp=pe({},Al,{relatedTarget:0}),za=at(vp),xp=pe({},Rn,{animationName:0,elapsedTime:0,pseudoElement:0}),wp=at(xp),kp=pe({},Rn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Sp=at(kp),Ep=pe({},Rn,{data:0}),Ws=at(Ep),Cp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Np={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Dp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Fp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Dp[e])?!!t[e]:!1}function Ru(){return Fp}var Rp=pe({},Al,{key:function(e){if(e.key){var t=Cp[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=fo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Np[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ru,charCode:function(e){return e.type==="keypress"?fo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?fo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Bp=at(Rp),Ap=pe({},aa,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Qs=at(Ap),Pp=pe({},Al,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ru}),bp=at(Pp),_p=pe({},Rn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Lp=at(_p),Ip=pe({},aa,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Mp=at(Ip),Tp=[9,13,27,32],Bu=zt&&"CompositionEvent"in window,tl=null;zt&&"documentMode"in document&&(tl=document.documentMode);var zp=zt&&"TextEvent"in window&&!tl,tf=zt&&(!Bu||tl&&8<tl&&11>=tl),Ks=String.fromCharCode(32),Ys=!1;function rf(e,t){switch(e){case"keyup":return Tp.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function nf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var en=!1;function Op(e,t){switch(e){case"compositionend":return nf(t);case"keypress":return t.which!==32?null:(Ys=!0,Ks);case"textInput":return e=t.data,e===Ks&&Ys?null:e;default:return null}}function $p(e,t){if(en)return e==="compositionend"||!Bu&&rf(e,t)?(e=ef(),co=Du=rr=null,en=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return tf&&t.locale!=="ko"?null:t.data;default:return null}}var jp={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Gs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!jp[e.type]:t==="textarea"}function lf(e,t,r,n){Id(n),t=bo(t,"onChange"),0<t.length&&(r=new Fu("onChange","change",null,r,n),e.push({event:r,listeners:t}))}var rl=null,pl=null;function Up(e){yf(e,0)}function ia(e){var t=nn(e);if(Rd(t))return e}function Vp(e,t){if(e==="change")return t}var of=!1;if(zt){var Oa;if(zt){var $a="oninput"in document;if(!$a){var Js=document.createElement("div");Js.setAttribute("oninput","return;"),$a=typeof Js.oninput=="function"}Oa=$a}else Oa=!1;of=Oa&&(!document.documentMode||9<document.documentMode)}function Xs(){rl&&(rl.detachEvent("onpropertychange",af),pl=rl=null)}function af(e){if(e.propertyName==="value"&&ia(pl)){var t=[];lf(t,pl,e,ku(e)),Od(Up,t)}}function Hp(e,t,r){e==="focusin"?(Xs(),rl=t,pl=r,rl.attachEvent("onpropertychange",af)):e==="focusout"&&Xs()}function Wp(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ia(pl)}function Qp(e,t){if(e==="click")return ia(t)}function Kp(e,t){if(e==="input"||e==="change")return ia(t)}function Yp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Et=typeof Object.is=="function"?Object.is:Yp;function ml(e,t){if(Et(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++){var l=r[n];if(!ui.call(t,l)||!Et(e[l],t[l]))return!1}return!0}function qs(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Zs(e,t){var r=qs(e);e=0;for(var n;r;){if(r.nodeType===3){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=qs(r)}}function uf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?uf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function sf(){for(var e=window,t=No();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=No(e.document)}return t}function Au(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Gp(e){var t=sf(),r=e.focusedElem,n=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&uf(r.ownerDocument.documentElement,r)){if(n!==null&&Au(r)){if(t=n.start,e=n.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=r.textContent.length,o=Math.min(n.start,l);n=n.end===void 0?o:Math.min(n.end,l),!e.extend&&o>n&&(l=n,n=o,o=l),l=Zs(r,o);var a=Zs(r,n);l&&a&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>n?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Jp=zt&&"documentMode"in document&&11>=document.documentMode,tn=null,Fi=null,nl=null,Ri=!1;function ec(e,t,r){var n=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;Ri||tn==null||tn!==No(n)||(n=tn,"selectionStart"in n&&Au(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),nl&&ml(nl,n)||(nl=n,n=bo(Fi,"onSelect"),0<n.length&&(t=new Fu("onSelect","select",null,t,r),e.push({event:t,listeners:n}),t.target=tn)))}function Gl(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var rn={animationend:Gl("Animation","AnimationEnd"),animationiteration:Gl("Animation","AnimationIteration"),animationstart:Gl("Animation","AnimationStart"),transitionend:Gl("Transition","TransitionEnd")},ja={},cf={};zt&&(cf=document.createElement("div").style,"AnimationEvent"in window||(delete rn.animationend.animation,delete rn.animationiteration.animation,delete rn.animationstart.animation),"TransitionEvent"in window||delete rn.transitionend.transition);function ua(e){if(ja[e])return ja[e];if(!rn[e])return e;var t=rn[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in cf)return ja[e]=t[r];return e}var df=ua("animationend"),ff=ua("animationiteration"),hf=ua("animationstart"),pf=ua("transitionend"),mf=new Map,tc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function mr(e,t){mf.set(e,t),jr(t,[e])}for(var Ua=0;Ua<tc.length;Ua++){var Va=tc[Ua],Xp=Va.toLowerCase(),qp=Va[0].toUpperCase()+Va.slice(1);mr(Xp,"on"+qp)}mr(df,"onAnimationEnd");mr(ff,"onAnimationIteration");mr(hf,"onAnimationStart");mr("dblclick","onDoubleClick");mr("focusin","onFocus");mr("focusout","onBlur");mr(pf,"onTransitionEnd");wn("onMouseEnter",["mouseout","mouseover"]);wn("onMouseLeave",["mouseout","mouseover"]);wn("onPointerEnter",["pointerout","pointerover"]);wn("onPointerLeave",["pointerout","pointerover"]);jr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));jr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));jr("onBeforeInput",["compositionend","keypress","textInput","paste"]);jr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));jr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));jr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Jn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Zp=new Set("cancel close invalid load scroll toggle".split(" ").concat(Jn));function rc(e,t,r){var n=e.type||"unknown-event";e.currentTarget=r,X0(n,t,void 0,e),e.currentTarget=null}function yf(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var n=e[r],l=n.event;n=n.listeners;e:{var o=void 0;if(t)for(var a=n.length-1;0<=a;a--){var u=n[a],i=u.instance,s=u.currentTarget;if(u=u.listener,i!==o&&l.isPropagationStopped())break e;rc(l,u,s),o=i}else for(a=0;a<n.length;a++){if(u=n[a],i=u.instance,s=u.currentTarget,u=u.listener,i!==o&&l.isPropagationStopped())break e;rc(l,u,s),o=i}}}if(Fo)throw e=Ei,Fo=!1,Ei=null,e}function se(e,t){var r=t[_i];r===void 0&&(r=t[_i]=new Set);var n=e+"__bubble";r.has(n)||(gf(t,e,2,!1),r.add(n))}function Ha(e,t,r){var n=0;t&&(n|=4),gf(r,e,n,t)}var Jl="_reactListening"+Math.random().toString(36).slice(2);function yl(e){if(!e[Jl]){e[Jl]=!0,Ed.forEach(function(r){r!=="selectionchange"&&(Zp.has(r)||Ha(r,!1,e),Ha(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Jl]||(t[Jl]=!0,Ha("selectionchange",!1,t))}}function gf(e,t,r,n){switch(Zd(t)){case 1:var l=hp;break;case 4:l=pp;break;default:l=Nu}r=l.bind(null,t,r,e),l=void 0,!Si||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),n?l!==void 0?e.addEventListener(t,r,{capture:!0,passive:l}):e.addEventListener(t,r,!0):l!==void 0?e.addEventListener(t,r,{passive:l}):e.addEventListener(t,r,!1)}function Wa(e,t,r,n,l){var o=n;if((t&1)===0&&(t&2)===0&&n!==null)e:for(;;){if(n===null)return;var a=n.tag;if(a===3||a===4){var u=n.stateNode.containerInfo;if(u===l||u.nodeType===8&&u.parentNode===l)break;if(a===4)for(a=n.return;a!==null;){var i=a.tag;if((i===3||i===4)&&(i=a.stateNode.containerInfo,i===l||i.nodeType===8&&i.parentNode===l))return;a=a.return}for(;u!==null;){if(a=Ar(u),a===null)return;if(i=a.tag,i===5||i===6){n=o=a;continue e}u=u.parentNode}}n=n.return}Od(function(){var s=o,d=ku(r),m=[];e:{var f=mf.get(e);if(f!==void 0){var w=Fu,k=e;switch(e){case"keypress":if(fo(r)===0)break e;case"keydown":case"keyup":w=Bp;break;case"focusin":k="focus",w=za;break;case"focusout":k="blur",w=za;break;case"beforeblur":case"afterblur":w=za;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=Hs;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=gp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=bp;break;case df:case ff:case hf:w=wp;break;case pf:w=Lp;break;case"scroll":w=mp;break;case"wheel":w=Mp;break;case"copy":case"cut":case"paste":w=Sp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=Qs}var N=(t&4)!==0,P=!N&&e==="scroll",p=N?f!==null?f+"Capture":null:f;N=[];for(var h=s,g;h!==null;){g=h;var S=g.stateNode;if(g.tag===5&&S!==null&&(g=S,p!==null&&(S=cl(h,p),S!=null&&N.push(gl(h,S,g)))),P)break;h=h.return}0<N.length&&(f=new w(f,k,null,r,d),m.push({event:f,listeners:N}))}}if((t&7)===0){e:{if(f=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",f&&r!==wi&&(k=r.relatedTarget||r.fromElement)&&(Ar(k)||k[Ot]))break e;if((w||f)&&(f=d.window===d?d:(f=d.ownerDocument)?f.defaultView||f.parentWindow:window,w?(k=r.relatedTarget||r.toElement,w=s,k=k?Ar(k):null,k!==null&&(P=Ur(k),k!==P||k.tag!==5&&k.tag!==6)&&(k=null)):(w=null,k=s),w!==k)){if(N=Hs,S="onMouseLeave",p="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(N=Qs,S="onPointerLeave",p="onPointerEnter",h="pointer"),P=w==null?f:nn(w),g=k==null?f:nn(k),f=new N(S,h+"leave",w,r,d),f.target=P,f.relatedTarget=g,S=null,Ar(d)===s&&(N=new N(p,h+"enter",k,r,d),N.target=g,N.relatedTarget=P,S=N),P=S,w&&k)t:{for(N=w,p=k,h=0,g=N;g;g=Gr(g))h++;for(g=0,S=p;S;S=Gr(S))g++;for(;0<h-g;)N=Gr(N),h--;for(;0<g-h;)p=Gr(p),g--;for(;h--;){if(N===p||p!==null&&N===p.alternate)break t;N=Gr(N),p=Gr(p)}N=null}else N=null;w!==null&&nc(m,f,w,N,!1),k!==null&&P!==null&&nc(m,P,k,N,!0)}}e:{if(f=s?nn(s):window,w=f.nodeName&&f.nodeName.toLowerCase(),w==="select"||w==="input"&&f.type==="file")var F=Vp;else if(Gs(f))if(of)F=Kp;else{F=Wp;var v=Hp}else(w=f.nodeName)&&w.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(F=Qp);if(F&&(F=F(e,s))){lf(m,F,r,d);break e}v&&v(e,f,s),e==="focusout"&&(v=f._wrapperState)&&v.controlled&&f.type==="number"&&mi(f,"number",f.value)}switch(v=s?nn(s):window,e){case"focusin":(Gs(v)||v.contentEditable==="true")&&(tn=v,Fi=s,nl=null);break;case"focusout":nl=Fi=tn=null;break;case"mousedown":Ri=!0;break;case"contextmenu":case"mouseup":case"dragend":Ri=!1,ec(m,r,d);break;case"selectionchange":if(Jp)break;case"keydown":case"keyup":ec(m,r,d)}var y;if(Bu)e:{switch(e){case"compositionstart":var B="onCompositionStart";break e;case"compositionend":B="onCompositionEnd";break e;case"compositionupdate":B="onCompositionUpdate";break e}B=void 0}else en?rf(e,r)&&(B="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(B="onCompositionStart");B&&(tf&&r.locale!=="ko"&&(en||B!=="onCompositionStart"?B==="onCompositionEnd"&&en&&(y=ef()):(rr=d,Du="value"in rr?rr.value:rr.textContent,en=!0)),v=bo(s,B),0<v.length&&(B=new Ws(B,e,null,r,d),m.push({event:B,listeners:v}),y?B.data=y:(y=nf(r),y!==null&&(B.data=y)))),(y=zp?Op(e,r):$p(e,r))&&(s=bo(s,"onBeforeInput"),0<s.length&&(d=new Ws("onBeforeInput","beforeinput",null,r,d),m.push({event:d,listeners:s}),d.data=y))}yf(m,t)})}function gl(e,t,r){return{instance:e,listener:t,currentTarget:r}}function bo(e,t){for(var r=t+"Capture",n=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=cl(e,r),o!=null&&n.unshift(gl(e,o,l)),o=cl(e,t),o!=null&&n.push(gl(e,o,l))),e=e.return}return n}function Gr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function nc(e,t,r,n,l){for(var o=t._reactName,a=[];r!==null&&r!==n;){var u=r,i=u.alternate,s=u.stateNode;if(i!==null&&i===n)break;u.tag===5&&s!==null&&(u=s,l?(i=cl(r,o),i!=null&&a.unshift(gl(r,i,u))):l||(i=cl(r,o),i!=null&&a.push(gl(r,i,u)))),r=r.return}a.length!==0&&e.push({event:t,listeners:a})}var em=/\r\n?/g,tm=/\u0000|\uFFFD/g;function lc(e){return(typeof e=="string"?e:""+e).replace(em,`
`).replace(tm,"")}function Xl(e,t,r){if(t=lc(t),lc(e)!==t&&r)throw Error(_(425))}function _o(){}var Bi=null,Ai=null;function Pi(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var bi=typeof setTimeout=="function"?setTimeout:void 0,rm=typeof clearTimeout=="function"?clearTimeout:void 0,oc=typeof Promise=="function"?Promise:void 0,nm=typeof queueMicrotask=="function"?queueMicrotask:typeof oc<"u"?function(e){return oc.resolve(null).then(e).catch(lm)}:bi;function lm(e){setTimeout(function(){throw e})}function Qa(e,t){var r=t,n=0;do{var l=r.nextSibling;if(e.removeChild(r),l&&l.nodeType===8)if(r=l.data,r==="/$"){if(n===0){e.removeChild(l),hl(t);return}n--}else r!=="$"&&r!=="$?"&&r!=="$!"||n++;r=l}while(r);hl(t)}function ir(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ac(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var Bn=Math.random().toString(36).slice(2),Rt="__reactFiber$"+Bn,vl="__reactProps$"+Bn,Ot="__reactContainer$"+Bn,_i="__reactEvents$"+Bn,om="__reactListeners$"+Bn,am="__reactHandles$"+Bn;function Ar(e){var t=e[Rt];if(t)return t;for(var r=e.parentNode;r;){if(t=r[Ot]||r[Rt]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=ac(e);e!==null;){if(r=e[Rt])return r;e=ac(e)}return t}e=r,r=e.parentNode}return null}function Pl(e){return e=e[Rt]||e[Ot],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function nn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(_(33))}function sa(e){return e[vl]||null}var Li=[],ln=-1;function yr(e){return{current:e}}function ce(e){0>ln||(e.current=Li[ln],Li[ln]=null,ln--)}function ue(e,t){ln++,Li[ln]=e.current,e.current=t}var hr={},ze=yr(hr),Ge=yr(!1),Mr=hr;function kn(e,t){var r=e.type.contextTypes;if(!r)return hr;var n=e.stateNode;if(n&&n.__reactInternalMemoizedUnmaskedChildContext===t)return n.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in r)l[o]=t[o];return n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Je(e){return e=e.childContextTypes,e!=null}function Lo(){ce(Ge),ce(ze)}function ic(e,t,r){if(ze.current!==hr)throw Error(_(168));ue(ze,t),ue(Ge,r)}function vf(e,t,r){var n=e.stateNode;if(t=t.childContextTypes,typeof n.getChildContext!="function")return r;n=n.getChildContext();for(var l in n)if(!(l in t))throw Error(_(108,H0(e)||"Unknown",l));return pe({},r,n)}function Io(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||hr,Mr=ze.current,ue(ze,e),ue(Ge,Ge.current),!0}function uc(e,t,r){var n=e.stateNode;if(!n)throw Error(_(169));r?(e=vf(e,t,Mr),n.__reactInternalMemoizedMergedChildContext=e,ce(Ge),ce(ze),ue(ze,e)):ce(Ge),ue(Ge,r)}var Lt=null,ca=!1,Ka=!1;function xf(e){Lt===null?Lt=[e]:Lt.push(e)}function im(e){ca=!0,xf(e)}function gr(){if(!Ka&&Lt!==null){Ka=!0;var e=0,t=oe;try{var r=Lt;for(oe=1;e<r.length;e++){var n=r[e];do n=n(!0);while(n!==null)}Lt=null,ca=!1}catch(l){throw Lt!==null&&(Lt=Lt.slice(e+1)),Vd(Su,gr),l}finally{oe=t,Ka=!1}}return null}var on=[],an=0,Mo=null,To=0,ct=[],dt=0,Tr=null,It=1,Mt="";function Fr(e,t){on[an++]=To,on[an++]=Mo,Mo=e,To=t}function wf(e,t,r){ct[dt++]=It,ct[dt++]=Mt,ct[dt++]=Tr,Tr=e;var n=It;e=Mt;var l=32-kt(n)-1;n&=~(1<<l),r+=1;var o=32-kt(t)+l;if(30<o){var a=l-l%5;o=(n&(1<<a)-1).toString(32),n>>=a,l-=a,It=1<<32-kt(t)+l|r<<l|n,Mt=o+e}else It=1<<o|r<<l|n,Mt=e}function Pu(e){e.return!==null&&(Fr(e,1),wf(e,1,0))}function bu(e){for(;e===Mo;)Mo=on[--an],on[an]=null,To=on[--an],on[an]=null;for(;e===Tr;)Tr=ct[--dt],ct[dt]=null,Mt=ct[--dt],ct[dt]=null,It=ct[--dt],ct[dt]=null}var nt=null,rt=null,de=!1,wt=null;function kf(e,t){var r=ft(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function sc(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,nt=e,rt=ir(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,nt=e,rt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=Tr!==null?{id:It,overflow:Mt}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=ft(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,nt=e,rt=null,!0):!1;default:return!1}}function Ii(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Mi(e){if(de){var t=rt;if(t){var r=t;if(!sc(e,t)){if(Ii(e))throw Error(_(418));t=ir(r.nextSibling);var n=nt;t&&sc(e,t)?kf(n,r):(e.flags=e.flags&-4097|2,de=!1,nt=e)}}else{if(Ii(e))throw Error(_(418));e.flags=e.flags&-4097|2,de=!1,nt=e}}}function cc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;nt=e}function ql(e){if(e!==nt)return!1;if(!de)return cc(e),de=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Pi(e.type,e.memoizedProps)),t&&(t=rt)){if(Ii(e))throw Sf(),Error(_(418));for(;t;)kf(e,t),t=ir(t.nextSibling)}if(cc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){rt=ir(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}rt=null}}else rt=nt?ir(e.stateNode.nextSibling):null;return!0}function Sf(){for(var e=rt;e;)e=ir(e.nextSibling)}function Sn(){rt=nt=null,de=!1}function _u(e){wt===null?wt=[e]:wt.push(e)}var um=Ut.ReactCurrentBatchConfig;function jn(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(_(309));var n=r.stateNode}if(!n)throw Error(_(147,e));var l=n,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(a){var u=l.refs;a===null?delete u[o]:u[o]=a},t._stringRef=o,t)}if(typeof e!="string")throw Error(_(284));if(!r._owner)throw Error(_(290,e))}return e}function Zl(e,t){throw e=Object.prototype.toString.call(t),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function dc(e){var t=e._init;return t(e._payload)}function Ef(e){function t(p,h){if(e){var g=p.deletions;g===null?(p.deletions=[h],p.flags|=16):g.push(h)}}function r(p,h){if(!e)return null;for(;h!==null;)t(p,h),h=h.sibling;return null}function n(p,h){for(p=new Map;h!==null;)h.key!==null?p.set(h.key,h):p.set(h.index,h),h=h.sibling;return p}function l(p,h){return p=dr(p,h),p.index=0,p.sibling=null,p}function o(p,h,g){return p.index=g,e?(g=p.alternate,g!==null?(g=g.index,g<h?(p.flags|=2,h):g):(p.flags|=2,h)):(p.flags|=1048576,h)}function a(p){return e&&p.alternate===null&&(p.flags|=2),p}function u(p,h,g,S){return h===null||h.tag!==6?(h=ei(g,p.mode,S),h.return=p,h):(h=l(h,g),h.return=p,h)}function i(p,h,g,S){var F=g.type;return F===Zr?d(p,h,g.props.children,S,g.key):h!==null&&(h.elementType===F||typeof F=="object"&&F!==null&&F.$$typeof===Xt&&dc(F)===h.type)?(S=l(h,g.props),S.ref=jn(p,h,g),S.return=p,S):(S=xo(g.type,g.key,g.props,null,p.mode,S),S.ref=jn(p,h,g),S.return=p,S)}function s(p,h,g,S){return h===null||h.tag!==4||h.stateNode.containerInfo!==g.containerInfo||h.stateNode.implementation!==g.implementation?(h=ti(g,p.mode,S),h.return=p,h):(h=l(h,g.children||[]),h.return=p,h)}function d(p,h,g,S,F){return h===null||h.tag!==7?(h=Ir(g,p.mode,S,F),h.return=p,h):(h=l(h,g),h.return=p,h)}function m(p,h,g){if(typeof h=="string"&&h!==""||typeof h=="number")return h=ei(""+h,p.mode,g),h.return=p,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Ul:return g=xo(h.type,h.key,h.props,null,p.mode,g),g.ref=jn(p,null,h),g.return=p,g;case qr:return h=ti(h,p.mode,g),h.return=p,h;case Xt:var S=h._init;return m(p,S(h._payload),g)}if(Yn(h)||Mn(h))return h=Ir(h,p.mode,g,null),h.return=p,h;Zl(p,h)}return null}function f(p,h,g,S){var F=h!==null?h.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return F!==null?null:u(p,h,""+g,S);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Ul:return g.key===F?i(p,h,g,S):null;case qr:return g.key===F?s(p,h,g,S):null;case Xt:return F=g._init,f(p,h,F(g._payload),S)}if(Yn(g)||Mn(g))return F!==null?null:d(p,h,g,S,null);Zl(p,g)}return null}function w(p,h,g,S,F){if(typeof S=="string"&&S!==""||typeof S=="number")return p=p.get(g)||null,u(h,p,""+S,F);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Ul:return p=p.get(S.key===null?g:S.key)||null,i(h,p,S,F);case qr:return p=p.get(S.key===null?g:S.key)||null,s(h,p,S,F);case Xt:var v=S._init;return w(p,h,g,v(S._payload),F)}if(Yn(S)||Mn(S))return p=p.get(g)||null,d(h,p,S,F,null);Zl(h,S)}return null}function k(p,h,g,S){for(var F=null,v=null,y=h,B=h=0,z=null;y!==null&&B<g.length;B++){y.index>B?(z=y,y=null):z=y.sibling;var $=f(p,y,g[B],S);if($===null){y===null&&(y=z);break}e&&y&&$.alternate===null&&t(p,y),h=o($,h,B),v===null?F=$:v.sibling=$,v=$,y=z}if(B===g.length)return r(p,y),de&&Fr(p,B),F;if(y===null){for(;B<g.length;B++)y=m(p,g[B],S),y!==null&&(h=o(y,h,B),v===null?F=y:v.sibling=y,v=y);return de&&Fr(p,B),F}for(y=n(p,y);B<g.length;B++)z=w(y,p,B,g[B],S),z!==null&&(e&&z.alternate!==null&&y.delete(z.key===null?B:z.key),h=o(z,h,B),v===null?F=z:v.sibling=z,v=z);return e&&y.forEach(function(G){return t(p,G)}),de&&Fr(p,B),F}function N(p,h,g,S){var F=Mn(g);if(typeof F!="function")throw Error(_(150));if(g=F.call(g),g==null)throw Error(_(151));for(var v=F=null,y=h,B=h=0,z=null,$=g.next();y!==null&&!$.done;B++,$=g.next()){y.index>B?(z=y,y=null):z=y.sibling;var G=f(p,y,$.value,S);if(G===null){y===null&&(y=z);break}e&&y&&G.alternate===null&&t(p,y),h=o(G,h,B),v===null?F=G:v.sibling=G,v=G,y=z}if($.done)return r(p,y),de&&Fr(p,B),F;if(y===null){for(;!$.done;B++,$=g.next())$=m(p,$.value,S),$!==null&&(h=o($,h,B),v===null?F=$:v.sibling=$,v=$);return de&&Fr(p,B),F}for(y=n(p,y);!$.done;B++,$=g.next())$=w(y,p,B,$.value,S),$!==null&&(e&&$.alternate!==null&&y.delete($.key===null?B:$.key),h=o($,h,B),v===null?F=$:v.sibling=$,v=$);return e&&y.forEach(function(ge){return t(p,ge)}),de&&Fr(p,B),F}function P(p,h,g,S){if(typeof g=="object"&&g!==null&&g.type===Zr&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case Ul:e:{for(var F=g.key,v=h;v!==null;){if(v.key===F){if(F=g.type,F===Zr){if(v.tag===7){r(p,v.sibling),h=l(v,g.props.children),h.return=p,p=h;break e}}else if(v.elementType===F||typeof F=="object"&&F!==null&&F.$$typeof===Xt&&dc(F)===v.type){r(p,v.sibling),h=l(v,g.props),h.ref=jn(p,v,g),h.return=p,p=h;break e}r(p,v);break}else t(p,v);v=v.sibling}g.type===Zr?(h=Ir(g.props.children,p.mode,S,g.key),h.return=p,p=h):(S=xo(g.type,g.key,g.props,null,p.mode,S),S.ref=jn(p,h,g),S.return=p,p=S)}return a(p);case qr:e:{for(v=g.key;h!==null;){if(h.key===v)if(h.tag===4&&h.stateNode.containerInfo===g.containerInfo&&h.stateNode.implementation===g.implementation){r(p,h.sibling),h=l(h,g.children||[]),h.return=p,p=h;break e}else{r(p,h);break}else t(p,h);h=h.sibling}h=ti(g,p.mode,S),h.return=p,p=h}return a(p);case Xt:return v=g._init,P(p,h,v(g._payload),S)}if(Yn(g))return k(p,h,g,S);if(Mn(g))return N(p,h,g,S);Zl(p,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,h!==null&&h.tag===6?(r(p,h.sibling),h=l(h,g),h.return=p,p=h):(r(p,h),h=ei(g,p.mode,S),h.return=p,p=h),a(p)):r(p,h)}return P}var En=Ef(!0),Cf=Ef(!1),zo=yr(null),Oo=null,un=null,Lu=null;function Iu(){Lu=un=Oo=null}function Mu(e){var t=zo.current;ce(zo),e._currentValue=t}function Ti(e,t,r){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===r)break;e=e.return}}function yn(e,t){Oo=e,Lu=un=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(Ye=!0),e.firstContext=null)}function pt(e){var t=e._currentValue;if(Lu!==e)if(e={context:e,memoizedValue:t,next:null},un===null){if(Oo===null)throw Error(_(308));un=e,Oo.dependencies={lanes:0,firstContext:e}}else un=un.next=e;return t}var Pr=null;function Tu(e){Pr===null?Pr=[e]:Pr.push(e)}function Nf(e,t,r,n){var l=t.interleaved;return l===null?(r.next=r,Tu(t)):(r.next=l.next,l.next=r),t.interleaved=r,$t(e,n)}function $t(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var qt=!1;function zu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Df(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Tt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function ur(e,t,r){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,(re&2)!==0){var l=n.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),n.pending=t,$t(e,r)}return l=n.interleaved,l===null?(t.next=t,Tu(n)):(t.next=l.next,l.next=t),n.interleaved=t,$t(e,r)}function ho(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Eu(e,r)}}function fc(e,t){var r=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,r===n)){var l=null,o=null;if(r=r.firstBaseUpdate,r!==null){do{var a={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};o===null?l=o=a:o=o.next=a,r=r.next}while(r!==null);o===null?l=o=t:o=o.next=t}else l=o=t;r={baseState:n.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:n.shared,effects:n.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function $o(e,t,r,n){var l=e.updateQueue;qt=!1;var o=l.firstBaseUpdate,a=l.lastBaseUpdate,u=l.shared.pending;if(u!==null){l.shared.pending=null;var i=u,s=i.next;i.next=null,a===null?o=s:a.next=s,a=i;var d=e.alternate;d!==null&&(d=d.updateQueue,u=d.lastBaseUpdate,u!==a&&(u===null?d.firstBaseUpdate=s:u.next=s,d.lastBaseUpdate=i))}if(o!==null){var m=l.baseState;a=0,d=s=i=null,u=o;do{var f=u.lane,w=u.eventTime;if((n&f)===f){d!==null&&(d=d.next={eventTime:w,lane:0,tag:u.tag,payload:u.payload,callback:u.callback,next:null});e:{var k=e,N=u;switch(f=t,w=r,N.tag){case 1:if(k=N.payload,typeof k=="function"){m=k.call(w,m,f);break e}m=k;break e;case 3:k.flags=k.flags&-65537|128;case 0:if(k=N.payload,f=typeof k=="function"?k.call(w,m,f):k,f==null)break e;m=pe({},m,f);break e;case 2:qt=!0}}u.callback!==null&&u.lane!==0&&(e.flags|=64,f=l.effects,f===null?l.effects=[u]:f.push(u))}else w={eventTime:w,lane:f,tag:u.tag,payload:u.payload,callback:u.callback,next:null},d===null?(s=d=w,i=m):d=d.next=w,a|=f;if(u=u.next,u===null){if(u=l.shared.pending,u===null)break;f=u,u=f.next,f.next=null,l.lastBaseUpdate=f,l.shared.pending=null}}while(1);if(d===null&&(i=m),l.baseState=i,l.firstBaseUpdate=s,l.lastBaseUpdate=d,t=l.shared.interleaved,t!==null){l=t;do a|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);Or|=a,e.lanes=a,e.memoizedState=m}}function hc(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var n=e[t],l=n.callback;if(l!==null){if(n.callback=null,n=r,typeof l!="function")throw Error(_(191,l));l.call(n)}}}var bl={},At=yr(bl),xl=yr(bl),wl=yr(bl);function br(e){if(e===bl)throw Error(_(174));return e}function Ou(e,t){switch(ue(wl,t),ue(xl,e),ue(At,bl),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:gi(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=gi(t,e)}ce(At),ue(At,t)}function Cn(){ce(At),ce(xl),ce(wl)}function Ff(e){br(wl.current);var t=br(At.current),r=gi(t,e.type);t!==r&&(ue(xl,e),ue(At,r))}function $u(e){xl.current===e&&(ce(At),ce(xl))}var fe=yr(0);function jo(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ya=[];function ju(){for(var e=0;e<Ya.length;e++)Ya[e]._workInProgressVersionPrimary=null;Ya.length=0}var po=Ut.ReactCurrentDispatcher,Ga=Ut.ReactCurrentBatchConfig,zr=0,he=null,De=null,Be=null,Uo=!1,ll=!1,kl=0,sm=0;function Ie(){throw Error(_(321))}function Uu(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!Et(e[r],t[r]))return!1;return!0}function Vu(e,t,r,n,l,o){if(zr=o,he=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,po.current=e===null||e.memoizedState===null?hm:pm,e=r(n,l),ll){o=0;do{if(ll=!1,kl=0,25<=o)throw Error(_(301));o+=1,Be=De=null,t.updateQueue=null,po.current=mm,e=r(n,l)}while(ll)}if(po.current=Vo,t=De!==null&&De.next!==null,zr=0,Be=De=he=null,Uo=!1,t)throw Error(_(300));return e}function Hu(){var e=kl!==0;return kl=0,e}function Ft(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Be===null?he.memoizedState=Be=e:Be=Be.next=e,Be}function mt(){if(De===null){var e=he.alternate;e=e!==null?e.memoizedState:null}else e=De.next;var t=Be===null?he.memoizedState:Be.next;if(t!==null)Be=t,De=e;else{if(e===null)throw Error(_(310));De=e,e={memoizedState:De.memoizedState,baseState:De.baseState,baseQueue:De.baseQueue,queue:De.queue,next:null},Be===null?he.memoizedState=Be=e:Be=Be.next=e}return Be}function Sl(e,t){return typeof t=="function"?t(e):t}function Ja(e){var t=mt(),r=t.queue;if(r===null)throw Error(_(311));r.lastRenderedReducer=e;var n=De,l=n.baseQueue,o=r.pending;if(o!==null){if(l!==null){var a=l.next;l.next=o.next,o.next=a}n.baseQueue=l=o,r.pending=null}if(l!==null){o=l.next,n=n.baseState;var u=a=null,i=null,s=o;do{var d=s.lane;if((zr&d)===d)i!==null&&(i=i.next={lane:0,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null}),n=s.hasEagerState?s.eagerState:e(n,s.action);else{var m={lane:d,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null};i===null?(u=i=m,a=n):i=i.next=m,he.lanes|=d,Or|=d}s=s.next}while(s!==null&&s!==o);i===null?a=n:i.next=u,Et(n,t.memoizedState)||(Ye=!0),t.memoizedState=n,t.baseState=a,t.baseQueue=i,r.lastRenderedState=n}if(e=r.interleaved,e!==null){l=e;do o=l.lane,he.lanes|=o,Or|=o,l=l.next;while(l!==e)}else l===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function Xa(e){var t=mt(),r=t.queue;if(r===null)throw Error(_(311));r.lastRenderedReducer=e;var n=r.dispatch,l=r.pending,o=t.memoizedState;if(l!==null){r.pending=null;var a=l=l.next;do o=e(o,a.action),a=a.next;while(a!==l);Et(o,t.memoizedState)||(Ye=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),r.lastRenderedState=o}return[o,n]}function Rf(){}function Bf(e,t){var r=he,n=mt(),l=t(),o=!Et(n.memoizedState,l);if(o&&(n.memoizedState=l,Ye=!0),n=n.queue,Wu(bf.bind(null,r,n,e),[e]),n.getSnapshot!==t||o||Be!==null&&Be.memoizedState.tag&1){if(r.flags|=2048,El(9,Pf.bind(null,r,n,l,t),void 0,null),Ae===null)throw Error(_(349));(zr&30)!==0||Af(r,t,l)}return l}function Af(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=he.updateQueue,t===null?(t={lastEffect:null,stores:null},he.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function Pf(e,t,r,n){t.value=r,t.getSnapshot=n,_f(t)&&Lf(e)}function bf(e,t,r){return r(function(){_f(t)&&Lf(e)})}function _f(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!Et(e,r)}catch{return!0}}function Lf(e){var t=$t(e,1);t!==null&&St(t,e,1,-1)}function pc(e){var t=Ft();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Sl,lastRenderedState:e},t.queue=e,e=e.dispatch=fm.bind(null,he,e),[t.memoizedState,e]}function El(e,t,r,n){return e={tag:e,create:t,destroy:r,deps:n,next:null},t=he.updateQueue,t===null?(t={lastEffect:null,stores:null},he.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e)),e}function If(){return mt().memoizedState}function mo(e,t,r,n){var l=Ft();he.flags|=e,l.memoizedState=El(1|t,r,void 0,n===void 0?null:n)}function da(e,t,r,n){var l=mt();n=n===void 0?null:n;var o=void 0;if(De!==null){var a=De.memoizedState;if(o=a.destroy,n!==null&&Uu(n,a.deps)){l.memoizedState=El(t,r,o,n);return}}he.flags|=e,l.memoizedState=El(1|t,r,o,n)}function mc(e,t){return mo(8390656,8,e,t)}function Wu(e,t){return da(2048,8,e,t)}function Mf(e,t){return da(4,2,e,t)}function Tf(e,t){return da(4,4,e,t)}function zf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Of(e,t,r){return r=r!=null?r.concat([e]):null,da(4,4,zf.bind(null,t,e),r)}function Qu(){}function $f(e,t){var r=mt();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&Uu(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function jf(e,t){var r=mt();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&Uu(t,n[1])?n[0]:(e=e(),r.memoizedState=[e,t],e)}function Uf(e,t,r){return(zr&21)===0?(e.baseState&&(e.baseState=!1,Ye=!0),e.memoizedState=r):(Et(r,t)||(r=Qd(),he.lanes|=r,Or|=r,e.baseState=!0),t)}function cm(e,t){var r=oe;oe=r!==0&&4>r?r:4,e(!0);var n=Ga.transition;Ga.transition={};try{e(!1),t()}finally{oe=r,Ga.transition=n}}function Vf(){return mt().memoizedState}function dm(e,t,r){var n=cr(e);if(r={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null},Hf(e))Wf(t,r);else if(r=Nf(e,t,r,n),r!==null){var l=He();St(r,e,n,l),Qf(r,t,n)}}function fm(e,t,r){var n=cr(e),l={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null};if(Hf(e))Wf(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var a=t.lastRenderedState,u=o(a,r);if(l.hasEagerState=!0,l.eagerState=u,Et(u,a)){var i=t.interleaved;i===null?(l.next=l,Tu(t)):(l.next=i.next,i.next=l),t.interleaved=l;return}}catch{}finally{}r=Nf(e,t,l,n),r!==null&&(l=He(),St(r,e,n,l),Qf(r,t,n))}}function Hf(e){var t=e.alternate;return e===he||t!==null&&t===he}function Wf(e,t){ll=Uo=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function Qf(e,t,r){if((r&4194240)!==0){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Eu(e,r)}}var Vo={readContext:pt,useCallback:Ie,useContext:Ie,useEffect:Ie,useImperativeHandle:Ie,useInsertionEffect:Ie,useLayoutEffect:Ie,useMemo:Ie,useReducer:Ie,useRef:Ie,useState:Ie,useDebugValue:Ie,useDeferredValue:Ie,useTransition:Ie,useMutableSource:Ie,useSyncExternalStore:Ie,useId:Ie,unstable_isNewReconciler:!1},hm={readContext:pt,useCallback:function(e,t){return Ft().memoizedState=[e,t===void 0?null:t],e},useContext:pt,useEffect:mc,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,mo(4194308,4,zf.bind(null,t,e),r)},useLayoutEffect:function(e,t){return mo(4194308,4,e,t)},useInsertionEffect:function(e,t){return mo(4,2,e,t)},useMemo:function(e,t){var r=Ft();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var n=Ft();return t=r!==void 0?r(t):t,n.memoizedState=n.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},n.queue=e,e=e.dispatch=dm.bind(null,he,e),[n.memoizedState,e]},useRef:function(e){var t=Ft();return e={current:e},t.memoizedState=e},useState:pc,useDebugValue:Qu,useDeferredValue:function(e){return Ft().memoizedState=e},useTransition:function(){var e=pc(!1),t=e[0];return e=cm.bind(null,e[1]),Ft().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var n=he,l=Ft();if(de){if(r===void 0)throw Error(_(407));r=r()}else{if(r=t(),Ae===null)throw Error(_(349));(zr&30)!==0||Af(n,t,r)}l.memoizedState=r;var o={value:r,getSnapshot:t};return l.queue=o,mc(bf.bind(null,n,o,e),[e]),n.flags|=2048,El(9,Pf.bind(null,n,o,r,t),void 0,null),r},useId:function(){var e=Ft(),t=Ae.identifierPrefix;if(de){var r=Mt,n=It;r=(n&~(1<<32-kt(n)-1)).toString(32)+r,t=":"+t+"R"+r,r=kl++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=sm++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},pm={readContext:pt,useCallback:$f,useContext:pt,useEffect:Wu,useImperativeHandle:Of,useInsertionEffect:Mf,useLayoutEffect:Tf,useMemo:jf,useReducer:Ja,useRef:If,useState:function(){return Ja(Sl)},useDebugValue:Qu,useDeferredValue:function(e){var t=mt();return Uf(t,De.memoizedState,e)},useTransition:function(){var e=Ja(Sl)[0],t=mt().memoizedState;return[e,t]},useMutableSource:Rf,useSyncExternalStore:Bf,useId:Vf,unstable_isNewReconciler:!1},mm={readContext:pt,useCallback:$f,useContext:pt,useEffect:Wu,useImperativeHandle:Of,useInsertionEffect:Mf,useLayoutEffect:Tf,useMemo:jf,useReducer:Xa,useRef:If,useState:function(){return Xa(Sl)},useDebugValue:Qu,useDeferredValue:function(e){var t=mt();return De===null?t.memoizedState=e:Uf(t,De.memoizedState,e)},useTransition:function(){var e=Xa(Sl)[0],t=mt().memoizedState;return[e,t]},useMutableSource:Rf,useSyncExternalStore:Bf,useId:Vf,unstable_isNewReconciler:!1};function vt(e,t){if(e&&e.defaultProps){t=pe({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function zi(e,t,r,n){t=e.memoizedState,r=r(n,t),r=r==null?t:pe({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var fa={isMounted:function(e){return(e=e._reactInternals)?Ur(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var n=He(),l=cr(e),o=Tt(n,l);o.payload=t,r!=null&&(o.callback=r),t=ur(e,o,l),t!==null&&(St(t,e,l,n),ho(t,e,l))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var n=He(),l=cr(e),o=Tt(n,l);o.tag=1,o.payload=t,r!=null&&(o.callback=r),t=ur(e,o,l),t!==null&&(St(t,e,l,n),ho(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=He(),n=cr(e),l=Tt(r,n);l.tag=2,t!=null&&(l.callback=t),t=ur(e,l,n),t!==null&&(St(t,e,n,r),ho(t,e,n))}};function yc(e,t,r,n,l,o,a){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,o,a):t.prototype&&t.prototype.isPureReactComponent?!ml(r,n)||!ml(l,o):!0}function Kf(e,t,r){var n=!1,l=hr,o=t.contextType;return typeof o=="object"&&o!==null?o=pt(o):(l=Je(t)?Mr:ze.current,n=t.contextTypes,o=(n=n!=null)?kn(e,l):hr),t=new t(r,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=fa,e.stateNode=t,t._reactInternals=e,n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function gc(e,t,r,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&fa.enqueueReplaceState(t,t.state,null)}function Oi(e,t,r,n){var l=e.stateNode;l.props=r,l.state=e.memoizedState,l.refs={},zu(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=pt(o):(o=Je(t)?Mr:ze.current,l.context=kn(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(zi(e,t,o,r),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&fa.enqueueReplaceState(l,l.state,null),$o(e,r,l,n),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function Nn(e,t){try{var r="",n=t;do r+=V0(n),n=n.return;while(n);var l=r}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function qa(e,t,r){return{value:e,source:null,stack:r!=null?r:null,digest:t!=null?t:null}}function $i(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var ym=typeof WeakMap=="function"?WeakMap:Map;function Yf(e,t,r){r=Tt(-1,r),r.tag=3,r.payload={element:null};var n=t.value;return r.callback=function(){Wo||(Wo=!0,Ji=n),$i(e,t)},r}function Gf(e,t,r){r=Tt(-1,r),r.tag=3;var n=e.type.getDerivedStateFromError;if(typeof n=="function"){var l=t.value;r.payload=function(){return n(l)},r.callback=function(){$i(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(r.callback=function(){$i(e,t),typeof n!="function"&&(sr===null?sr=new Set([this]):sr.add(this));var a=t.stack;this.componentDidCatch(t.value,{componentStack:a!==null?a:""})}),r}function vc(e,t,r){var n=e.pingCache;if(n===null){n=e.pingCache=new ym;var l=new Set;n.set(t,l)}else l=n.get(t),l===void 0&&(l=new Set,n.set(t,l));l.has(r)||(l.add(r),e=Am.bind(null,e,t,r),t.then(e,e))}function xc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function wc(e,t,r,n,l){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=Tt(-1,1),t.tag=2,ur(r,t,1))),r.lanes|=1),e):(e.flags|=65536,e.lanes=l,e)}var gm=Ut.ReactCurrentOwner,Ye=!1;function Ve(e,t,r,n){t.child=e===null?Cf(t,null,r,n):En(t,e.child,r,n)}function kc(e,t,r,n,l){r=r.render;var o=t.ref;return yn(t,l),n=Vu(e,t,r,n,o,l),r=Hu(),e!==null&&!Ye?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,jt(e,t,l)):(de&&r&&Pu(t),t.flags|=1,Ve(e,t,n,l),t.child)}function Sc(e,t,r,n,l){if(e===null){var o=r.type;return typeof o=="function"&&!es(o)&&o.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=o,Jf(e,t,o,n,l)):(e=xo(r.type,null,n,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,(e.lanes&l)===0){var a=o.memoizedProps;if(r=r.compare,r=r!==null?r:ml,r(a,n)&&e.ref===t.ref)return jt(e,t,l)}return t.flags|=1,e=dr(o,n),e.ref=t.ref,e.return=t,t.child=e}function Jf(e,t,r,n,l){if(e!==null){var o=e.memoizedProps;if(ml(o,n)&&e.ref===t.ref)if(Ye=!1,t.pendingProps=n=o,(e.lanes&l)!==0)(e.flags&131072)!==0&&(Ye=!0);else return t.lanes=e.lanes,jt(e,t,l)}return ji(e,t,r,n,l)}function Xf(e,t,r){var n=t.pendingProps,l=n.children,o=e!==null?e.memoizedState:null;if(n.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ue(cn,Ze),Ze|=r;else{if((r&1073741824)===0)return e=o!==null?o.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ue(cn,Ze),Ze|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},n=o!==null?o.baseLanes:r,ue(cn,Ze),Ze|=n}else o!==null?(n=o.baseLanes|r,t.memoizedState=null):n=r,ue(cn,Ze),Ze|=n;return Ve(e,t,l,r),t.child}function qf(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function ji(e,t,r,n,l){var o=Je(r)?Mr:ze.current;return o=kn(t,o),yn(t,l),r=Vu(e,t,r,n,o,l),n=Hu(),e!==null&&!Ye?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,jt(e,t,l)):(de&&n&&Pu(t),t.flags|=1,Ve(e,t,r,l),t.child)}function Ec(e,t,r,n,l){if(Je(r)){var o=!0;Io(t)}else o=!1;if(yn(t,l),t.stateNode===null)yo(e,t),Kf(t,r,n),Oi(t,r,n,l),n=!0;else if(e===null){var a=t.stateNode,u=t.memoizedProps;a.props=u;var i=a.context,s=r.contextType;typeof s=="object"&&s!==null?s=pt(s):(s=Je(r)?Mr:ze.current,s=kn(t,s));var d=r.getDerivedStateFromProps,m=typeof d=="function"||typeof a.getSnapshotBeforeUpdate=="function";m||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(u!==n||i!==s)&&gc(t,a,n,s),qt=!1;var f=t.memoizedState;a.state=f,$o(t,n,a,l),i=t.memoizedState,u!==n||f!==i||Ge.current||qt?(typeof d=="function"&&(zi(t,r,d,n),i=t.memoizedState),(u=qt||yc(t,r,u,n,f,i,s))?(m||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount()),typeof a.componentDidMount=="function"&&(t.flags|=4194308)):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=i),a.props=n,a.state=i,a.context=s,n=u):(typeof a.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{a=t.stateNode,Df(e,t),u=t.memoizedProps,s=t.type===t.elementType?u:vt(t.type,u),a.props=s,m=t.pendingProps,f=a.context,i=r.contextType,typeof i=="object"&&i!==null?i=pt(i):(i=Je(r)?Mr:ze.current,i=kn(t,i));var w=r.getDerivedStateFromProps;(d=typeof w=="function"||typeof a.getSnapshotBeforeUpdate=="function")||typeof a.UNSAFE_componentWillReceiveProps!="function"&&typeof a.componentWillReceiveProps!="function"||(u!==m||f!==i)&&gc(t,a,n,i),qt=!1,f=t.memoizedState,a.state=f,$o(t,n,a,l);var k=t.memoizedState;u!==m||f!==k||Ge.current||qt?(typeof w=="function"&&(zi(t,r,w,n),k=t.memoizedState),(s=qt||yc(t,r,s,n,f,k,i)||!1)?(d||typeof a.UNSAFE_componentWillUpdate!="function"&&typeof a.componentWillUpdate!="function"||(typeof a.componentWillUpdate=="function"&&a.componentWillUpdate(n,k,i),typeof a.UNSAFE_componentWillUpdate=="function"&&a.UNSAFE_componentWillUpdate(n,k,i)),typeof a.componentDidUpdate=="function"&&(t.flags|=4),typeof a.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof a.componentDidUpdate!="function"||u===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=k),a.props=n,a.state=k,a.context=i,n=s):(typeof a.componentDidUpdate!="function"||u===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof a.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),n=!1)}return Ui(e,t,r,n,o,l)}function Ui(e,t,r,n,l,o){qf(e,t);var a=(t.flags&128)!==0;if(!n&&!a)return l&&uc(t,r,!1),jt(e,t,o);n=t.stateNode,gm.current=t;var u=a&&typeof r.getDerivedStateFromError!="function"?null:n.render();return t.flags|=1,e!==null&&a?(t.child=En(t,e.child,null,o),t.child=En(t,null,u,o)):Ve(e,t,u,o),t.memoizedState=n.state,l&&uc(t,r,!0),t.child}function Zf(e){var t=e.stateNode;t.pendingContext?ic(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ic(e,t.context,!1),Ou(e,t.containerInfo)}function Cc(e,t,r,n,l){return Sn(),_u(l),t.flags|=256,Ve(e,t,r,n),t.child}var Vi={dehydrated:null,treeContext:null,retryLane:0};function Hi(e){return{baseLanes:e,cachePool:null,transitions:null}}function eh(e,t,r){var n=t.pendingProps,l=fe.current,o=!1,a=(t.flags&128)!==0,u;if((u=a)||(u=e!==null&&e.memoizedState===null?!1:(l&2)!==0),u?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),ue(fe,l&1),e===null)return Mi(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(a=n.children,e=n.fallback,o?(n=t.mode,o=t.child,a={mode:"hidden",children:a},(n&1)===0&&o!==null?(o.childLanes=0,o.pendingProps=a):o=ma(a,n,0,null),e=Ir(e,n,r,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Hi(r),t.memoizedState=Vi,e):Ku(t,a));if(l=e.memoizedState,l!==null&&(u=l.dehydrated,u!==null))return vm(e,t,a,n,u,l,r);if(o){o=n.fallback,a=t.mode,l=e.child,u=l.sibling;var i={mode:"hidden",children:n.children};return(a&1)===0&&t.child!==l?(n=t.child,n.childLanes=0,n.pendingProps=i,t.deletions=null):(n=dr(l,i),n.subtreeFlags=l.subtreeFlags&14680064),u!==null?o=dr(u,o):(o=Ir(o,a,r,null),o.flags|=2),o.return=t,n.return=t,n.sibling=o,t.child=n,n=o,o=t.child,a=e.child.memoizedState,a=a===null?Hi(r):{baseLanes:a.baseLanes|r,cachePool:null,transitions:a.transitions},o.memoizedState=a,o.childLanes=e.childLanes&~r,t.memoizedState=Vi,n}return o=e.child,e=o.sibling,n=dr(o,{mode:"visible",children:n.children}),(t.mode&1)===0&&(n.lanes=r),n.return=t,n.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n}function Ku(e,t){return t=ma({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function eo(e,t,r,n){return n!==null&&_u(n),En(t,e.child,null,r),e=Ku(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function vm(e,t,r,n,l,o,a){if(r)return t.flags&256?(t.flags&=-257,n=qa(Error(_(422))),eo(e,t,a,n)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=n.fallback,l=t.mode,n=ma({mode:"visible",children:n.children},l,0,null),o=Ir(o,l,a,null),o.flags|=2,n.return=t,o.return=t,n.sibling=o,t.child=n,(t.mode&1)!==0&&En(t,e.child,null,a),t.child.memoizedState=Hi(a),t.memoizedState=Vi,o);if((t.mode&1)===0)return eo(e,t,a,null);if(l.data==="$!"){if(n=l.nextSibling&&l.nextSibling.dataset,n)var u=n.dgst;return n=u,o=Error(_(419)),n=qa(o,n,void 0),eo(e,t,a,n)}if(u=(a&e.childLanes)!==0,Ye||u){if(n=Ae,n!==null){switch(a&-a){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=(l&(n.suspendedLanes|a))!==0?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,$t(e,l),St(n,e,l,-1))}return Zu(),n=qa(Error(_(421))),eo(e,t,a,n)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=Pm.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,rt=ir(l.nextSibling),nt=t,de=!0,wt=null,e!==null&&(ct[dt++]=It,ct[dt++]=Mt,ct[dt++]=Tr,It=e.id,Mt=e.overflow,Tr=t),t=Ku(t,n.children),t.flags|=4096,t)}function Nc(e,t,r){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),Ti(e.return,t,r)}function Za(e,t,r,n,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=n,o.tail=r,o.tailMode=l)}function th(e,t,r){var n=t.pendingProps,l=n.revealOrder,o=n.tail;if(Ve(e,t,n.children,r),n=fe.current,(n&2)!==0)n=n&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Nc(e,r,t);else if(e.tag===19)Nc(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}if(ue(fe,n),(t.mode&1)===0)t.memoizedState=null;else switch(l){case"forwards":for(r=t.child,l=null;r!==null;)e=r.alternate,e!==null&&jo(e)===null&&(l=r),r=r.sibling;r=l,r===null?(l=t.child,t.child=null):(l=r.sibling,r.sibling=null),Za(t,!1,l,r,o);break;case"backwards":for(r=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&jo(e)===null){t.child=l;break}e=l.sibling,l.sibling=r,r=l,l=e}Za(t,!0,r,null,o);break;case"together":Za(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function yo(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function jt(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),Or|=t.lanes,(r&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(_(153));if(t.child!==null){for(e=t.child,r=dr(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=dr(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function xm(e,t,r){switch(t.tag){case 3:Zf(t),Sn();break;case 5:Ff(t);break;case 1:Je(t.type)&&Io(t);break;case 4:Ou(t,t.stateNode.containerInfo);break;case 10:var n=t.type._context,l=t.memoizedProps.value;ue(zo,n._currentValue),n._currentValue=l;break;case 13:if(n=t.memoizedState,n!==null)return n.dehydrated!==null?(ue(fe,fe.current&1),t.flags|=128,null):(r&t.child.childLanes)!==0?eh(e,t,r):(ue(fe,fe.current&1),e=jt(e,t,r),e!==null?e.sibling:null);ue(fe,fe.current&1);break;case 19:if(n=(r&t.childLanes)!==0,(e.flags&128)!==0){if(n)return th(e,t,r);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),ue(fe,fe.current),n)break;return null;case 22:case 23:return t.lanes=0,Xf(e,t,r)}return jt(e,t,r)}var rh,Wi,nh,lh;rh=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};Wi=function(){};nh=function(e,t,r,n){var l=e.memoizedProps;if(l!==n){e=t.stateNode,br(At.current);var o=null;switch(r){case"input":l=hi(e,l),n=hi(e,n),o=[];break;case"select":l=pe({},l,{value:void 0}),n=pe({},n,{value:void 0}),o=[];break;case"textarea":l=yi(e,l),n=yi(e,n),o=[];break;default:typeof l.onClick!="function"&&typeof n.onClick=="function"&&(e.onclick=_o)}vi(r,n);var a;r=null;for(s in l)if(!n.hasOwnProperty(s)&&l.hasOwnProperty(s)&&l[s]!=null)if(s==="style"){var u=l[s];for(a in u)u.hasOwnProperty(a)&&(r||(r={}),r[a]="")}else s!=="dangerouslySetInnerHTML"&&s!=="children"&&s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(ul.hasOwnProperty(s)?o||(o=[]):(o=o||[]).push(s,null));for(s in n){var i=n[s];if(u=l!=null?l[s]:void 0,n.hasOwnProperty(s)&&i!==u&&(i!=null||u!=null))if(s==="style")if(u){for(a in u)!u.hasOwnProperty(a)||i&&i.hasOwnProperty(a)||(r||(r={}),r[a]="");for(a in i)i.hasOwnProperty(a)&&u[a]!==i[a]&&(r||(r={}),r[a]=i[a])}else r||(o||(o=[]),o.push(s,r)),r=i;else s==="dangerouslySetInnerHTML"?(i=i?i.__html:void 0,u=u?u.__html:void 0,i!=null&&u!==i&&(o=o||[]).push(s,i)):s==="children"?typeof i!="string"&&typeof i!="number"||(o=o||[]).push(s,""+i):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&(ul.hasOwnProperty(s)?(i!=null&&s==="onScroll"&&se("scroll",e),o||u===i||(o=[])):(o=o||[]).push(s,i))}r&&(o=o||[]).push("style",r);var s=o;(t.updateQueue=s)&&(t.flags|=4)}};lh=function(e,t,r,n){r!==n&&(t.flags|=4)};function Un(e,t){if(!de)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;r!==null;)r.alternate!==null&&(n=r),r=r.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function Me(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,n=0;if(t)for(var l=e.child;l!==null;)r|=l.lanes|l.childLanes,n|=l.subtreeFlags&14680064,n|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)r|=l.lanes|l.childLanes,n|=l.subtreeFlags,n|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=n,e.childLanes=r,t}function wm(e,t,r){var n=t.pendingProps;switch(bu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Me(t),null;case 1:return Je(t.type)&&Lo(),Me(t),null;case 3:return n=t.stateNode,Cn(),ce(Ge),ce(ze),ju(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(ql(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,wt!==null&&(Zi(wt),wt=null))),Wi(e,t),Me(t),null;case 5:$u(t);var l=br(wl.current);if(r=t.type,e!==null&&t.stateNode!=null)nh(e,t,r,n,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!n){if(t.stateNode===null)throw Error(_(166));return Me(t),null}if(e=br(At.current),ql(t)){n=t.stateNode,r=t.type;var o=t.memoizedProps;switch(n[Rt]=t,n[vl]=o,e=(t.mode&1)!==0,r){case"dialog":se("cancel",n),se("close",n);break;case"iframe":case"object":case"embed":se("load",n);break;case"video":case"audio":for(l=0;l<Jn.length;l++)se(Jn[l],n);break;case"source":se("error",n);break;case"img":case"image":case"link":se("error",n),se("load",n);break;case"details":se("toggle",n);break;case"input":Ls(n,o),se("invalid",n);break;case"select":n._wrapperState={wasMultiple:!!o.multiple},se("invalid",n);break;case"textarea":Ms(n,o),se("invalid",n)}vi(r,o),l=null;for(var a in o)if(o.hasOwnProperty(a)){var u=o[a];a==="children"?typeof u=="string"?n.textContent!==u&&(o.suppressHydrationWarning!==!0&&Xl(n.textContent,u,e),l=["children",u]):typeof u=="number"&&n.textContent!==""+u&&(o.suppressHydrationWarning!==!0&&Xl(n.textContent,u,e),l=["children",""+u]):ul.hasOwnProperty(a)&&u!=null&&a==="onScroll"&&se("scroll",n)}switch(r){case"input":Vl(n),Is(n,o,!0);break;case"textarea":Vl(n),Ts(n);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(n.onclick=_o)}n=l,t.updateQueue=n,n!==null&&(t.flags|=4)}else{a=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Pd(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=a.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof n.is=="string"?e=a.createElement(r,{is:n.is}):(e=a.createElement(r),r==="select"&&(a=e,n.multiple?a.multiple=!0:n.size&&(a.size=n.size))):e=a.createElementNS(e,r),e[Rt]=t,e[vl]=n,rh(e,t,!1,!1),t.stateNode=e;e:{switch(a=xi(r,n),r){case"dialog":se("cancel",e),se("close",e),l=n;break;case"iframe":case"object":case"embed":se("load",e),l=n;break;case"video":case"audio":for(l=0;l<Jn.length;l++)se(Jn[l],e);l=n;break;case"source":se("error",e),l=n;break;case"img":case"image":case"link":se("error",e),se("load",e),l=n;break;case"details":se("toggle",e),l=n;break;case"input":Ls(e,n),l=hi(e,n),se("invalid",e);break;case"option":l=n;break;case"select":e._wrapperState={wasMultiple:!!n.multiple},l=pe({},n,{value:void 0}),se("invalid",e);break;case"textarea":Ms(e,n),l=yi(e,n),se("invalid",e);break;default:l=n}vi(r,l),u=l;for(o in u)if(u.hasOwnProperty(o)){var i=u[o];o==="style"?Ld(e,i):o==="dangerouslySetInnerHTML"?(i=i?i.__html:void 0,i!=null&&bd(e,i)):o==="children"?typeof i=="string"?(r!=="textarea"||i!=="")&&sl(e,i):typeof i=="number"&&sl(e,""+i):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(ul.hasOwnProperty(o)?i!=null&&o==="onScroll"&&se("scroll",e):i!=null&&gu(e,o,i,a))}switch(r){case"input":Vl(e),Is(e,n,!1);break;case"textarea":Vl(e),Ts(e);break;case"option":n.value!=null&&e.setAttribute("value",""+fr(n.value));break;case"select":e.multiple=!!n.multiple,o=n.value,o!=null?fn(e,!!n.multiple,o,!1):n.defaultValue!=null&&fn(e,!!n.multiple,n.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=_o)}switch(r){case"button":case"input":case"select":case"textarea":n=!!n.autoFocus;break e;case"img":n=!0;break e;default:n=!1}}n&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Me(t),null;case 6:if(e&&t.stateNode!=null)lh(e,t,e.memoizedProps,n);else{if(typeof n!="string"&&t.stateNode===null)throw Error(_(166));if(r=br(wl.current),br(At.current),ql(t)){if(n=t.stateNode,r=t.memoizedProps,n[Rt]=t,(o=n.nodeValue!==r)&&(e=nt,e!==null))switch(e.tag){case 3:Xl(n.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Xl(n.nodeValue,r,(e.mode&1)!==0)}o&&(t.flags|=4)}else n=(r.nodeType===9?r:r.ownerDocument).createTextNode(n),n[Rt]=t,t.stateNode=n}return Me(t),null;case 13:if(ce(fe),n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(de&&rt!==null&&(t.mode&1)!==0&&(t.flags&128)===0)Sf(),Sn(),t.flags|=98560,o=!1;else if(o=ql(t),n!==null&&n.dehydrated!==null){if(e===null){if(!o)throw Error(_(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(_(317));o[Rt]=t}else Sn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Me(t),o=!1}else wt!==null&&(Zi(wt),wt=null),o=!0;if(!o)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=r,t):(n=n!==null,n!==(e!==null&&e.memoizedState!==null)&&n&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(fe.current&1)!==0?Fe===0&&(Fe=3):Zu())),t.updateQueue!==null&&(t.flags|=4),Me(t),null);case 4:return Cn(),Wi(e,t),e===null&&yl(t.stateNode.containerInfo),Me(t),null;case 10:return Mu(t.type._context),Me(t),null;case 17:return Je(t.type)&&Lo(),Me(t),null;case 19:if(ce(fe),o=t.memoizedState,o===null)return Me(t),null;if(n=(t.flags&128)!==0,a=o.rendering,a===null)if(n)Un(o,!1);else{if(Fe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(a=jo(e),a!==null){for(t.flags|=128,Un(o,!1),n=a.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),t.subtreeFlags=0,n=r,r=t.child;r!==null;)o=r,e=n,o.flags&=14680066,a=o.alternate,a===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=a.childLanes,o.lanes=a.lanes,o.child=a.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=a.memoizedProps,o.memoizedState=a.memoizedState,o.updateQueue=a.updateQueue,o.type=a.type,e=a.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return ue(fe,fe.current&1|2),t.child}e=e.sibling}o.tail!==null&&ve()>Dn&&(t.flags|=128,n=!0,Un(o,!1),t.lanes=4194304)}else{if(!n)if(e=jo(a),e!==null){if(t.flags|=128,n=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),Un(o,!0),o.tail===null&&o.tailMode==="hidden"&&!a.alternate&&!de)return Me(t),null}else 2*ve()-o.renderingStartTime>Dn&&r!==1073741824&&(t.flags|=128,n=!0,Un(o,!1),t.lanes=4194304);o.isBackwards?(a.sibling=t.child,t.child=a):(r=o.last,r!==null?r.sibling=a:t.child=a,o.last=a)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=ve(),t.sibling=null,r=fe.current,ue(fe,n?r&1|2:r&1),t):(Me(t),null);case 22:case 23:return qu(),n=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==n&&(t.flags|=8192),n&&(t.mode&1)!==0?(Ze&1073741824)!==0&&(Me(t),t.subtreeFlags&6&&(t.flags|=8192)):Me(t),null;case 24:return null;case 25:return null}throw Error(_(156,t.tag))}function km(e,t){switch(bu(t),t.tag){case 1:return Je(t.type)&&Lo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Cn(),ce(Ge),ce(ze),ju(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return $u(t),null;case 13:if(ce(fe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(_(340));Sn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ce(fe),null;case 4:return Cn(),null;case 10:return Mu(t.type._context),null;case 22:case 23:return qu(),null;case 24:return null;default:return null}}var to=!1,Te=!1,Sm=typeof WeakSet=="function"?WeakSet:Set,T=null;function sn(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(n){me(e,t,n)}else r.current=null}function Qi(e,t,r){try{r()}catch(n){me(e,t,n)}}var Dc=!1;function Em(e,t){if(Bi=Ao,e=sf(),Au(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var n=r.getSelection&&r.getSelection();if(n&&n.rangeCount!==0){r=n.anchorNode;var l=n.anchorOffset,o=n.focusNode;n=n.focusOffset;try{r.nodeType,o.nodeType}catch{r=null;break e}var a=0,u=-1,i=-1,s=0,d=0,m=e,f=null;t:for(;;){for(var w;m!==r||l!==0&&m.nodeType!==3||(u=a+l),m!==o||n!==0&&m.nodeType!==3||(i=a+n),m.nodeType===3&&(a+=m.nodeValue.length),(w=m.firstChild)!==null;)f=m,m=w;for(;;){if(m===e)break t;if(f===r&&++s===l&&(u=a),f===o&&++d===n&&(i=a),(w=m.nextSibling)!==null)break;m=f,f=m.parentNode}m=w}r=u===-1||i===-1?null:{start:u,end:i}}else r=null}r=r||{start:0,end:0}}else r=null;for(Ai={focusedElem:e,selectionRange:r},Ao=!1,T=t;T!==null;)if(t=T,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,T=e;else for(;T!==null;){t=T;try{var k=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(k!==null){var N=k.memoizedProps,P=k.memoizedState,p=t.stateNode,h=p.getSnapshotBeforeUpdate(t.elementType===t.type?N:vt(t.type,N),P);p.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(_(163))}}catch(S){me(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,T=e;break}T=t.return}return k=Dc,Dc=!1,k}function ol(e,t,r){var n=t.updateQueue;if(n=n!==null?n.lastEffect:null,n!==null){var l=n=n.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&Qi(t,r,o)}l=l.next}while(l!==n)}}function ha(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var n=r.create;r.destroy=n()}r=r.next}while(r!==t)}}function Ki(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function oh(e){var t=e.alternate;t!==null&&(e.alternate=null,oh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Rt],delete t[vl],delete t[_i],delete t[om],delete t[am])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ah(e){return e.tag===5||e.tag===3||e.tag===4}function Fc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ah(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Yi(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=_o));else if(n!==4&&(e=e.child,e!==null))for(Yi(e,t,r),e=e.sibling;e!==null;)Yi(e,t,r),e=e.sibling}function Gi(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(n!==4&&(e=e.child,e!==null))for(Gi(e,t,r),e=e.sibling;e!==null;)Gi(e,t,r),e=e.sibling}var Pe=null,xt=!1;function Gt(e,t,r){for(r=r.child;r!==null;)ih(e,t,r),r=r.sibling}function ih(e,t,r){if(Bt&&typeof Bt.onCommitFiberUnmount=="function")try{Bt.onCommitFiberUnmount(oa,r)}catch{}switch(r.tag){case 5:Te||sn(r,t);case 6:var n=Pe,l=xt;Pe=null,Gt(e,t,r),Pe=n,xt=l,Pe!==null&&(xt?(e=Pe,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):Pe.removeChild(r.stateNode));break;case 18:Pe!==null&&(xt?(e=Pe,r=r.stateNode,e.nodeType===8?Qa(e.parentNode,r):e.nodeType===1&&Qa(e,r),hl(e)):Qa(Pe,r.stateNode));break;case 4:n=Pe,l=xt,Pe=r.stateNode.containerInfo,xt=!0,Gt(e,t,r),Pe=n,xt=l;break;case 0:case 11:case 14:case 15:if(!Te&&(n=r.updateQueue,n!==null&&(n=n.lastEffect,n!==null))){l=n=n.next;do{var o=l,a=o.destroy;o=o.tag,a!==void 0&&((o&2)!==0||(o&4)!==0)&&Qi(r,t,a),l=l.next}while(l!==n)}Gt(e,t,r);break;case 1:if(!Te&&(sn(r,t),n=r.stateNode,typeof n.componentWillUnmount=="function"))try{n.props=r.memoizedProps,n.state=r.memoizedState,n.componentWillUnmount()}catch(u){me(r,t,u)}Gt(e,t,r);break;case 21:Gt(e,t,r);break;case 22:r.mode&1?(Te=(n=Te)||r.memoizedState!==null,Gt(e,t,r),Te=n):Gt(e,t,r);break;default:Gt(e,t,r)}}function Rc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new Sm),t.forEach(function(n){var l=bm.bind(null,e,n);r.has(n)||(r.add(n),n.then(l,l))})}}function gt(e,t){var r=t.deletions;if(r!==null)for(var n=0;n<r.length;n++){var l=r[n];try{var o=e,a=t,u=a;e:for(;u!==null;){switch(u.tag){case 5:Pe=u.stateNode,xt=!1;break e;case 3:Pe=u.stateNode.containerInfo,xt=!0;break e;case 4:Pe=u.stateNode.containerInfo,xt=!0;break e}u=u.return}if(Pe===null)throw Error(_(160));ih(o,a,l),Pe=null,xt=!1;var i=l.alternate;i!==null&&(i.return=null),l.return=null}catch(s){me(l,t,s)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)uh(t,e),t=t.sibling}function uh(e,t){var r=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(gt(t,e),Dt(e),n&4){try{ol(3,e,e.return),ha(3,e)}catch(N){me(e,e.return,N)}try{ol(5,e,e.return)}catch(N){me(e,e.return,N)}}break;case 1:gt(t,e),Dt(e),n&512&&r!==null&&sn(r,r.return);break;case 5:if(gt(t,e),Dt(e),n&512&&r!==null&&sn(r,r.return),e.flags&32){var l=e.stateNode;try{sl(l,"")}catch(N){me(e,e.return,N)}}if(n&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,a=r!==null?r.memoizedProps:o,u=e.type,i=e.updateQueue;if(e.updateQueue=null,i!==null)try{u==="input"&&o.type==="radio"&&o.name!=null&&Bd(l,o),xi(u,a);var s=xi(u,o);for(a=0;a<i.length;a+=2){var d=i[a],m=i[a+1];d==="style"?Ld(l,m):d==="dangerouslySetInnerHTML"?bd(l,m):d==="children"?sl(l,m):gu(l,d,m,s)}switch(u){case"input":pi(l,o);break;case"textarea":Ad(l,o);break;case"select":var f=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var w=o.value;w!=null?fn(l,!!o.multiple,w,!1):f!==!!o.multiple&&(o.defaultValue!=null?fn(l,!!o.multiple,o.defaultValue,!0):fn(l,!!o.multiple,o.multiple?[]:"",!1))}l[vl]=o}catch(N){me(e,e.return,N)}}break;case 6:if(gt(t,e),Dt(e),n&4){if(e.stateNode===null)throw Error(_(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(N){me(e,e.return,N)}}break;case 3:if(gt(t,e),Dt(e),n&4&&r!==null&&r.memoizedState.isDehydrated)try{hl(t.containerInfo)}catch(N){me(e,e.return,N)}break;case 4:gt(t,e),Dt(e);break;case 13:gt(t,e),Dt(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(Ju=ve())),n&4&&Rc(e);break;case 22:if(d=r!==null&&r.memoizedState!==null,e.mode&1?(Te=(s=Te)||d,gt(t,e),Te=s):gt(t,e),Dt(e),n&8192){if(s=e.memoizedState!==null,(e.stateNode.isHidden=s)&&!d&&(e.mode&1)!==0)for(T=e,d=e.child;d!==null;){for(m=T=d;T!==null;){switch(f=T,w=f.child,f.tag){case 0:case 11:case 14:case 15:ol(4,f,f.return);break;case 1:sn(f,f.return);var k=f.stateNode;if(typeof k.componentWillUnmount=="function"){n=f,r=f.return;try{t=n,k.props=t.memoizedProps,k.state=t.memoizedState,k.componentWillUnmount()}catch(N){me(n,r,N)}}break;case 5:sn(f,f.return);break;case 22:if(f.memoizedState!==null){Ac(m);continue}}w!==null?(w.return=f,T=w):Ac(m)}d=d.sibling}e:for(d=null,m=e;;){if(m.tag===5){if(d===null){d=m;try{l=m.stateNode,s?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(u=m.stateNode,i=m.memoizedProps.style,a=i!=null&&i.hasOwnProperty("display")?i.display:null,u.style.display=_d("display",a))}catch(N){me(e,e.return,N)}}}else if(m.tag===6){if(d===null)try{m.stateNode.nodeValue=s?"":m.memoizedProps}catch(N){me(e,e.return,N)}}else if((m.tag!==22&&m.tag!==23||m.memoizedState===null||m===e)&&m.child!==null){m.child.return=m,m=m.child;continue}if(m===e)break e;for(;m.sibling===null;){if(m.return===null||m.return===e)break e;d===m&&(d=null),m=m.return}d===m&&(d=null),m.sibling.return=m.return,m=m.sibling}}break;case 19:gt(t,e),Dt(e),n&4&&Rc(e);break;case 21:break;default:gt(t,e),Dt(e)}}function Dt(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(ah(r)){var n=r;break e}r=r.return}throw Error(_(160))}switch(n.tag){case 5:var l=n.stateNode;n.flags&32&&(sl(l,""),n.flags&=-33);var o=Fc(e);Gi(e,o,l);break;case 3:case 4:var a=n.stateNode.containerInfo,u=Fc(e);Yi(e,u,a);break;default:throw Error(_(161))}}catch(i){me(e,e.return,i)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Cm(e,t,r){T=e,sh(e)}function sh(e,t,r){for(var n=(e.mode&1)!==0;T!==null;){var l=T,o=l.child;if(l.tag===22&&n){var a=l.memoizedState!==null||to;if(!a){var u=l.alternate,i=u!==null&&u.memoizedState!==null||Te;u=to;var s=Te;if(to=a,(Te=i)&&!s)for(T=l;T!==null;)a=T,i=a.child,a.tag===22&&a.memoizedState!==null?Pc(l):i!==null?(i.return=a,T=i):Pc(l);for(;o!==null;)T=o,sh(o),o=o.sibling;T=l,to=u,Te=s}Bc(e)}else(l.subtreeFlags&8772)!==0&&o!==null?(o.return=l,T=o):Bc(e)}}function Bc(e){for(;T!==null;){var t=T;if((t.flags&8772)!==0){var r=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:Te||ha(5,t);break;case 1:var n=t.stateNode;if(t.flags&4&&!Te)if(r===null)n.componentDidMount();else{var l=t.elementType===t.type?r.memoizedProps:vt(t.type,r.memoizedProps);n.componentDidUpdate(l,r.memoizedState,n.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&hc(t,o,n);break;case 3:var a=t.updateQueue;if(a!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}hc(t,a,r)}break;case 5:var u=t.stateNode;if(r===null&&t.flags&4){r=u;var i=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":i.autoFocus&&r.focus();break;case"img":i.src&&(r.src=i.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var s=t.alternate;if(s!==null){var d=s.memoizedState;if(d!==null){var m=d.dehydrated;m!==null&&hl(m)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(_(163))}Te||t.flags&512&&Ki(t)}catch(f){me(t,t.return,f)}}if(t===e){T=null;break}if(r=t.sibling,r!==null){r.return=t.return,T=r;break}T=t.return}}function Ac(e){for(;T!==null;){var t=T;if(t===e){T=null;break}var r=t.sibling;if(r!==null){r.return=t.return,T=r;break}T=t.return}}function Pc(e){for(;T!==null;){var t=T;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{ha(4,t)}catch(i){me(t,r,i)}break;case 1:var n=t.stateNode;if(typeof n.componentDidMount=="function"){var l=t.return;try{n.componentDidMount()}catch(i){me(t,l,i)}}var o=t.return;try{Ki(t)}catch(i){me(t,o,i)}break;case 5:var a=t.return;try{Ki(t)}catch(i){me(t,a,i)}}}catch(i){me(t,t.return,i)}if(t===e){T=null;break}var u=t.sibling;if(u!==null){u.return=t.return,T=u;break}T=t.return}}var Nm=Math.ceil,Ho=Ut.ReactCurrentDispatcher,Yu=Ut.ReactCurrentOwner,ht=Ut.ReactCurrentBatchConfig,re=0,Ae=null,Ee=null,be=0,Ze=0,cn=yr(0),Fe=0,Cl=null,Or=0,pa=0,Gu=0,al=null,Ke=null,Ju=0,Dn=1/0,_t=null,Wo=!1,Ji=null,sr=null,ro=!1,nr=null,Qo=0,il=0,Xi=null,go=-1,vo=0;function He(){return(re&6)!==0?ve():go!==-1?go:go=ve()}function cr(e){return(e.mode&1)===0?1:(re&2)!==0&&be!==0?be&-be:um.transition!==null?(vo===0&&(vo=Qd()),vo):(e=oe,e!==0||(e=window.event,e=e===void 0?16:Zd(e.type)),e)}function St(e,t,r,n){if(50<il)throw il=0,Xi=null,Error(_(185));Bl(e,r,n),((re&2)===0||e!==Ae)&&(e===Ae&&((re&2)===0&&(pa|=r),Fe===4&&er(e,be)),Xe(e,n),r===1&&re===0&&(t.mode&1)===0&&(Dn=ve()+500,ca&&gr()))}function Xe(e,t){var r=e.callbackNode;up(e,t);var n=Bo(e,e===Ae?be:0);if(n===0)r!==null&&$s(r),e.callbackNode=null,e.callbackPriority=0;else if(t=n&-n,e.callbackPriority!==t){if(r!=null&&$s(r),t===1)e.tag===0?im(bc.bind(null,e)):xf(bc.bind(null,e)),nm(function(){(re&6)===0&&gr()}),r=null;else{switch(Kd(n)){case 1:r=Su;break;case 4:r=Hd;break;case 16:r=Ro;break;case 536870912:r=Wd;break;default:r=Ro}r=gh(r,ch.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function ch(e,t){if(go=-1,vo=0,(re&6)!==0)throw Error(_(327));var r=e.callbackNode;if(gn()&&e.callbackNode!==r)return null;var n=Bo(e,e===Ae?be:0);if(n===0)return null;if((n&30)!==0||(n&e.expiredLanes)!==0||t)t=Ko(e,n);else{t=n;var l=re;re|=2;var o=fh();(Ae!==e||be!==t)&&(_t=null,Dn=ve()+500,Lr(e,t));do try{Rm();break}catch(u){dh(e,u)}while(1);Iu(),Ho.current=o,re=l,Ee!==null?t=0:(Ae=null,be=0,t=Fe)}if(t!==0){if(t===2&&(l=Ci(e),l!==0&&(n=l,t=qi(e,l))),t===1)throw r=Cl,Lr(e,0),er(e,n),Xe(e,ve()),r;if(t===6)er(e,n);else{if(l=e.current.alternate,(n&30)===0&&!Dm(l)&&(t=Ko(e,n),t===2&&(o=Ci(e),o!==0&&(n=o,t=qi(e,o))),t===1))throw r=Cl,Lr(e,0),er(e,n),Xe(e,ve()),r;switch(e.finishedWork=l,e.finishedLanes=n,t){case 0:case 1:throw Error(_(345));case 2:Rr(e,Ke,_t);break;case 3:if(er(e,n),(n&130023424)===n&&(t=Ju+500-ve(),10<t)){if(Bo(e,0)!==0)break;if(l=e.suspendedLanes,(l&n)!==n){He(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=bi(Rr.bind(null,e,Ke,_t),t);break}Rr(e,Ke,_t);break;case 4:if(er(e,n),(n&4194240)===n)break;for(t=e.eventTimes,l=-1;0<n;){var a=31-kt(n);o=1<<a,a=t[a],a>l&&(l=a),n&=~o}if(n=l,n=ve()-n,n=(120>n?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*Nm(n/1960))-n,10<n){e.timeoutHandle=bi(Rr.bind(null,e,Ke,_t),n);break}Rr(e,Ke,_t);break;case 5:Rr(e,Ke,_t);break;default:throw Error(_(329))}}}return Xe(e,ve()),e.callbackNode===r?ch.bind(null,e):null}function qi(e,t){var r=al;return e.current.memoizedState.isDehydrated&&(Lr(e,t).flags|=256),e=Ko(e,t),e!==2&&(t=Ke,Ke=r,t!==null&&Zi(t)),e}function Zi(e){Ke===null?Ke=e:Ke.push.apply(Ke,e)}function Dm(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var n=0;n<r.length;n++){var l=r[n],o=l.getSnapshot;l=l.value;try{if(!Et(o(),l))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function er(e,t){for(t&=~Gu,t&=~pa,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-kt(t),n=1<<r;e[r]=-1,t&=~n}}function bc(e){if((re&6)!==0)throw Error(_(327));gn();var t=Bo(e,0);if((t&1)===0)return Xe(e,ve()),null;var r=Ko(e,t);if(e.tag!==0&&r===2){var n=Ci(e);n!==0&&(t=n,r=qi(e,n))}if(r===1)throw r=Cl,Lr(e,0),er(e,t),Xe(e,ve()),r;if(r===6)throw Error(_(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Rr(e,Ke,_t),Xe(e,ve()),null}function Xu(e,t){var r=re;re|=1;try{return e(t)}finally{re=r,re===0&&(Dn=ve()+500,ca&&gr())}}function $r(e){nr!==null&&nr.tag===0&&(re&6)===0&&gn();var t=re;re|=1;var r=ht.transition,n=oe;try{if(ht.transition=null,oe=1,e)return e()}finally{oe=n,ht.transition=r,re=t,(re&6)===0&&gr()}}function qu(){Ze=cn.current,ce(cn)}function Lr(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,rm(r)),Ee!==null)for(r=Ee.return;r!==null;){var n=r;switch(bu(n),n.tag){case 1:n=n.type.childContextTypes,n!=null&&Lo();break;case 3:Cn(),ce(Ge),ce(ze),ju();break;case 5:$u(n);break;case 4:Cn();break;case 13:ce(fe);break;case 19:ce(fe);break;case 10:Mu(n.type._context);break;case 22:case 23:qu()}r=r.return}if(Ae=e,Ee=e=dr(e.current,null),be=Ze=t,Fe=0,Cl=null,Gu=pa=Or=0,Ke=al=null,Pr!==null){for(t=0;t<Pr.length;t++)if(r=Pr[t],n=r.interleaved,n!==null){r.interleaved=null;var l=n.next,o=r.pending;if(o!==null){var a=o.next;o.next=l,n.next=a}r.pending=n}Pr=null}return e}function dh(e,t){do{var r=Ee;try{if(Iu(),po.current=Vo,Uo){for(var n=he.memoizedState;n!==null;){var l=n.queue;l!==null&&(l.pending=null),n=n.next}Uo=!1}if(zr=0,Be=De=he=null,ll=!1,kl=0,Yu.current=null,r===null||r.return===null){Fe=1,Cl=t,Ee=null;break}e:{var o=e,a=r.return,u=r,i=t;if(t=be,u.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){var s=i,d=u,m=d.tag;if((d.mode&1)===0&&(m===0||m===11||m===15)){var f=d.alternate;f?(d.updateQueue=f.updateQueue,d.memoizedState=f.memoizedState,d.lanes=f.lanes):(d.updateQueue=null,d.memoizedState=null)}var w=xc(a);if(w!==null){w.flags&=-257,wc(w,a,u,o,t),w.mode&1&&vc(o,s,t),t=w,i=s;var k=t.updateQueue;if(k===null){var N=new Set;N.add(i),t.updateQueue=N}else k.add(i);break e}else{if((t&1)===0){vc(o,s,t),Zu();break e}i=Error(_(426))}}else if(de&&u.mode&1){var P=xc(a);if(P!==null){(P.flags&65536)===0&&(P.flags|=256),wc(P,a,u,o,t),_u(Nn(i,u));break e}}o=i=Nn(i,u),Fe!==4&&(Fe=2),al===null?al=[o]:al.push(o),o=a;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var p=Yf(o,i,t);fc(o,p);break e;case 1:u=i;var h=o.type,g=o.stateNode;if((o.flags&128)===0&&(typeof h.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(sr===null||!sr.has(g)))){o.flags|=65536,t&=-t,o.lanes|=t;var S=Gf(o,u,t);fc(o,S);break e}}o=o.return}while(o!==null)}ph(r)}catch(F){t=F,Ee===r&&r!==null&&(Ee=r=r.return);continue}break}while(1)}function fh(){var e=Ho.current;return Ho.current=Vo,e===null?Vo:e}function Zu(){(Fe===0||Fe===3||Fe===2)&&(Fe=4),Ae===null||(Or&268435455)===0&&(pa&268435455)===0||er(Ae,be)}function Ko(e,t){var r=re;re|=2;var n=fh();(Ae!==e||be!==t)&&(_t=null,Lr(e,t));do try{Fm();break}catch(l){dh(e,l)}while(1);if(Iu(),re=r,Ho.current=n,Ee!==null)throw Error(_(261));return Ae=null,be=0,Fe}function Fm(){for(;Ee!==null;)hh(Ee)}function Rm(){for(;Ee!==null&&!Z0();)hh(Ee)}function hh(e){var t=yh(e.alternate,e,Ze);e.memoizedProps=e.pendingProps,t===null?ph(e):Ee=t,Yu.current=null}function ph(e){var t=e;do{var r=t.alternate;if(e=t.return,(t.flags&32768)===0){if(r=wm(r,t,Ze),r!==null){Ee=r;return}}else{if(r=km(r,t),r!==null){r.flags&=32767,Ee=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Fe=6,Ee=null;return}}if(t=t.sibling,t!==null){Ee=t;return}Ee=t=e}while(t!==null);Fe===0&&(Fe=5)}function Rr(e,t,r){var n=oe,l=ht.transition;try{ht.transition=null,oe=1,Bm(e,t,r,n)}finally{ht.transition=l,oe=n}return null}function Bm(e,t,r,n){do gn();while(nr!==null);if((re&6)!==0)throw Error(_(327));r=e.finishedWork;var l=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(_(177));e.callbackNode=null,e.callbackPriority=0;var o=r.lanes|r.childLanes;if(sp(e,o),e===Ae&&(Ee=Ae=null,be=0),(r.subtreeFlags&2064)===0&&(r.flags&2064)===0||ro||(ro=!0,gh(Ro,function(){return gn(),null})),o=(r.flags&15990)!==0,(r.subtreeFlags&15990)!==0||o){o=ht.transition,ht.transition=null;var a=oe;oe=1;var u=re;re|=4,Yu.current=null,Em(e,r),uh(r,e),Gp(Ai),Ao=!!Bi,Ai=Bi=null,e.current=r,Cm(r),ep(),re=u,oe=a,ht.transition=o}else e.current=r;if(ro&&(ro=!1,nr=e,Qo=l),o=e.pendingLanes,o===0&&(sr=null),np(r.stateNode),Xe(e,ve()),t!==null)for(n=e.onRecoverableError,r=0;r<t.length;r++)l=t[r],n(l.value,{componentStack:l.stack,digest:l.digest});if(Wo)throw Wo=!1,e=Ji,Ji=null,e;return(Qo&1)!==0&&e.tag!==0&&gn(),o=e.pendingLanes,(o&1)!==0?e===Xi?il++:(il=0,Xi=e):il=0,gr(),null}function gn(){if(nr!==null){var e=Kd(Qo),t=ht.transition,r=oe;try{if(ht.transition=null,oe=16>e?16:e,nr===null)var n=!1;else{if(e=nr,nr=null,Qo=0,(re&6)!==0)throw Error(_(331));var l=re;for(re|=4,T=e.current;T!==null;){var o=T,a=o.child;if((T.flags&16)!==0){var u=o.deletions;if(u!==null){for(var i=0;i<u.length;i++){var s=u[i];for(T=s;T!==null;){var d=T;switch(d.tag){case 0:case 11:case 15:ol(8,d,o)}var m=d.child;if(m!==null)m.return=d,T=m;else for(;T!==null;){d=T;var f=d.sibling,w=d.return;if(oh(d),d===s){T=null;break}if(f!==null){f.return=w,T=f;break}T=w}}}var k=o.alternate;if(k!==null){var N=k.child;if(N!==null){k.child=null;do{var P=N.sibling;N.sibling=null,N=P}while(N!==null)}}T=o}}if((o.subtreeFlags&2064)!==0&&a!==null)a.return=o,T=a;else e:for(;T!==null;){if(o=T,(o.flags&2048)!==0)switch(o.tag){case 0:case 11:case 15:ol(9,o,o.return)}var p=o.sibling;if(p!==null){p.return=o.return,T=p;break e}T=o.return}}var h=e.current;for(T=h;T!==null;){a=T;var g=a.child;if((a.subtreeFlags&2064)!==0&&g!==null)g.return=a,T=g;else e:for(a=h;T!==null;){if(u=T,(u.flags&2048)!==0)try{switch(u.tag){case 0:case 11:case 15:ha(9,u)}}catch(F){me(u,u.return,F)}if(u===a){T=null;break e}var S=u.sibling;if(S!==null){S.return=u.return,T=S;break e}T=u.return}}if(re=l,gr(),Bt&&typeof Bt.onPostCommitFiberRoot=="function")try{Bt.onPostCommitFiberRoot(oa,e)}catch{}n=!0}return n}finally{oe=r,ht.transition=t}}return!1}function _c(e,t,r){t=Nn(r,t),t=Yf(e,t,1),e=ur(e,t,1),t=He(),e!==null&&(Bl(e,1,t),Xe(e,t))}function me(e,t,r){if(e.tag===3)_c(e,e,r);else for(;t!==null;){if(t.tag===3){_c(t,e,r);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(sr===null||!sr.has(n))){e=Nn(r,e),e=Gf(t,e,1),t=ur(t,e,1),e=He(),t!==null&&(Bl(t,1,e),Xe(t,e));break}}t=t.return}}function Am(e,t,r){var n=e.pingCache;n!==null&&n.delete(t),t=He(),e.pingedLanes|=e.suspendedLanes&r,Ae===e&&(be&r)===r&&(Fe===4||Fe===3&&(be&130023424)===be&&500>ve()-Ju?Lr(e,0):Gu|=r),Xe(e,t)}function mh(e,t){t===0&&((e.mode&1)===0?t=1:(t=Ql,Ql<<=1,(Ql&130023424)===0&&(Ql=4194304)));var r=He();e=$t(e,t),e!==null&&(Bl(e,t,r),Xe(e,r))}function Pm(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),mh(e,r)}function bm(e,t){var r=0;switch(e.tag){case 13:var n=e.stateNode,l=e.memoizedState;l!==null&&(r=l.retryLane);break;case 19:n=e.stateNode;break;default:throw Error(_(314))}n!==null&&n.delete(t),mh(e,r)}var yh;yh=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ge.current)Ye=!0;else{if((e.lanes&r)===0&&(t.flags&128)===0)return Ye=!1,xm(e,t,r);Ye=(e.flags&131072)!==0}else Ye=!1,de&&(t.flags&1048576)!==0&&wf(t,To,t.index);switch(t.lanes=0,t.tag){case 2:var n=t.type;yo(e,t),e=t.pendingProps;var l=kn(t,ze.current);yn(t,r),l=Vu(null,t,n,e,l,r);var o=Hu();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Je(n)?(o=!0,Io(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,zu(t),l.updater=fa,t.stateNode=l,l._reactInternals=t,Oi(t,n,e,r),t=Ui(null,t,n,!0,o,r)):(t.tag=0,de&&o&&Pu(t),Ve(null,t,l,r),t=t.child),t;case 16:n=t.elementType;e:{switch(yo(e,t),e=t.pendingProps,l=n._init,n=l(n._payload),t.type=n,l=t.tag=Lm(n),e=vt(n,e),l){case 0:t=ji(null,t,n,e,r);break e;case 1:t=Ec(null,t,n,e,r);break e;case 11:t=kc(null,t,n,e,r);break e;case 14:t=Sc(null,t,n,vt(n.type,e),r);break e}throw Error(_(306,n,""))}return t;case 0:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:vt(n,l),ji(e,t,n,l,r);case 1:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:vt(n,l),Ec(e,t,n,l,r);case 3:e:{if(Zf(t),e===null)throw Error(_(387));n=t.pendingProps,o=t.memoizedState,l=o.element,Df(e,t),$o(t,n,null,r);var a=t.memoizedState;if(n=a.element,o.isDehydrated)if(o={element:n,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=Nn(Error(_(423)),t),t=Cc(e,t,n,r,l);break e}else if(n!==l){l=Nn(Error(_(424)),t),t=Cc(e,t,n,r,l);break e}else for(rt=ir(t.stateNode.containerInfo.firstChild),nt=t,de=!0,wt=null,r=Cf(t,null,n,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(Sn(),n===l){t=jt(e,t,r);break e}Ve(e,t,n,r)}t=t.child}return t;case 5:return Ff(t),e===null&&Mi(t),n=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,a=l.children,Pi(n,l)?a=null:o!==null&&Pi(n,o)&&(t.flags|=32),qf(e,t),Ve(e,t,a,r),t.child;case 6:return e===null&&Mi(t),null;case 13:return eh(e,t,r);case 4:return Ou(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=En(t,null,n,r):Ve(e,t,n,r),t.child;case 11:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:vt(n,l),kc(e,t,n,l,r);case 7:return Ve(e,t,t.pendingProps,r),t.child;case 8:return Ve(e,t,t.pendingProps.children,r),t.child;case 12:return Ve(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(n=t.type._context,l=t.pendingProps,o=t.memoizedProps,a=l.value,ue(zo,n._currentValue),n._currentValue=a,o!==null)if(Et(o.value,a)){if(o.children===l.children&&!Ge.current){t=jt(e,t,r);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var u=o.dependencies;if(u!==null){a=o.child;for(var i=u.firstContext;i!==null;){if(i.context===n){if(o.tag===1){i=Tt(-1,r&-r),i.tag=2;var s=o.updateQueue;if(s!==null){s=s.shared;var d=s.pending;d===null?i.next=i:(i.next=d.next,d.next=i),s.pending=i}}o.lanes|=r,i=o.alternate,i!==null&&(i.lanes|=r),Ti(o.return,r,t),u.lanes|=r;break}i=i.next}}else if(o.tag===10)a=o.type===t.type?null:o.child;else if(o.tag===18){if(a=o.return,a===null)throw Error(_(341));a.lanes|=r,u=a.alternate,u!==null&&(u.lanes|=r),Ti(a,r,t),a=o.sibling}else a=o.child;if(a!==null)a.return=o;else for(a=o;a!==null;){if(a===t){a=null;break}if(o=a.sibling,o!==null){o.return=a.return,a=o;break}a=a.return}o=a}Ve(e,t,l.children,r),t=t.child}return t;case 9:return l=t.type,n=t.pendingProps.children,yn(t,r),l=pt(l),n=n(l),t.flags|=1,Ve(e,t,n,r),t.child;case 14:return n=t.type,l=vt(n,t.pendingProps),l=vt(n.type,l),Sc(e,t,n,l,r);case 15:return Jf(e,t,t.type,t.pendingProps,r);case 17:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:vt(n,l),yo(e,t),t.tag=1,Je(n)?(e=!0,Io(t)):e=!1,yn(t,r),Kf(t,n,l),Oi(t,n,l,r),Ui(null,t,n,!0,e,r);case 19:return th(e,t,r);case 22:return Xf(e,t,r)}throw Error(_(156,t.tag))};function gh(e,t){return Vd(e,t)}function _m(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ft(e,t,r,n){return new _m(e,t,r,n)}function es(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Lm(e){if(typeof e=="function")return es(e)?1:0;if(e!=null){if(e=e.$$typeof,e===xu)return 11;if(e===wu)return 14}return 2}function dr(e,t){var r=e.alternate;return r===null?(r=ft(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function xo(e,t,r,n,l,o){var a=2;if(n=e,typeof e=="function")es(e)&&(a=1);else if(typeof e=="string")a=5;else e:switch(e){case Zr:return Ir(r.children,l,o,t);case vu:a=8,l|=8;break;case si:return e=ft(12,r,t,l|2),e.elementType=si,e.lanes=o,e;case ci:return e=ft(13,r,t,l),e.elementType=ci,e.lanes=o,e;case di:return e=ft(19,r,t,l),e.elementType=di,e.lanes=o,e;case Dd:return ma(r,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Cd:a=10;break e;case Nd:a=9;break e;case xu:a=11;break e;case wu:a=14;break e;case Xt:a=16,n=null;break e}throw Error(_(130,e==null?e:typeof e,""))}return t=ft(a,r,t,l),t.elementType=e,t.type=n,t.lanes=o,t}function Ir(e,t,r,n){return e=ft(7,e,n,t),e.lanes=r,e}function ma(e,t,r,n){return e=ft(22,e,n,t),e.elementType=Dd,e.lanes=r,e.stateNode={isHidden:!1},e}function ei(e,t,r){return e=ft(6,e,null,t),e.lanes=r,e}function ti(e,t,r){return t=ft(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Im(e,t,r,n,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ia(0),this.expirationTimes=Ia(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ia(0),this.identifierPrefix=n,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function ts(e,t,r,n,l,o,a,u,i){return e=new Im(e,t,r,u,i),t===1?(t=1,o===!0&&(t|=8)):t=0,o=ft(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:n,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},zu(o),e}function Mm(e,t,r){var n=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:qr,key:n==null?null:""+n,children:e,containerInfo:t,implementation:r}}function vh(e){if(!e)return hr;e=e._reactInternals;e:{if(Ur(e)!==e||e.tag!==1)throw Error(_(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Je(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(_(171))}if(e.tag===1){var r=e.type;if(Je(r))return vf(e,r,t)}return t}function xh(e,t,r,n,l,o,a,u,i){return e=ts(r,n,!0,e,l,o,a,u,i),e.context=vh(null),r=e.current,n=He(),l=cr(r),o=Tt(n,l),o.callback=t!=null?t:null,ur(r,o,l),e.current.lanes=l,Bl(e,l,n),Xe(e,n),e}function ya(e,t,r,n){var l=t.current,o=He(),a=cr(l);return r=vh(r),t.context===null?t.context=r:t.pendingContext=r,t=Tt(o,a),t.payload={element:e},n=n===void 0?null:n,n!==null&&(t.callback=n),e=ur(l,t,a),e!==null&&(St(e,l,a,o),ho(e,l,a)),a}function Yo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Lc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function rs(e,t){Lc(e,t),(e=e.alternate)&&Lc(e,t)}function Tm(){return null}var wh=typeof reportError=="function"?reportError:function(e){console.error(e)};function ns(e){this._internalRoot=e}ga.prototype.render=ns.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(_(409));ya(e,t,null,null)};ga.prototype.unmount=ns.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;$r(function(){ya(null,e,null,null)}),t[Ot]=null}};function ga(e){this._internalRoot=e}ga.prototype.unstable_scheduleHydration=function(e){if(e){var t=Jd();e={blockedOn:null,target:e,priority:t};for(var r=0;r<Zt.length&&t!==0&&t<Zt[r].priority;r++);Zt.splice(r,0,e),r===0&&qd(e)}};function ls(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function va(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ic(){}function zm(e,t,r,n,l){if(l){if(typeof n=="function"){var o=n;n=function(){var s=Yo(a);o.call(s)}}var a=xh(t,n,e,0,null,!1,!1,"",Ic);return e._reactRootContainer=a,e[Ot]=a.current,yl(e.nodeType===8?e.parentNode:e),$r(),a}for(;l=e.lastChild;)e.removeChild(l);if(typeof n=="function"){var u=n;n=function(){var s=Yo(i);u.call(s)}}var i=ts(e,0,!1,null,null,!1,!1,"",Ic);return e._reactRootContainer=i,e[Ot]=i.current,yl(e.nodeType===8?e.parentNode:e),$r(function(){ya(t,i,r,n)}),i}function xa(e,t,r,n,l){var o=r._reactRootContainer;if(o){var a=o;if(typeof l=="function"){var u=l;l=function(){var i=Yo(a);u.call(i)}}ya(t,a,e,l)}else a=zm(r,t,e,l,n);return Yo(a)}Yd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=Gn(t.pendingLanes);r!==0&&(Eu(t,r|1),Xe(t,ve()),(re&6)===0&&(Dn=ve()+500,gr()))}break;case 13:$r(function(){var n=$t(e,1);if(n!==null){var l=He();St(n,e,1,l)}}),rs(e,1)}};Cu=function(e){if(e.tag===13){var t=$t(e,134217728);if(t!==null){var r=He();St(t,e,134217728,r)}rs(e,134217728)}};Gd=function(e){if(e.tag===13){var t=cr(e),r=$t(e,t);if(r!==null){var n=He();St(r,e,t,n)}rs(e,t)}};Jd=function(){return oe};Xd=function(e,t){var r=oe;try{return oe=e,t()}finally{oe=r}};ki=function(e,t,r){switch(t){case"input":if(pi(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var n=r[t];if(n!==e&&n.form===e.form){var l=sa(n);if(!l)throw Error(_(90));Rd(n),pi(n,l)}}}break;case"textarea":Ad(e,r);break;case"select":t=r.value,t!=null&&fn(e,!!r.multiple,t,!1)}};Td=Xu;zd=$r;var Om={usingClientEntryPoint:!1,Events:[Pl,nn,sa,Id,Md,Xu]},Vn={findFiberByHostInstance:Ar,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},$m={bundleType:Vn.bundleType,version:Vn.version,rendererPackageName:Vn.rendererPackageName,rendererConfig:Vn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Ut.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=jd(e),e===null?null:e.stateNode},findFiberByHostInstance:Vn.findFiberByHostInstance||Tm,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var no=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!no.isDisabled&&no.supportsFiber)try{oa=no.inject($m),Bt=no}catch{}}ot.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Om;ot.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ls(t))throw Error(_(200));return Mm(e,t,null,r)};ot.createRoot=function(e,t){if(!ls(e))throw Error(_(299));var r=!1,n="",l=wh;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=ts(e,1,!1,null,null,r,!1,n,l),e[Ot]=t.current,yl(e.nodeType===8?e.parentNode:e),new ns(t)};ot.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=jd(t),e=e===null?null:e.stateNode,e};ot.flushSync=function(e){return $r(e)};ot.hydrate=function(e,t,r){if(!va(t))throw Error(_(200));return xa(null,e,t,!0,r)};ot.hydrateRoot=function(e,t,r){if(!ls(e))throw Error(_(405));var n=r!=null&&r.hydratedSources||null,l=!1,o="",a=wh;if(r!=null&&(r.unstable_strictMode===!0&&(l=!0),r.identifierPrefix!==void 0&&(o=r.identifierPrefix),r.onRecoverableError!==void 0&&(a=r.onRecoverableError)),t=xh(t,null,e,1,r!=null?r:null,l,!1,o,a),e[Ot]=t.current,yl(e),n)for(e=0;e<n.length;e++)r=n[e],l=r._getVersion,l=l(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,l]:t.mutableSourceEagerHydrationData.push(r,l);return new ga(t)};ot.render=function(e,t,r){if(!va(t))throw Error(_(200));return xa(null,e,t,!1,r)};ot.unmountComponentAtNode=function(e){if(!va(e))throw Error(_(40));return e._reactRootContainer?($r(function(){xa(null,null,e,!1,function(){e._reactRootContainer=null,e[Ot]=null})}),!0):!1};ot.unstable_batchedUpdates=Xu;ot.unstable_renderSubtreeIntoContainer=function(e,t,r,n){if(!va(r))throw Error(_(200));if(e==null||e._reactInternals===void 0)throw Error(_(38));return xa(e,t,r,!1,n)};ot.version="18.3.1-next-f1338f8080-20240426";(function(e){function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(r){console.error(r)}}t(),e.exports=ot})(pu);var kh,Mc=pu.exports;kh=Mc.createRoot,Mc.hydrateRoot;const jm="modulepreload",Um=function(e){return"/"+e},Tc={},Vm=function(t,r,n){if(!r||r.length===0)return t();const l=document.getElementsByTagName("link");return Promise.all(r.map(o=>{if(o=Um(o),o in Tc)return;Tc[o]=!0;const a=o.endsWith(".css"),u=a?'[rel="stylesheet"]':"";if(!!n)for(let d=l.length-1;d>=0;d--){const m=l[d];if(m.href===o&&(!a||m.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${o}"]${u}`))return;const s=document.createElement("link");if(s.rel=a?"stylesheet":jm,a||(s.as="script",s.crossOrigin=""),s.href=o,document.head.appendChild(s),a)return new Promise((d,m)=>{s.addEventListener("load",d),s.addEventListener("error",()=>m(new Error(`Unable to preload CSS for ${o}`)))})})).then(()=>t())};var os={};Object.defineProperty(os,"__esModule",{value:!0});os.parse=Jm;os.serialize=Xm;const Hm=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,Wm=/^[\u0021-\u003A\u003C-\u007E]*$/,Qm=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,Km=/^[\u0020-\u003A\u003D-\u007E]*$/,Ym=Object.prototype.toString,Gm=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function Jm(e,t){const r=new Gm,n=e.length;if(n<2)return r;const l=(t==null?void 0:t.decode)||qm;let o=0;do{const a=e.indexOf("=",o);if(a===-1)break;const u=e.indexOf(";",o),i=u===-1?n:u;if(a>i){o=e.lastIndexOf(";",a-1)+1;continue}const s=zc(e,o,a),d=Oc(e,a,s),m=e.slice(s,d);if(r[m]===void 0){let f=zc(e,a+1,i),w=Oc(e,i,f);const k=l(e.slice(f,w));r[m]=k}o=i+1}while(o<n);return r}function zc(e,t,r){do{const n=e.charCodeAt(t);if(n!==32&&n!==9)return t}while(++t<r);return r}function Oc(e,t,r){for(;t>r;){const n=e.charCodeAt(--t);if(n!==32&&n!==9)return t+1}return r}function Xm(e,t,r){const n=(r==null?void 0:r.encode)||encodeURIComponent;if(!Hm.test(e))throw new TypeError(`argument name is invalid: ${e}`);const l=n(t);if(!Wm.test(l))throw new TypeError(`argument val is invalid: ${t}`);let o=e+"="+l;if(!r)return o;if(r.maxAge!==void 0){if(!Number.isInteger(r.maxAge))throw new TypeError(`option maxAge is invalid: ${r.maxAge}`);o+="; Max-Age="+r.maxAge}if(r.domain){if(!Qm.test(r.domain))throw new TypeError(`option domain is invalid: ${r.domain}`);o+="; Domain="+r.domain}if(r.path){if(!Km.test(r.path))throw new TypeError(`option path is invalid: ${r.path}`);o+="; Path="+r.path}if(r.expires){if(!Zm(r.expires)||!Number.isFinite(r.expires.valueOf()))throw new TypeError(`option expires is invalid: ${r.expires}`);o+="; Expires="+r.expires.toUTCString()}if(r.httpOnly&&(o+="; HttpOnly"),r.secure&&(o+="; Secure"),r.partitioned&&(o+="; Partitioned"),r.priority)switch(typeof r.priority=="string"?r.priority.toLowerCase():void 0){case"low":o+="; Priority=Low";break;case"medium":o+="; Priority=Medium";break;case"high":o+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${r.priority}`)}if(r.sameSite)switch(typeof r.sameSite=="string"?r.sameSite.toLowerCase():r.sameSite){case!0:case"strict":o+="; SameSite=Strict";break;case"lax":o+="; SameSite=Lax";break;case"none":o+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${r.sameSite}`)}return o}function qm(e){if(e.indexOf("%")===-1)return e;try{return decodeURIComponent(e)}catch{return e}}function Zm(e){return Ym.call(e)==="[object Date]"}var wa={exports:{}},ka={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ey=C.exports,ty=Symbol.for("react.element"),ry=Symbol.for("react.fragment"),ny=Object.prototype.hasOwnProperty,ly=ey.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,oy={key:!0,ref:!0,__self:!0,__source:!0};function Sh(e,t,r){var n,l={},o=null,a=null;r!==void 0&&(o=""+r),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(a=t.ref);for(n in t)ny.call(t,n)&&!oy.hasOwnProperty(n)&&(l[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)l[n]===void 0&&(l[n]=t[n]);return{$$typeof:ty,type:e,key:o,ref:a,props:l,_owner:ly.current}}ka.Fragment=ry;ka.jsx=Sh;ka.jsxs=Sh;(function(e){e.exports=ka})(wa);const as=wa.exports.Fragment,c=wa.exports.jsx,E=wa.exports.jsxs;var Eh=e=>{throw TypeError(e)},ay=(e,t,r)=>t.has(e)||Eh("Cannot "+r),ri=(e,t,r)=>(ay(e,t,"read from private field"),r?r.call(e):t.get(e)),iy=(e,t,r)=>t.has(e)?Eh("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),$c="popstate";function uy(e={}){function t(n,l){let{pathname:o,search:a,hash:u}=n.location;return Nl("",{pathname:o,search:a,hash:u},l.state&&l.state.usr||null,l.state&&l.state.key||"default")}function r(n,l){return typeof l=="string"?l:pr(l)}return cy(t,r,null,e)}function X(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function xe(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function sy(){return Math.random().toString(36).substring(2,10)}function jc(e,t){return{usr:e.state,key:e.key,idx:t}}function Nl(e,t,r=null,n){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Vt(t):t,state:r,key:t&&t.key||n||sy()}}function pr({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Vt(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substring(n),e=e.substring(0,n)),e&&(t.pathname=e)}return t}function cy(e,t,r,n={}){let{window:l=document.defaultView,v5Compat:o=!1}=n,a=l.history,u="POP",i=null,s=d();s==null&&(s=0,a.replaceState({...a.state,idx:s},""));function d(){return(a.state||{idx:null}).idx}function m(){u="POP";let P=d(),p=P==null?null:P-s;s=P,i&&i({action:u,location:N.location,delta:p})}function f(P,p){u="PUSH";let h=Nl(N.location,P,p);r&&r(h,P),s=d()+1;let g=jc(h,s),S=N.createHref(h);try{a.pushState(g,"",S)}catch(F){if(F instanceof DOMException&&F.name==="DataCloneError")throw F;l.location.assign(S)}o&&i&&i({action:u,location:N.location,delta:1})}function w(P,p){u="REPLACE";let h=Nl(N.location,P,p);r&&r(h,P),s=d();let g=jc(h,s),S=N.createHref(h);a.replaceState(g,"",S),o&&i&&i({action:u,location:N.location,delta:0})}function k(P){return Ch(P)}let N={get action(){return u},get location(){return e(l,a)},listen(P){if(i)throw new Error("A history only accepts one active listener");return l.addEventListener($c,m),i=P,()=>{l.removeEventListener($c,m),i=null}},createHref(P){return t(l,P)},createURL:k,encodeLocation(P){let p=k(P);return{pathname:p.pathname,search:p.search,hash:p.hash}},push:f,replace:w,go(P){return a.go(P)}};return N}function Ch(e,t=!1){let r="http://localhost";typeof window<"u"&&(r=window.location.origin!=="null"?window.location.origin:window.location.href),X(r,"No window.location.(origin|href) available to create URL");let n=typeof e=="string"?e:pr(e);return n=n.replace(/ $/,"%20"),!t&&n.startsWith("//")&&(n=r+n),new URL(n,r)}var Xn,Uc=class{constructor(e){if(iy(this,Xn,new Map),e)for(let[t,r]of e)this.set(t,r)}get(e){if(ri(this,Xn).has(e))return ri(this,Xn).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,t){ri(this,Xn).set(e,t)}};Xn=new WeakMap;var dy=new Set(["lazy","caseSensitive","path","id","index","children"]);function fy(e){return dy.has(e)}var hy=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function py(e){return hy.has(e)}function my(e){return e.index===!0}function Go(e,t,r=[],n={}){return e.map((l,o)=>{let a=[...r,String(o)],u=typeof l.id=="string"?l.id:a.join("-");if(X(l.index!==!0||!l.children,"Cannot specify children on an index route"),X(!n[u],`Found a route id collision on id "${u}".  Route id's must be globally unique within Data Router usages`),my(l)){let i={...l,...t(l),id:u};return n[u]=i,i}else{let i={...l,...t(l),id:u,children:void 0};return n[u]=i,l.children&&(i.children=Go(l.children,t,a,n)),i}})}function tr(e,t,r="/"){return wo(e,t,r,!1)}function wo(e,t,r,n){let l=typeof t=="string"?Vt(t):t,o=yt(l.pathname||"/",r);if(o==null)return null;let a=Nh(e);gy(a);let u=null;for(let i=0;u==null&&i<a.length;++i){let s=Ry(o);u=Dy(a[i],s,n)}return u}function yy(e,t){let{route:r,pathname:n,params:l}=e;return{id:r.id,pathname:n,params:l,data:t[r.id],handle:r.handle}}function Nh(e,t=[],r=[],n=""){let l=(o,a,u)=>{let i={relativePath:u===void 0?o.path||"":u,caseSensitive:o.caseSensitive===!0,childrenIndex:a,route:o};i.relativePath.startsWith("/")&&(X(i.relativePath.startsWith(n),`Absolute route path "${i.relativePath}" nested under path "${n}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(n.length));let s=Pt([n,i.relativePath]),d=r.concat(i);o.children&&o.children.length>0&&(X(o.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${s}".`),Nh(o.children,t,d,s)),!(o.path==null&&!o.index)&&t.push({path:s,score:Cy(s,o.index),routesMeta:d})};return e.forEach((o,a)=>{var u;if(o.path===""||!((u=o.path)!=null&&u.includes("?")))l(o,a);else for(let i of Dh(o.path))l(o,a,i)}),t}function Dh(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,l=r.endsWith("?"),o=r.replace(/\?$/,"");if(n.length===0)return l?[o,""]:[o];let a=Dh(n.join("/")),u=[];return u.push(...a.map(i=>i===""?o:[o,i].join("/"))),l&&u.push(...a),u.map(i=>e.startsWith("/")&&i===""?"/":i)}function gy(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Ny(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}var vy=/^:[\w-]+$/,xy=3,wy=2,ky=1,Sy=10,Ey=-2,Vc=e=>e==="*";function Cy(e,t){let r=e.split("/"),n=r.length;return r.some(Vc)&&(n+=Ey),t&&(n+=wy),r.filter(l=>!Vc(l)).reduce((l,o)=>l+(vy.test(o)?xy:o===""?ky:Sy),n)}function Ny(e,t){return e.length===t.length&&e.slice(0,-1).every((n,l)=>n===t[l])?e[e.length-1]-t[t.length-1]:0}function Dy(e,t,r=!1){let{routesMeta:n}=e,l={},o="/",a=[];for(let u=0;u<n.length;++u){let i=n[u],s=u===n.length-1,d=o==="/"?t:t.slice(o.length)||"/",m=Jo({path:i.relativePath,caseSensitive:i.caseSensitive,end:s},d),f=i.route;if(!m&&s&&r&&!n[n.length-1].route.index&&(m=Jo({path:i.relativePath,caseSensitive:i.caseSensitive,end:!1},d)),!m)return null;Object.assign(l,m.params),a.push({params:l,pathname:Pt([o,m.pathname]),pathnameBase:Py(Pt([o,m.pathnameBase])),route:f}),m.pathnameBase!=="/"&&(o=Pt([o,m.pathnameBase]))}return a}function Jo(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=Fy(e.path,e.caseSensitive,e.end),l=t.match(r);if(!l)return null;let o=l[0],a=o.replace(/(.)\/+$/,"$1"),u=l.slice(1);return{params:n.reduce((s,{paramName:d,isOptional:m},f)=>{if(d==="*"){let k=u[f]||"";a=o.slice(0,o.length-k.length).replace(/(.)\/+$/,"$1")}const w=u[f];return m&&!w?s[d]=void 0:s[d]=(w||"").replace(/%2F/g,"/"),s},{}),pathname:o,pathnameBase:a,pattern:e}}function Fy(e,t=!1,r=!0){xe(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let n=[],l="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(a,u,i)=>(n.push({paramName:u,isOptional:i!=null}),i?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),l+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?l+="\\/*$":e!==""&&e!=="/"&&(l+="(?:(?=\\/|$))"),[new RegExp(l,t?void 0:"i"),n]}function Ry(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return xe(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function yt(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function By(e,t="/"){let{pathname:r,search:n="",hash:l=""}=typeof e=="string"?Vt(e):e;return{pathname:r?r.startsWith("/")?r:Ay(r,t):t,search:by(n),hash:_y(l)}}function Ay(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(l=>{l===".."?r.length>1&&r.pop():l!=="."&&r.push(l)}),r.length>1?r.join("/"):"/"}function ni(e,t,r,n){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(n)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Fh(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function Sa(e){let t=Fh(e);return t.map((r,n)=>n===t.length-1?r.pathname:r.pathnameBase)}function Ea(e,t,r,n=!1){let l;typeof e=="string"?l=Vt(e):(l={...e},X(!l.pathname||!l.pathname.includes("?"),ni("?","pathname","search",l)),X(!l.pathname||!l.pathname.includes("#"),ni("#","pathname","hash",l)),X(!l.search||!l.search.includes("#"),ni("#","search","hash",l)));let o=e===""||l.pathname==="",a=o?"/":l.pathname,u;if(a==null)u=r;else{let m=t.length-1;if(!n&&a.startsWith("..")){let f=a.split("/");for(;f[0]==="..";)f.shift(),m-=1;l.pathname=f.join("/")}u=m>=0?t[m]:"/"}let i=By(l,u),s=a&&a!=="/"&&a.endsWith("/"),d=(o||a===".")&&r.endsWith("/");return!i.pathname.endsWith("/")&&(s||d)&&(i.pathname+="/"),i}var Pt=e=>e.join("/").replace(/\/\/+/g,"/"),Py=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),by=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,_y=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,Xo=class{constructor(e,t,r,n=!1){this.status=e,this.statusText=t||"",this.internal=n,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}};function Dl(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var Rh=["POST","PUT","PATCH","DELETE"],Ly=new Set(Rh),Iy=["GET",...Rh],My=new Set(Iy),Ty=new Set([301,302,303,307,308]),zy=new Set([307,308]),li={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Oy={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Hn={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},is=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,$y=e=>({hasErrorBoundary:Boolean(e.hasErrorBoundary)}),Bh="remix-router-transitions",Ah=Symbol("ResetLoaderData");function jy(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u";X(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let n=e.hydrationRouteProperties||[],l=e.mapRouteProperties||$y,o={},a=Go(e.routes,l,void 0,o),u,i=e.basename||"/",s=e.dataStrategy||Qy,d={unstable_middleware:!1,...e.future},m=null,f=new Set,w=null,k=null,N=null,P=e.hydrationData!=null,p=tr(a,e.history.location,i),h=!1,g=null,S;if(p==null&&!e.patchRoutesOnNavigation){let x=st(404,{pathname:e.history.location.pathname}),{matches:D,route:R}=td(a);S=!0,p=D,g={[R.id]:x}}else if(p&&!e.hydrationData&&Tl(p,a,e.history.location.pathname).active&&(p=null),p)if(p.some(x=>x.route.lazy))S=!1;else if(!p.some(x=>x.route.loader))S=!0;else{let x=e.hydrationData?e.hydrationData.loaderData:null,D=e.hydrationData?e.hydrationData.errors:null;if(D){let R=p.findIndex(b=>D[b.route.id]!==void 0);S=p.slice(0,R+1).every(b=>!tu(b.route,x,D))}else S=p.every(R=>!tu(R.route,x,D))}else{S=!1,p=[];let x=Tl(null,a,e.history.location.pathname);x.active&&x.matches&&(h=!0,p=x.matches)}let F,v={historyAction:e.history.action,location:e.history.location,matches:p,initialized:S,navigation:li,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||g,fetchers:new Map,blockers:new Map},y="POP",B=!1,z,$=!1,G=new Map,ge=null,we=!1,A=!1,L=new Set,Z=new Map,Ce=0,I=-1,j=new Map,V=new Set,te=new Map,le=new Map,ke=new Set,$e=new Map,Wt,je=null;function Wr(){if(m=e.history.listen(({action:x,location:D,delta:R})=>{if(Wt){Wt(),Wt=void 0;return}xe($e.size===0||R!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let b=Cs({currentLocation:v.location,nextLocation:D,historyAction:x});if(b&&R!=null){let M=new Promise(U=>{Wt=U});e.history.go(R*-1),Ml(b,{state:"blocked",location:D,proceed(){Ml(b,{state:"proceeding",proceed:void 0,reset:void 0,location:D}),M.then(()=>e.history.go(R))},reset(){let U=new Map(v.blockers);U.set(b,Hn),Ue({blockers:U})}});return}return Er(x,D)}),r){ng(t,G);let x=()=>lg(t,G);t.addEventListener("pagehide",x),ge=()=>t.removeEventListener("pagehide",x)}return v.initialized||Er("POP",v.location,{initialHydration:!0}),F}function n0(){m&&m(),ge&&ge(),f.clear(),z&&z.abort(),v.fetchers.forEach((x,D)=>Da(D)),v.blockers.forEach((x,D)=>Es(D))}function l0(x){return f.add(x),()=>f.delete(x)}function Ue(x,D={}){v={...v,...x};let R=[],b=[];v.fetchers.forEach((M,U)=>{M.state==="idle"&&(ke.has(U)?R.push(U):b.push(U))}),ke.forEach(M=>{!v.fetchers.has(M)&&!Z.has(M)&&R.push(M)}),[...f].forEach(M=>M(v,{deletedFetchers:R,viewTransitionOpts:D.viewTransitionOpts,flushSync:D.flushSync===!0})),R.forEach(M=>Da(M)),b.forEach(M=>v.fetchers.delete(M))}function Qr(x,D,{flushSync:R}={}){var Q,Y;let b=v.actionData!=null&&v.navigation.formMethod!=null&&tt(v.navigation.formMethod)&&v.navigation.state==="loading"&&((Q=x.state)==null?void 0:Q._isRedirect)!==!0,M;D.actionData?Object.keys(D.actionData).length>0?M=D.actionData:M=null:b?M=v.actionData:M=null;let U=D.loaderData?Zc(v.loaderData,D.loaderData,D.matches||[],D.errors):v.loaderData,K=v.blockers;K.size>0&&(K=new Map(K),K.forEach((W,J)=>K.set(J,Hn)));let O=B===!0||v.navigation.formMethod!=null&&tt(v.navigation.formMethod)&&((Y=x.state)==null?void 0:Y._isRedirect)!==!0;u&&(a=u,u=void 0),we||y==="POP"||(y==="PUSH"?e.history.push(x,x.state):y==="REPLACE"&&e.history.replace(x,x.state));let H;if(y==="POP"){let W=G.get(v.location.pathname);W&&W.has(x.pathname)?H={currentLocation:v.location,nextLocation:x}:G.has(x.pathname)&&(H={currentLocation:x,nextLocation:v.location})}else if($){let W=G.get(v.location.pathname);W?W.add(x.pathname):(W=new Set([x.pathname]),G.set(v.location.pathname,W)),H={currentLocation:v.location,nextLocation:x}}Ue({...D,actionData:M,loaderData:U,historyAction:y,location:x,initialized:!0,navigation:li,revalidation:"idle",restoreScrollPosition:Ds(x,D.matches||v.matches),preventScrollReset:O,blockers:K},{viewTransitionOpts:H,flushSync:R===!0}),y="POP",B=!1,$=!1,we=!1,A=!1,je==null||je.resolve(),je=null}async function ys(x,D){if(typeof x=="number"){e.history.go(x);return}let R=eu(v.location,v.matches,i,x,D==null?void 0:D.fromRouteId,D==null?void 0:D.relative),{path:b,submission:M,error:U}=Hc(!1,R,D),K=v.location,O=Nl(v.location,b,D&&D.state);O={...O,...e.history.encodeLocation(O)};let H=D&&D.replace!=null?D.replace:void 0,Q="PUSH";H===!0?Q="REPLACE":H===!1||M!=null&&tt(M.formMethod)&&M.formAction===v.location.pathname+v.location.search&&(Q="REPLACE");let Y=D&&"preventScrollReset"in D?D.preventScrollReset===!0:void 0,W=(D&&D.flushSync)===!0,J=Cs({currentLocation:K,nextLocation:O,historyAction:Q});if(J){Ml(J,{state:"blocked",location:O,proceed(){Ml(J,{state:"proceeding",proceed:void 0,reset:void 0,location:O}),ys(x,D)},reset(){let ie=new Map(v.blockers);ie.set(J,Hn),Ue({blockers:ie})}});return}await Er(Q,O,{submission:M,pendingError:U,preventScrollReset:Y,replace:D&&D.replace,enableViewTransition:D&&D.viewTransition,flushSync:W})}function o0(){je||(je=og()),Na(),Ue({revalidation:"loading"});let x=je.promise;return v.navigation.state==="submitting"?x:v.navigation.state==="idle"?(Er(v.historyAction,v.location,{startUninterruptedRevalidation:!0}),x):(Er(y||v.historyAction,v.navigation.location,{overrideNavigation:v.navigation,enableViewTransition:$===!0}),x)}async function Er(x,D,R){z&&z.abort(),z=null,y=x,we=(R&&R.startUninterruptedRevalidation)===!0,m0(v.location,v.matches),B=(R&&R.preventScrollReset)===!0,$=(R&&R.enableViewTransition)===!0;let b=u||a,M=R&&R.overrideNavigation,U=(R==null?void 0:R.initialHydration)&&v.matches&&v.matches.length>0&&!h?v.matches:tr(b,D,i),K=(R&&R.flushSync)===!0;if(U&&v.initialized&&!A&&qy(v.location,D)&&!(R&&R.submission&&tt(R.submission.formMethod))){Qr(D,{matches:U},{flushSync:K});return}let O=Tl(U,b,D.pathname);if(O.active&&O.matches&&(U=O.matches),!U){let{error:Le,notFoundMatches:qe,route:ne}=Fa(D.pathname);Qr(D,{matches:qe,loaderData:{},errors:{[ne.id]:Le}},{flushSync:K});return}z=new AbortController;let H=Xr(e.history,D,z.signal,R&&R.submission),Q=new Uc(e.unstable_getContext?await e.unstable_getContext():void 0),Y;if(R&&R.pendingError)Y=[Br(U).route.id,{type:"error",error:R.pendingError}];else if(R&&R.submission&&tt(R.submission.formMethod)){let Le=await a0(H,D,R.submission,U,Q,O.active,R&&R.initialHydration===!0,{replace:R.replace,flushSync:K});if(Le.shortCircuited)return;if(Le.pendingActionResult){let[qe,ne]=Le.pendingActionResult;if(et(ne)&&Dl(ne.error)&&ne.error.status===404){z=null,Qr(D,{matches:Le.matches,loaderData:{},errors:{[qe]:ne.error}});return}}U=Le.matches||U,Y=Le.pendingActionResult,M=oi(D,R.submission),K=!1,O.active=!1,H=Xr(e.history,H.url,H.signal)}let{shortCircuited:W,matches:J,loaderData:ie,errors:Ne}=await i0(H,D,U,Q,O.active,M,R&&R.submission,R&&R.fetcherSubmission,R&&R.replace,R&&R.initialHydration===!0,K,Y);W||(z=null,Qr(D,{matches:J||U,...ed(Y),loaderData:ie,errors:Ne}))}async function a0(x,D,R,b,M,U,K,O={}){Na();let H=tg(D,R);if(Ue({navigation:H},{flushSync:O.flushSync===!0}),U){let W=await zl(b,D.pathname,x.signal);if(W.type==="aborted")return{shortCircuited:!0};if(W.type==="error"){let J=Br(W.partialMatches).route.id;return{matches:W.partialMatches,pendingActionResult:[J,{type:"error",error:W.error}]}}else if(W.matches)b=W.matches;else{let{notFoundMatches:J,error:ie,route:Ne}=Fa(D.pathname);return{matches:J,pendingActionResult:[Ne.id,{type:"error",error:ie}]}}}let Q,Y=qn(b,D);if(!Y.route.action&&!Y.route.lazy)Q={type:"error",error:st(405,{method:x.method,pathname:D.pathname,routeId:Y.route.id})};else{let W=vn(l,o,x,b,Y,K?[]:n,M),J=await Pn(x,W,M,null);if(Q=J[Y.route.id],!Q){for(let ie of b)if(J[ie.route.id]){Q=J[ie.route.id];break}}if(x.signal.aborted)return{shortCircuited:!0}}if(_r(Q)){let W;return O&&O.replace!=null?W=O.replace:W=Jc(Q.response.headers.get("Location"),new URL(x.url),i)===v.location.pathname+v.location.search,await Cr(x,Q,!0,{submission:R,replace:W}),{shortCircuited:!0}}if(et(Q)){let W=Br(b,Y.route.id);return(O&&O.replace)!==!0&&(y="PUSH"),{matches:b,pendingActionResult:[W.route.id,Q,Y.route.id]}}return{matches:b,pendingActionResult:[Y.route.id,Q]}}async function i0(x,D,R,b,M,U,K,O,H,Q,Y,W){let J=U||oi(D,K),ie=K||O||nd(J),Ne=!we&&!Q;if(M){if(Ne){let ut=gs(W);Ue({navigation:J,...ut!==void 0?{actionData:ut}:{}},{flushSync:Y})}let ae=await zl(R,D.pathname,x.signal);if(ae.type==="aborted")return{shortCircuited:!0};if(ae.type==="error"){let ut=Br(ae.partialMatches).route.id;return{matches:ae.partialMatches,loaderData:{},errors:{[ut]:ae.error}}}else if(ae.matches)R=ae.matches;else{let{error:ut,notFoundMatches:Yt,route:$l}=Fa(D.pathname);return{matches:Yt,loaderData:{},errors:{[$l.id]:ut}}}}let Le=u||a,{dsMatches:qe,revalidatingFetchers:ne}=Wc(x,b,l,o,e.history,v,R,ie,D,Q?[]:n,Q===!0,A,L,ke,te,V,Le,i,e.patchRoutesOnNavigation!=null,W);if(I=++Ce,!e.dataStrategy&&!qe.some(ae=>ae.shouldLoad)&&ne.length===0){let ae=ks();return Qr(D,{matches:R,loaderData:{},errors:W&&et(W[1])?{[W[0]]:W[1].error}:null,...ed(W),...ae?{fetchers:new Map(v.fetchers)}:{}},{flushSync:Y}),{shortCircuited:!0}}if(Ne){let ae={};if(!M){ae.navigation=J;let ut=gs(W);ut!==void 0&&(ae.actionData=ut)}ne.length>0&&(ae.fetchers=u0(ne)),Ue(ae,{flushSync:Y})}ne.forEach(ae=>{Kt(ae.key),ae.controller&&Z.set(ae.key,ae.controller)});let bn=()=>ne.forEach(ae=>Kt(ae.key));z&&z.signal.addEventListener("abort",bn);let{loaderResults:Nr,fetcherResults:_n}=await vs(qe,ne,x,b);if(x.signal.aborted)return{shortCircuited:!0};z&&z.signal.removeEventListener("abort",bn),ne.forEach(ae=>Z.delete(ae.key));let it=lo(Nr);if(it)return await Cr(x,it.result,!0,{replace:H}),{shortCircuited:!0};if(it=lo(_n),it)return V.add(it.key),await Cr(x,it.result,!0,{replace:H}),{shortCircuited:!0};let{loaderData:Ln,errors:In}=qc(v,R,Nr,W,ne,_n);Q&&v.errors&&(In={...v.errors,...In});let Ra=ks(),Dr=Ss(I),Ol=Ra||Dr||ne.length>0;return{matches:R,loaderData:Ln,errors:In,...Ol?{fetchers:new Map(v.fetchers)}:{}}}function gs(x){if(x&&!et(x[1]))return{[x[0]]:x[1].data};if(v.actionData)return Object.keys(v.actionData).length===0?null:v.actionData}function u0(x){return x.forEach(D=>{let R=v.fetchers.get(D.key),b=Wn(void 0,R?R.data:void 0);v.fetchers.set(D.key,b)}),new Map(v.fetchers)}async function s0(x,D,R,b){Kt(x);let M=(b&&b.flushSync)===!0,U=u||a,K=eu(v.location,v.matches,i,R,D,b==null?void 0:b.relative),O=tr(U,K,i),H=Tl(O,U,K);if(H.active&&H.matches&&(O=H.matches),!O){bt(x,D,st(404,{pathname:K}),{flushSync:M});return}let{path:Q,submission:Y,error:W}=Hc(!0,K,b);if(W){bt(x,D,W,{flushSync:M});return}let J=qn(O,Q),ie=new Uc(e.unstable_getContext?await e.unstable_getContext():void 0),Ne=(b&&b.preventScrollReset)===!0;if(Y&&tt(Y.formMethod)){await c0(x,D,Q,J,O,ie,H.active,M,Ne,Y);return}te.set(x,{routeId:D,path:Q}),await d0(x,D,Q,J,O,ie,H.active,M,Ne,Y)}async function c0(x,D,R,b,M,U,K,O,H,Q){Na(),te.delete(x);function Y(Se){if(!Se.route.action&&!Se.route.lazy){let Kr=st(405,{method:Q.formMethod,pathname:R,routeId:D});return bt(x,D,Kr,{flushSync:O}),!0}return!1}if(!K&&Y(b))return;let W=v.fetchers.get(x);Qt(x,rg(Q,W),{flushSync:O});let J=new AbortController,ie=Xr(e.history,R,J.signal,Q);if(K){let Se=await zl(M,R,ie.signal,x);if(Se.type==="aborted")return;if(Se.type==="error"){bt(x,D,Se.error,{flushSync:O});return}else if(Se.matches){if(M=Se.matches,b=qn(M,R),Y(b))return}else{bt(x,D,st(404,{pathname:R}),{flushSync:O});return}}Z.set(x,J);let Ne=Ce,Le=vn(l,o,ie,M,b,n,U),ne=(await Pn(ie,Le,U,x))[b.route.id];if(ie.signal.aborted){Z.get(x)===J&&Z.delete(x);return}if(ke.has(x)){if(_r(ne)||et(ne)){Qt(x,Jt(void 0));return}}else{if(_r(ne))if(Z.delete(x),I>Ne){Qt(x,Jt(void 0));return}else return V.add(x),Qt(x,Wn(Q)),Cr(ie,ne,!1,{fetcherSubmission:Q,preventScrollReset:H});if(et(ne)){bt(x,D,ne.error);return}}let bn=v.navigation.location||v.location,Nr=Xr(e.history,bn,J.signal),_n=u||a,it=v.navigation.state!=="idle"?tr(_n,v.navigation.location,i):v.matches;X(it,"Didn't find any matches after fetcher action");let Ln=++Ce;j.set(x,Ln);let In=Wn(Q,ne.data);v.fetchers.set(x,In);let{dsMatches:Ra,revalidatingFetchers:Dr}=Wc(Nr,U,l,o,e.history,v,it,Q,bn,n,!1,A,L,ke,te,V,_n,i,e.patchRoutesOnNavigation!=null,[b.route.id,ne]);Dr.filter(Se=>Se.key!==x).forEach(Se=>{let Kr=Se.key,Fs=v.fetchers.get(Kr),v0=Wn(void 0,Fs?Fs.data:void 0);v.fetchers.set(Kr,v0),Kt(Kr),Se.controller&&Z.set(Kr,Se.controller)}),Ue({fetchers:new Map(v.fetchers)});let Ol=()=>Dr.forEach(Se=>Kt(Se.key));J.signal.addEventListener("abort",Ol);let{loaderResults:ae,fetcherResults:ut}=await vs(Ra,Dr,Nr,U);if(J.signal.aborted)return;if(J.signal.removeEventListener("abort",Ol),j.delete(x),Z.delete(x),Dr.forEach(Se=>Z.delete(Se.key)),v.fetchers.has(x)){let Se=Jt(ne.data);v.fetchers.set(x,Se)}let Yt=lo(ae);if(Yt)return Cr(Nr,Yt.result,!1,{preventScrollReset:H});if(Yt=lo(ut),Yt)return V.add(Yt.key),Cr(Nr,Yt.result,!1,{preventScrollReset:H});let{loaderData:$l,errors:Ba}=qc(v,it,ae,void 0,Dr,ut);Ss(Ln),v.navigation.state==="loading"&&Ln>I?(X(y,"Expected pending action"),z&&z.abort(),Qr(v.navigation.location,{matches:it,loaderData:$l,errors:Ba,fetchers:new Map(v.fetchers)})):(Ue({errors:Ba,loaderData:Zc(v.loaderData,$l,it,Ba),fetchers:new Map(v.fetchers)}),A=!1)}async function d0(x,D,R,b,M,U,K,O,H,Q){let Y=v.fetchers.get(x);Qt(x,Wn(Q,Y?Y.data:void 0),{flushSync:O});let W=new AbortController,J=Xr(e.history,R,W.signal);if(K){let ne=await zl(M,R,J.signal,x);if(ne.type==="aborted")return;if(ne.type==="error"){bt(x,D,ne.error,{flushSync:O});return}else if(ne.matches)M=ne.matches,b=qn(M,R);else{bt(x,D,st(404,{pathname:R}),{flushSync:O});return}}Z.set(x,W);let ie=Ce,Ne=vn(l,o,J,M,b,n,U),qe=(await Pn(J,Ne,U,x))[b.route.id];if(Z.get(x)===W&&Z.delete(x),!J.signal.aborted){if(ke.has(x)){Qt(x,Jt(void 0));return}if(_r(qe))if(I>ie){Qt(x,Jt(void 0));return}else{V.add(x),await Cr(J,qe,!1,{preventScrollReset:H});return}if(et(qe)){bt(x,D,qe.error);return}Qt(x,Jt(qe.data))}}async function Cr(x,D,R,{submission:b,fetcherSubmission:M,preventScrollReset:U,replace:K}={}){D.response.headers.has("X-Remix-Revalidate")&&(A=!0);let O=D.response.headers.get("Location");X(O,"Expected a Location header on the redirect Response"),O=Jc(O,new URL(x.url),i);let H=Nl(v.location,O,{_isRedirect:!0});if(r){let Ne=!1;if(D.response.headers.has("X-Remix-Reload-Document"))Ne=!0;else if(is.test(O)){const Le=Ch(O,!0);Ne=Le.origin!==t.location.origin||yt(Le.pathname,i)==null}if(Ne){K?t.location.replace(O):t.location.assign(O);return}}z=null;let Q=K===!0||D.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:Y,formAction:W,formEncType:J}=v.navigation;!b&&!M&&Y&&W&&J&&(b=nd(v.navigation));let ie=b||M;if(zy.has(D.response.status)&&ie&&tt(ie.formMethod))await Er(Q,H,{submission:{...ie,formAction:O},preventScrollReset:U||B,enableViewTransition:R?$:void 0});else{let Ne=oi(H,b);await Er(Q,H,{overrideNavigation:Ne,fetcherSubmission:M,preventScrollReset:U||B,enableViewTransition:R?$:void 0})}}async function Pn(x,D,R,b){let M,U={};try{M=await Ky(s,x,D,b,R,!1)}catch(K){return D.filter(O=>O.shouldLoad).forEach(O=>{U[O.route.id]={type:"error",error:K}}),U}if(x.signal.aborted)return U;for(let[K,O]of Object.entries(M))if(Zy(O)){let H=O.result;U[K]={type:"redirect",response:Jy(H,x,K,D,i)}}else U[K]=await Gy(O);return U}async function vs(x,D,R,b){let M=Pn(R,x,b,null),U=Promise.all(D.map(async H=>{if(H.matches&&H.match&&H.request&&H.controller){let Y=(await Pn(H.request,H.matches,b,H.key))[H.match.route.id];return{[H.key]:Y}}else return Promise.resolve({[H.key]:{type:"error",error:st(404,{pathname:H.path})}})})),K=await M,O=(await U).reduce((H,Q)=>Object.assign(H,Q),{});return{loaderResults:K,fetcherResults:O}}function Na(){A=!0,te.forEach((x,D)=>{Z.has(D)&&L.add(D),Kt(D)})}function Qt(x,D,R={}){v.fetchers.set(x,D),Ue({fetchers:new Map(v.fetchers)},{flushSync:(R&&R.flushSync)===!0})}function bt(x,D,R,b={}){let M=Br(v.matches,D);Da(x),Ue({errors:{[M.route.id]:R},fetchers:new Map(v.fetchers)},{flushSync:(b&&b.flushSync)===!0})}function xs(x){return le.set(x,(le.get(x)||0)+1),ke.has(x)&&ke.delete(x),v.fetchers.get(x)||Oy}function Da(x){let D=v.fetchers.get(x);Z.has(x)&&!(D&&D.state==="loading"&&j.has(x))&&Kt(x),te.delete(x),j.delete(x),V.delete(x),ke.delete(x),L.delete(x),v.fetchers.delete(x)}function f0(x){let D=(le.get(x)||0)-1;D<=0?(le.delete(x),ke.add(x)):le.set(x,D),Ue({fetchers:new Map(v.fetchers)})}function Kt(x){let D=Z.get(x);D&&(D.abort(),Z.delete(x))}function ws(x){for(let D of x){let R=xs(D),b=Jt(R.data);v.fetchers.set(D,b)}}function ks(){let x=[],D=!1;for(let R of V){let b=v.fetchers.get(R);X(b,`Expected fetcher: ${R}`),b.state==="loading"&&(V.delete(R),x.push(R),D=!0)}return ws(x),D}function Ss(x){let D=[];for(let[R,b]of j)if(b<x){let M=v.fetchers.get(R);X(M,`Expected fetcher: ${R}`),M.state==="loading"&&(Kt(R),j.delete(R),D.push(R))}return ws(D),D.length>0}function h0(x,D){let R=v.blockers.get(x)||Hn;return $e.get(x)!==D&&$e.set(x,D),R}function Es(x){v.blockers.delete(x),$e.delete(x)}function Ml(x,D){let R=v.blockers.get(x)||Hn;X(R.state==="unblocked"&&D.state==="blocked"||R.state==="blocked"&&D.state==="blocked"||R.state==="blocked"&&D.state==="proceeding"||R.state==="blocked"&&D.state==="unblocked"||R.state==="proceeding"&&D.state==="unblocked",`Invalid blocker state transition: ${R.state} -> ${D.state}`);let b=new Map(v.blockers);b.set(x,D),Ue({blockers:b})}function Cs({currentLocation:x,nextLocation:D,historyAction:R}){if($e.size===0)return;$e.size>1&&xe(!1,"A router only supports one blocker at a time");let b=Array.from($e.entries()),[M,U]=b[b.length-1],K=v.blockers.get(M);if(!(K&&K.state==="proceeding")&&U({currentLocation:x,nextLocation:D,historyAction:R}))return M}function Fa(x){let D=st(404,{pathname:x}),R=u||a,{matches:b,route:M}=td(R);return{notFoundMatches:b,route:M,error:D}}function p0(x,D,R){if(w=x,N=D,k=R||null,!P&&v.navigation===li){P=!0;let b=Ds(v.location,v.matches);b!=null&&Ue({restoreScrollPosition:b})}return()=>{w=null,N=null,k=null}}function Ns(x,D){return k&&k(x,D.map(b=>yy(b,v.loaderData)))||x.key}function m0(x,D){if(w&&N){let R=Ns(x,D);w[R]=N()}}function Ds(x,D){if(w){let R=Ns(x,D),b=w[R];if(typeof b=="number")return b}return null}function Tl(x,D,R){if(e.patchRoutesOnNavigation)if(x){if(Object.keys(x[0].params).length>0)return{active:!0,matches:wo(D,R,i,!0)}}else return{active:!0,matches:wo(D,R,i,!0)||[]};return{active:!1,matches:null}}async function zl(x,D,R,b){if(!e.patchRoutesOnNavigation)return{type:"success",matches:x};let M=x;for(;;){let U=u==null,K=u||a,O=o;try{await e.patchRoutesOnNavigation({signal:R,path:D,matches:M,fetcherKey:b,patch:(Y,W)=>{R.aborted||Qc(Y,W,K,O,l)}})}catch(Y){return{type:"error",error:Y,partialMatches:M}}finally{U&&!R.aborted&&(a=[...a])}if(R.aborted)return{type:"aborted"};let H=tr(K,D,i);if(H)return{type:"success",matches:H};let Q=wo(K,D,i,!0);if(!Q||M.length===Q.length&&M.every((Y,W)=>Y.route.id===Q[W].route.id))return{type:"success",matches:null};M=Q}}function y0(x){o={},u=Go(x,l,void 0,o)}function g0(x,D){let R=u==null;Qc(x,D,u||a,o,l),R&&(a=[...a],Ue({}))}return F={get basename(){return i},get future(){return d},get state(){return v},get routes(){return a},get window(){return t},initialize:Wr,subscribe:l0,enableScrollRestoration:p0,navigate:ys,fetch:s0,revalidate:o0,createHref:x=>e.history.createHref(x),encodeLocation:x=>e.history.encodeLocation(x),getFetcher:xs,deleteFetcher:f0,dispose:n0,getBlocker:h0,deleteBlocker:Es,patchRoutes:g0,_internalFetchControllers:Z,_internalSetRoutes:y0},F}function Uy(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function eu(e,t,r,n,l,o){let a,u;if(l){a=[];for(let s of t)if(a.push(s),s.route.id===l){u=s;break}}else a=t,u=t[t.length-1];let i=Ea(n||".",Sa(a),yt(e.pathname,r)||e.pathname,o==="path");if(n==null&&(i.search=e.search,i.hash=e.hash),(n==null||n===""||n===".")&&u){let s=us(i.search);if(u.route.index&&!s)i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index";else if(!u.route.index&&s){let d=new URLSearchParams(i.search),m=d.getAll("index");d.delete("index"),m.filter(w=>w).forEach(w=>d.append("index",w));let f=d.toString();i.search=f?`?${f}`:""}}return r!=="/"&&(i.pathname=i.pathname==="/"?r:Pt([r,i.pathname])),pr(i)}function Hc(e,t,r){if(!r||!Uy(r))return{path:t};if(r.formMethod&&!eg(r.formMethod))return{path:t,error:st(405,{method:r.formMethod})};let n=()=>({path:t,error:st(400,{type:"invalid-body"})}),o=(r.formMethod||"get").toUpperCase(),a=Mh(t);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!tt(o))return n();let m=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((f,[w,k])=>`${f}${w}=${k}
`,""):String(r.body);return{path:t,submission:{formMethod:o,formAction:a,formEncType:r.formEncType,formData:void 0,json:void 0,text:m}}}else if(r.formEncType==="application/json"){if(!tt(o))return n();try{let m=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:t,submission:{formMethod:o,formAction:a,formEncType:r.formEncType,formData:void 0,json:m,text:void 0}}}catch{return n()}}}X(typeof FormData=="function","FormData is not available in this environment");let u,i;if(r.formData)u=nu(r.formData),i=r.formData;else if(r.body instanceof FormData)u=nu(r.body),i=r.body;else if(r.body instanceof URLSearchParams)u=r.body,i=Xc(u);else if(r.body==null)u=new URLSearchParams,i=new FormData;else try{u=new URLSearchParams(r.body),i=Xc(u)}catch{return n()}let s={formMethod:o,formAction:a,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:i,json:void 0,text:void 0};if(tt(s.formMethod))return{path:t,submission:s};let d=Vt(t);return e&&d.search&&us(d.search)&&u.append("index",""),d.search=`?${u}`,{path:pr(d),submission:s}}function Wc(e,t,r,n,l,o,a,u,i,s,d,m,f,w,k,N,P,p,h,g){var we;let S=g?et(g[1])?g[1].error:g[1].data:void 0,F=l.createURL(o.location),v=l.createURL(i),y;if(d&&o.errors){let A=Object.keys(o.errors)[0];y=a.findIndex(L=>L.route.id===A)}else if(g&&et(g[1])){let A=g[0];y=a.findIndex(L=>L.route.id===A)-1}let B=g?g[1].statusCode:void 0,z=B&&B>=400,$={currentUrl:F,currentParams:((we=o.matches[0])==null?void 0:we.params)||{},nextUrl:v,nextParams:a[0].params,...u,actionResult:S,actionStatus:B},G=a.map((A,L)=>{let{route:Z}=A,Ce=null;if(y!=null&&L>y?Ce=!1:Z.lazy?Ce=!0:Z.loader==null?Ce=!1:d?Ce=tu(Z,o.loaderData,o.errors):Vy(o.loaderData,o.matches[L],A)&&(Ce=!0),Ce!==null)return ru(r,n,e,A,s,t,Ce);let I=z?!1:m||F.pathname+F.search===v.pathname+v.search||F.search!==v.search||Hy(o.matches[L],A),j={...$,defaultShouldRevalidate:I},V=qo(A,j);return ru(r,n,e,A,s,t,V,j)}),ge=[];return k.forEach((A,L)=>{if(d||!a.some(ke=>ke.route.id===A.routeId)||w.has(L))return;let Z=o.fetchers.get(L),Ce=Z&&Z.state!=="idle"&&Z.data===void 0,I=tr(P,A.path,p);if(!I){if(h&&Ce)return;ge.push({key:L,routeId:A.routeId,path:A.path,matches:null,match:null,request:null,controller:null});return}if(N.has(L))return;let j=qn(I,A.path),V=new AbortController,te=Xr(l,A.path,V.signal),le=null;if(f.has(L))f.delete(L),le=vn(r,n,te,I,j,s,t);else if(Ce)m&&(le=vn(r,n,te,I,j,s,t));else{let ke={...$,defaultShouldRevalidate:z?!1:m};qo(j,ke)&&(le=vn(r,n,te,I,j,s,t,ke))}le&&ge.push({key:L,routeId:A.routeId,path:A.path,matches:le,match:j,request:te,controller:V})}),{dsMatches:G,revalidatingFetchers:ge}}function tu(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=t!=null&&e.id in t,l=r!=null&&r[e.id]!==void 0;return!n&&l?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!n&&!l}function Vy(e,t,r){let n=!t||r.route.id!==t.route.id,l=!e.hasOwnProperty(r.route.id);return n||l}function Hy(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function qo(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function Qc(e,t,r,n,l){let o;if(e){let i=n[e];X(i,`No route found to patch children into: routeId = ${e}`),i.children||(i.children=[]),o=i.children}else o=r;let a=t.filter(i=>!o.some(s=>Ph(i,s))),u=Go(a,l,[e||"_","patch",String((o==null?void 0:o.length)||"0")],n);o.push(...u)}function Ph(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,n)=>{var l;return(l=t.children)==null?void 0:l.some(o=>Ph(r,o))}):!1}var Kc=new WeakMap,bh=({key:e,route:t,manifest:r,mapRouteProperties:n})=>{let l=r[t.id];if(X(l,"No route found in manifest"),!l.lazy||typeof l.lazy!="object")return;let o=l.lazy[e];if(!o)return;let a=Kc.get(l);a||(a={},Kc.set(l,a));let u=a[e];if(u)return u;let i=(async()=>{let s=fy(e),m=l[e]!==void 0&&e!=="hasErrorBoundary";if(s)xe(!s,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),a[e]=Promise.resolve();else if(m)xe(!1,`Route "${l.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let f=await o();f!=null&&(Object.assign(l,{[e]:f}),Object.assign(l,n(l)))}typeof l.lazy=="object"&&(l.lazy[e]=void 0,Object.values(l.lazy).every(f=>f===void 0)&&(l.lazy=void 0))})();return a[e]=i,i},Yc=new WeakMap;function Wy(e,t,r,n,l){let o=r[e.id];if(X(o,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let d=Yc.get(o);if(d)return{lazyRoutePromise:d,lazyHandlerPromise:d};let m=(async()=>{X(typeof e.lazy=="function","No lazy route function found");let f=await e.lazy(),w={};for(let k in f){let N=f[k];if(N===void 0)continue;let P=py(k),h=o[k]!==void 0&&k!=="hasErrorBoundary";P?xe(!P,"Route property "+k+" is not a supported property to be returned from a lazy route function. This property will be ignored."):h?xe(!h,`Route "${o.id}" has a static property "${k}" defined but its lazy function is also returning a value for this property. The lazy route property "${k}" will be ignored.`):w[k]=N}Object.assign(o,w),Object.assign(o,{...n(o),lazy:void 0})})();return Yc.set(o,m),m.catch(()=>{}),{lazyRoutePromise:m,lazyHandlerPromise:m}}let a=Object.keys(e.lazy),u=[],i;for(let d of a){if(l&&l.includes(d))continue;let m=bh({key:d,route:e,manifest:r,mapRouteProperties:n});m&&(u.push(m),d===t&&(i=m))}let s=u.length>0?Promise.all(u).then(()=>{}):void 0;return s==null||s.catch(()=>{}),i==null||i.catch(()=>{}),{lazyRoutePromise:s,lazyHandlerPromise:i}}async function Gc(e){let t=e.matches.filter(l=>l.shouldLoad),r={};return(await Promise.all(t.map(l=>l.resolve()))).forEach((l,o)=>{r[t[o].route.id]=l}),r}async function Qy(e){return e.matches.some(t=>t.route.unstable_middleware)?_h(e,!1,()=>Gc(e),(t,r)=>({[r]:{type:"error",result:t}})):Gc(e)}async function _h(e,t,r,n){let{matches:l,request:o,params:a,context:u}=e,i={handlerResult:void 0};try{let s=l.flatMap(m=>m.route.unstable_middleware?m.route.unstable_middleware.map(f=>[m.route.id,f]):[]),d=await Lh({request:o,params:a,context:u},s,t,i,r);return t?d:i.handlerResult}catch(s){if(!i.middlewareError)throw s;let d=await n(i.middlewareError.error,i.middlewareError.routeId);return t||!i.handlerResult?d:Object.assign(i.handlerResult,d)}}async function Lh(e,t,r,n,l,o=0){let{request:a}=e;if(a.signal.aborted)throw a.signal.reason?a.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${a.method} ${a.url}`);let u=t[o];if(!u)return n.handlerResult=await l(),n.handlerResult;let[i,s]=u,d=!1,m,f=async()=>{if(d)throw new Error("You may only call `next()` once per middleware");d=!0;let w=await Lh(e,t,r,n,l,o+1);if(r)return m=w,m};try{let w=await s({request:e.request,params:e.params,context:e.context},f);return d?w===void 0?m:w:f()}catch(w){throw n.middlewareError?n.middlewareError.error!==w&&(n.middlewareError={routeId:i,error:w}):n.middlewareError={routeId:i,error:w},w}}function Ih(e,t,r,n,l){let o=bh({key:"unstable_middleware",route:n.route,manifest:t,mapRouteProperties:e}),a=Wy(n.route,tt(r.method)?"action":"loader",t,e,l);return{middleware:o,route:a.lazyRoutePromise,handler:a.lazyHandlerPromise}}function ru(e,t,r,n,l,o,a,u=null){let i=!1,s=Ih(e,t,r,n,l);return{...n,_lazyPromises:s,shouldLoad:a,unstable_shouldRevalidateArgs:u,unstable_shouldCallHandler(d){return i=!0,u?typeof d=="boolean"?qo(n,{...u,defaultShouldRevalidate:d}):qo(n,u):a},resolve(d){return i||a||d&&r.method==="GET"&&(n.route.lazy||n.route.loader)?Yy({request:r,match:n,lazyHandlerPromise:s==null?void 0:s.handler,lazyRoutePromise:s==null?void 0:s.route,handlerOverride:d,scopedContext:o}):Promise.resolve({type:"data",result:void 0})}}}function vn(e,t,r,n,l,o,a,u=null){return n.map(i=>i.route.id!==l.route.id?{...i,shouldLoad:!1,unstable_shouldRevalidateArgs:u,unstable_shouldCallHandler:()=>!1,_lazyPromises:Ih(e,t,r,i,o),resolve:()=>Promise.resolve({type:"data",result:void 0})}:ru(e,t,r,i,o,a,!0,u))}async function Ky(e,t,r,n,l,o){r.some(s=>{var d;return(d=s._lazyPromises)==null?void 0:d.middleware})&&await Promise.all(r.map(s=>{var d;return(d=s._lazyPromises)==null?void 0:d.middleware}));let a={request:t,params:r[0].params,context:l,matches:r},i=await e({...a,fetcherKey:n,unstable_runClientMiddleware:o?()=>{throw new Error("You cannot call `unstable_runClientMiddleware()` from a static handler `dataStrategy`. Middleware is run outside of `dataStrategy` during SSR in order to bubble up the Response.  You can enable middleware via the `respond` API in `query`/`queryRoute`")}:s=>{let d=a;return _h(d,!1,()=>s({...d,fetcherKey:n,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(m,f)=>({[f]:{type:"error",result:m}}))}});try{await Promise.all(r.flatMap(s=>{var d,m;return[(d=s._lazyPromises)==null?void 0:d.handler,(m=s._lazyPromises)==null?void 0:m.route]}))}catch{}return i}async function Yy({request:e,match:t,lazyHandlerPromise:r,lazyRoutePromise:n,handlerOverride:l,scopedContext:o}){let a,u,i=tt(e.method),s=i?"action":"loader",d=m=>{let f,w=new Promise((P,p)=>f=p);u=()=>f(),e.signal.addEventListener("abort",u);let k=P=>typeof m!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${s}" [routeId: ${t.route.id}]`)):m({request:e,params:t.params,context:o},...P!==void 0?[P]:[]),N=(async()=>{try{return{type:"data",result:await(l?l(p=>k(p)):k())}}catch(P){return{type:"error",result:P}}})();return Promise.race([N,w])};try{let m=i?t.route.action:t.route.loader;if(r||n)if(m){let f,[w]=await Promise.all([d(m).catch(k=>{f=k}),r,n]);if(f!==void 0)throw f;a=w}else{await r;let f=i?t.route.action:t.route.loader;if(f)[a]=await Promise.all([d(f),n]);else if(s==="action"){let w=new URL(e.url),k=w.pathname+w.search;throw st(405,{method:e.method,pathname:k,routeId:t.route.id})}else return{type:"data",result:void 0}}else if(m)a=await d(m);else{let f=new URL(e.url),w=f.pathname+f.search;throw st(404,{pathname:w})}}catch(m){return{type:"error",result:m}}finally{u&&e.signal.removeEventListener("abort",u)}return a}async function Gy(e){var n,l,o,a,u,i;let{result:t,type:r}=e;if(Th(t)){let s;try{let d=t.headers.get("Content-Type");d&&/\bapplication\/json\b/.test(d)?t.body==null?s=null:s=await t.json():s=await t.text()}catch(d){return{type:"error",error:d}}return r==="error"?{type:"error",error:new Xo(t.status,t.statusText,s),statusCode:t.status,headers:t.headers}:{type:"data",data:s,statusCode:t.status,headers:t.headers}}return r==="error"?rd(t)?t.data instanceof Error?{type:"error",error:t.data,statusCode:(n=t.init)==null?void 0:n.status,headers:(l=t.init)!=null&&l.headers?new Headers(t.init.headers):void 0}:{type:"error",error:new Xo(((o=t.init)==null?void 0:o.status)||500,void 0,t.data),statusCode:Dl(t)?t.status:void 0,headers:(a=t.init)!=null&&a.headers?new Headers(t.init.headers):void 0}:{type:"error",error:t,statusCode:Dl(t)?t.status:void 0}:rd(t)?{type:"data",data:t.data,statusCode:(u=t.init)==null?void 0:u.status,headers:(i=t.init)!=null&&i.headers?new Headers(t.init.headers):void 0}:{type:"data",data:t}}function Jy(e,t,r,n,l){let o=e.headers.get("Location");if(X(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!is.test(o)){let a=n.slice(0,n.findIndex(u=>u.route.id===r)+1);o=eu(new URL(t.url),a,l,o),e.headers.set("Location",o)}return e}function Jc(e,t,r){if(is.test(e)){let n=e,l=n.startsWith("//")?new URL(t.protocol+n):new URL(n),o=yt(l.pathname,r)!=null;if(l.origin===t.origin&&o)return l.pathname+l.search+l.hash}return e}function Xr(e,t,r,n){let l=e.createURL(Mh(t)).toString(),o={signal:r};if(n&&tt(n.formMethod)){let{formMethod:a,formEncType:u}=n;o.method=a.toUpperCase(),u==="application/json"?(o.headers=new Headers({"Content-Type":u}),o.body=JSON.stringify(n.json)):u==="text/plain"?o.body=n.text:u==="application/x-www-form-urlencoded"&&n.formData?o.body=nu(n.formData):o.body=n.formData}return new Request(l,o)}function nu(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function Xc(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function Xy(e,t,r,n=!1,l=!1){let o={},a=null,u,i=!1,s={},d=r&&et(r[1])?r[1].error:void 0;return e.forEach(m=>{if(!(m.route.id in t))return;let f=m.route.id,w=t[f];if(X(!_r(w),"Cannot handle redirect results in processLoaderData"),et(w)){let k=w.error;if(d!==void 0&&(k=d,d=void 0),a=a||{},l)a[f]=k;else{let N=Br(e,f);a[N.route.id]==null&&(a[N.route.id]=k)}n||(o[f]=Ah),i||(i=!0,u=Dl(w.error)?w.error.status:500),w.headers&&(s[f]=w.headers)}else o[f]=w.data,w.statusCode&&w.statusCode!==200&&!i&&(u=w.statusCode),w.headers&&(s[f]=w.headers)}),d!==void 0&&r&&(a={[r[0]]:d},r[2]&&(o[r[2]]=void 0)),{loaderData:o,errors:a,statusCode:u||200,loaderHeaders:s}}function qc(e,t,r,n,l,o){let{loaderData:a,errors:u}=Xy(t,r,n);return l.filter(i=>!i.matches||i.matches.some(s=>s.shouldLoad)).forEach(i=>{let{key:s,match:d,controller:m}=i,f=o[s];if(X(f,"Did not find corresponding fetcher result"),!(m&&m.signal.aborted))if(et(f)){let w=Br(e.matches,d==null?void 0:d.route.id);u&&u[w.route.id]||(u={...u,[w.route.id]:f.error}),e.fetchers.delete(s)}else if(_r(f))X(!1,"Unhandled fetcher revalidation redirect");else{let w=Jt(f.data);e.fetchers.set(s,w)}}),{loaderData:a,errors:u}}function Zc(e,t,r,n){let l=Object.entries(t).filter(([,o])=>o!==Ah).reduce((o,[a,u])=>(o[a]=u,o),{});for(let o of r){let a=o.route.id;if(!t.hasOwnProperty(a)&&e.hasOwnProperty(a)&&o.route.loader&&(l[a]=e[a]),n&&n.hasOwnProperty(a))break}return l}function ed(e){return e?et(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function Br(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function td(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function st(e,{pathname:t,routeId:r,method:n,type:l,message:o}={}){let a="Unknown Server Error",u="Unknown @remix-run/router error";return e===400?(a="Bad Request",n&&t&&r?u=`You made a ${n} request to "${t}" but did not provide a \`loader\` for route "${r}", so there is no way to handle the request.`:l==="invalid-body"&&(u="Unable to encode submission body")):e===403?(a="Forbidden",u=`Route "${r}" does not match URL "${t}"`):e===404?(a="Not Found",u=`No route matches URL "${t}"`):e===405&&(a="Method Not Allowed",n&&t&&r?u=`You made a ${n.toUpperCase()} request to "${t}" but did not provide an \`action\` for route "${r}", so there is no way to handle the request.`:n&&(u=`Invalid request method "${n.toUpperCase()}"`)),new Xo(e||500,a,new Error(u),!0)}function lo(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[n,l]=t[r];if(_r(l))return{key:n,result:l}}}function Mh(e){let t=typeof e=="string"?Vt(e):e;return pr({...t,hash:""})}function qy(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function Zy(e){return Th(e.result)&&Ty.has(e.result.status)}function et(e){return e.type==="error"}function _r(e){return(e&&e.type)==="redirect"}function rd(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function Th(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function eg(e){return My.has(e.toUpperCase())}function tt(e){return Ly.has(e.toUpperCase())}function us(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function qn(e,t){let r=typeof t=="string"?Vt(t).search:t.search;if(e[e.length-1].route.index&&us(r||""))return e[e.length-1];let n=Fh(e);return n[n.length-1]}function nd(e){let{formMethod:t,formAction:r,formEncType:n,text:l,formData:o,json:a}=e;if(!(!t||!r||!n)){if(l!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:l};if(o!=null)return{formMethod:t,formAction:r,formEncType:n,formData:o,json:void 0,text:void 0};if(a!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:a,text:void 0}}}function oi(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function tg(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function Wn(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function rg(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function Jt(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function ng(e,t){try{let r=e.sessionStorage.getItem(Bh);if(r){let n=JSON.parse(r);for(let[l,o]of Object.entries(n||{}))o&&Array.isArray(o)&&t.set(l,new Set(o||[]))}}catch{}}function lg(e,t){if(t.size>0){let r={};for(let[n,l]of t)r[n]=[...l];try{e.sessionStorage.setItem(Bh,JSON.stringify(r))}catch(n){xe(!1,`Failed to save applied view transitions in sessionStorage (${n}).`)}}}function og(){let e,t,r=new Promise((n,l)=>{e=async o=>{n(o);try{await r}catch{}},t=async o=>{l(o);try{await r}catch{}}});return{promise:r,resolve:e,reject:t}}var Vr=C.exports.createContext(null);Vr.displayName="DataRouter";var _l=C.exports.createContext(null);_l.displayName="DataRouterState";var ss=C.exports.createContext({isTransitioning:!1});ss.displayName="ViewTransition";var zh=C.exports.createContext(new Map);zh.displayName="Fetchers";var ag=C.exports.createContext(null);ag.displayName="Await";var Ct=C.exports.createContext(null);Ct.displayName="Navigation";var Ll=C.exports.createContext(null);Ll.displayName="Location";var Nt=C.exports.createContext({outlet:null,matches:[],isDataRoute:!1});Nt.displayName="Route";var cs=C.exports.createContext(null);cs.displayName="RouteError";function ig(e,{relative:t}={}){X(An(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:n}=C.exports.useContext(Ct),{hash:l,pathname:o,search:a}=Il(e,{relative:t}),u=o;return r!=="/"&&(u=o==="/"?r:Pt([r,o])),n.createHref({pathname:u,search:a,hash:l})}function An(){return C.exports.useContext(Ll)!=null}function vr(){return X(An(),"useLocation() may be used only in the context of a <Router> component."),C.exports.useContext(Ll).location}var Oh="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function $h(e){C.exports.useContext(Ct).static||C.exports.useLayoutEffect(e)}function jh(){let{isDataRoute:e}=C.exports.useContext(Nt);return e?Sg():ug()}function ug(){X(An(),"useNavigate() may be used only in the context of a <Router> component.");let e=C.exports.useContext(Vr),{basename:t,navigator:r}=C.exports.useContext(Ct),{matches:n}=C.exports.useContext(Nt),{pathname:l}=vr(),o=JSON.stringify(Sa(n)),a=C.exports.useRef(!1);return $h(()=>{a.current=!0}),C.exports.useCallback((i,s={})=>{if(xe(a.current,Oh),!a.current)return;if(typeof i=="number"){r.go(i);return}let d=Ea(i,JSON.parse(o),l,s.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Pt([t,d.pathname])),(s.replace?r.replace:r.push)(d,s.state,s)},[t,r,o,l,e])}var sg=C.exports.createContext(null);function cg(e){let t=C.exports.useContext(Nt).outlet;return t&&C.exports.createElement(sg.Provider,{value:e},t)}function Il(e,{relative:t}={}){let{matches:r}=C.exports.useContext(Nt),{pathname:n}=vr(),l=JSON.stringify(Sa(r));return C.exports.useMemo(()=>Ea(e,JSON.parse(l),n,t==="path"),[e,l,n,t])}function dg(e,t,r,n){var p;X(An(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:l}=C.exports.useContext(Ct),{matches:o}=C.exports.useContext(Nt),a=o[o.length-1],u=a?a.params:{},i=a?a.pathname:"/",s=a?a.pathnameBase:"/",d=a&&a.route;{let h=d&&d.path||"";Uh(i,!d||h.endsWith("*")||h.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${i}" (under <Route path="${h}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${h}"> to <Route path="${h==="/"?"*":`${h}/*`}">.`)}let m=vr(),f;if(t){let h=typeof t=="string"?Vt(t):t;X(s==="/"||((p=h.pathname)==null?void 0:p.startsWith(s)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${s}" but pathname "${h.pathname}" was given in the \`location\` prop.`),f=h}else f=m;let w=f.pathname||"/",k=w;if(s!=="/"){let h=s.replace(/^\//,"").split("/");k="/"+w.replace(/^\//,"").split("/").slice(h.length).join("/")}let N=tr(e,{pathname:k});xe(d||N!=null,`No routes matched location "${f.pathname}${f.search}${f.hash}" `),xe(N==null||N[N.length-1].route.element!==void 0||N[N.length-1].route.Component!==void 0||N[N.length-1].route.lazy!==void 0,`Matched leaf route at location "${f.pathname}${f.search}${f.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let P=yg(N&&N.map(h=>Object.assign({},h,{params:Object.assign({},u,h.params),pathname:Pt([s,l.encodeLocation?l.encodeLocation(h.pathname).pathname:h.pathname]),pathnameBase:h.pathnameBase==="/"?s:Pt([s,l.encodeLocation?l.encodeLocation(h.pathnameBase).pathname:h.pathnameBase])})),o,r,n);return t&&P?C.exports.createElement(Ll.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...f},navigationType:"POP"}},P):P}function fg(){let e=kg(),t=Dl(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n="rgba(200,200,200, 0.5)",l={padding:"0.5rem",backgroundColor:n},o={padding:"2px 4px",backgroundColor:n},a=null;return console.error("Error handled by React Router default ErrorBoundary:",e),a=C.exports.createElement(C.exports.Fragment,null,C.exports.createElement("p",null,"\u{1F4BF} Hey developer \u{1F44B}"),C.exports.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",C.exports.createElement("code",{style:o},"ErrorBoundary")," or"," ",C.exports.createElement("code",{style:o},"errorElement")," prop on your route.")),C.exports.createElement(C.exports.Fragment,null,C.exports.createElement("h2",null,"Unexpected Application Error!"),C.exports.createElement("h3",{style:{fontStyle:"italic"}},t),r?C.exports.createElement("pre",{style:l},r):null,a)}var hg=C.exports.createElement(fg,null),pg=class extends C.exports.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?C.exports.createElement(Nt.Provider,{value:this.props.routeContext},C.exports.createElement(cs.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function mg({routeContext:e,match:t,children:r}){let n=C.exports.useContext(Vr);return n&&n.static&&n.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=t.route.id),C.exports.createElement(Nt.Provider,{value:e},r)}function yg(e,t=[],r=null,n=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let l=e,o=r==null?void 0:r.errors;if(o!=null){let i=l.findIndex(s=>s.route.id&&(o==null?void 0:o[s.route.id])!==void 0);X(i>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),l=l.slice(0,Math.min(l.length,i+1))}let a=!1,u=-1;if(r)for(let i=0;i<l.length;i++){let s=l[i];if((s.route.HydrateFallback||s.route.hydrateFallbackElement)&&(u=i),s.route.id){let{loaderData:d,errors:m}=r,f=s.route.loader&&!d.hasOwnProperty(s.route.id)&&(!m||m[s.route.id]===void 0);if(s.route.lazy||f){a=!0,u>=0?l=l.slice(0,u+1):l=[l[0]];break}}}return l.reduceRight((i,s,d)=>{let m,f=!1,w=null,k=null;r&&(m=o&&s.route.id?o[s.route.id]:void 0,w=s.route.errorElement||hg,a&&(u<0&&d===0?(Uh("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),f=!0,k=null):u===d&&(f=!0,k=s.route.hydrateFallbackElement||null)));let N=t.concat(l.slice(0,d+1)),P=()=>{let p;return m?p=w:f?p=k:s.route.Component?p=C.exports.createElement(s.route.Component,null):s.route.element?p=s.route.element:p=i,C.exports.createElement(mg,{match:s,routeContext:{outlet:i,matches:N,isDataRoute:r!=null},children:p})};return r&&(s.route.ErrorBoundary||s.route.errorElement||d===0)?C.exports.createElement(pg,{location:r.location,revalidation:r.revalidation,component:w,error:m,children:P(),routeContext:{outlet:null,matches:N,isDataRoute:!0}}):P()},null)}function ds(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function gg(e){let t=C.exports.useContext(Vr);return X(t,ds(e)),t}function vg(e){let t=C.exports.useContext(_l);return X(t,ds(e)),t}function xg(e){let t=C.exports.useContext(Nt);return X(t,ds(e)),t}function fs(e){let t=xg(e),r=t.matches[t.matches.length-1];return X(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function wg(){return fs("useRouteId")}function kg(){var n;let e=C.exports.useContext(cs),t=vg("useRouteError"),r=fs("useRouteError");return e!==void 0?e:(n=t.errors)==null?void 0:n[r]}function Sg(){let{router:e}=gg("useNavigate"),t=fs("useNavigate"),r=C.exports.useRef(!1);return $h(()=>{r.current=!0}),C.exports.useCallback(async(l,o={})=>{xe(r.current,Oh),r.current&&(typeof l=="number"?e.navigate(l):await e.navigate(l,{fromRouteId:t,...o}))},[e,t])}var ld={};function Uh(e,t,r){!t&&!ld[e]&&(ld[e]=!0,xe(!1,r))}var od={};function ad(e,t){!e&&!od[t]&&(od[t]=!0,console.warn(t))}function Eg(e){let t={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&xe(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(t,{element:C.exports.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&xe(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(t,{hydrateFallbackElement:C.exports.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&xe(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(t,{errorElement:C.exports.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),t}var Cg=["HydrateFallback","hydrateFallbackElement"],Ng=class{constructor(){this.status="pending",this.promise=new Promise((e,t)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",e(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",t(r))}})}};function Dg({router:e,flushSync:t}){let[r,n]=C.exports.useState(e.state),[l,o]=C.exports.useState(),[a,u]=C.exports.useState({isTransitioning:!1}),[i,s]=C.exports.useState(),[d,m]=C.exports.useState(),[f,w]=C.exports.useState(),k=C.exports.useRef(new Map),N=C.exports.useCallback((g,{deletedFetchers:S,flushSync:F,viewTransitionOpts:v})=>{g.fetchers.forEach((B,z)=>{B.data!==void 0&&k.current.set(z,B.data)}),S.forEach(B=>k.current.delete(B)),ad(F===!1||t!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let y=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(ad(v==null||y,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!v||!y){t&&F?t(()=>n(g)):C.exports.startTransition(()=>n(g));return}if(t&&F){t(()=>{d&&(i&&i.resolve(),d.skipTransition()),u({isTransitioning:!0,flushSync:!0,currentLocation:v.currentLocation,nextLocation:v.nextLocation})});let B=e.window.document.startViewTransition(()=>{t(()=>n(g))});B.finished.finally(()=>{t(()=>{s(void 0),m(void 0),o(void 0),u({isTransitioning:!1})})}),t(()=>m(B));return}d?(i&&i.resolve(),d.skipTransition(),w({state:g,currentLocation:v.currentLocation,nextLocation:v.nextLocation})):(o(g),u({isTransitioning:!0,flushSync:!1,currentLocation:v.currentLocation,nextLocation:v.nextLocation}))},[e.window,t,d,i]);C.exports.useLayoutEffect(()=>e.subscribe(N),[e,N]),C.exports.useEffect(()=>{a.isTransitioning&&!a.flushSync&&s(new Ng)},[a]),C.exports.useEffect(()=>{if(i&&l&&e.window){let g=l,S=i.promise,F=e.window.document.startViewTransition(async()=>{C.exports.startTransition(()=>n(g)),await S});F.finished.finally(()=>{s(void 0),m(void 0),o(void 0),u({isTransitioning:!1})}),m(F)}},[l,i,e.window]),C.exports.useEffect(()=>{i&&l&&r.location.key===l.location.key&&i.resolve()},[i,d,r.location,l]),C.exports.useEffect(()=>{!a.isTransitioning&&f&&(o(f.state),u({isTransitioning:!0,flushSync:!1,currentLocation:f.currentLocation,nextLocation:f.nextLocation}),w(void 0))},[a.isTransitioning,f]);let P=C.exports.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:g=>e.navigate(g),push:(g,S,F)=>e.navigate(g,{state:S,preventScrollReset:F==null?void 0:F.preventScrollReset}),replace:(g,S,F)=>e.navigate(g,{replace:!0,state:S,preventScrollReset:F==null?void 0:F.preventScrollReset})}),[e]),p=e.basename||"/",h=C.exports.useMemo(()=>({router:e,navigator:P,static:!1,basename:p}),[e,P,p]);return c(as,{children:c(Vr.Provider,{value:h,children:c(_l.Provider,{value:r,children:c(zh.Provider,{value:k.current,children:c(ss.Provider,{value:a,children:c(Pg,{basename:p,location:r.location,navigationType:r.historyAction,navigator:P,children:c(Fg,{routes:e.routes,future:e.future,state:r})})})})})})})}var Fg=C.exports.memo(Rg);function Rg({routes:e,future:t,state:r}){return dg(e,void 0,r,t)}function Bg({to:e,replace:t,state:r,relative:n}){X(An(),"<Navigate> may be used only in the context of a <Router> component.");let{static:l}=C.exports.useContext(Ct);xe(!l,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:o}=C.exports.useContext(Nt),{pathname:a}=vr(),u=jh(),i=Ea(e,Sa(o),a,n==="path"),s=JSON.stringify(i);return C.exports.useEffect(()=>{u(JSON.parse(s),{replace:t,state:r,relative:n})},[u,s,n,t,r]),null}function Ag(e){return cg(e.context)}function Pg({basename:e="/",children:t=null,location:r,navigationType:n="POP",navigator:l,static:o=!1}){X(!An(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let a=e.replace(/^\/*/,"/"),u=C.exports.useMemo(()=>({basename:a,navigator:l,static:o,future:{}}),[a,l,o]);typeof r=="string"&&(r=Vt(r));let{pathname:i="/",search:s="",hash:d="",state:m=null,key:f="default"}=r,w=C.exports.useMemo(()=>{let k=yt(i,a);return k==null?null:{location:{pathname:k,search:s,hash:d,state:m,key:f},navigationType:n}},[a,i,s,d,m,f,n]);return xe(w!=null,`<Router basename="${a}"> is not able to match the URL "${i}${s}${d}" because it does not start with the basename, so the <Router> won't render anything.`),w==null?null:c(Ct.Provider,{value:u,children:c(Ll.Provider,{children:t,value:w})})}var ko="get",So="application/x-www-form-urlencoded";function Ca(e){return e!=null&&typeof e.tagName=="string"}function bg(e){return Ca(e)&&e.tagName.toLowerCase()==="button"}function _g(e){return Ca(e)&&e.tagName.toLowerCase()==="form"}function Lg(e){return Ca(e)&&e.tagName.toLowerCase()==="input"}function Ig(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Mg(e,t){return e.button===0&&(!t||t==="_self")&&!Ig(e)}var oo=null;function Tg(){if(oo===null)try{new FormData(document.createElement("form"),0),oo=!1}catch{oo=!0}return oo}var zg=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function ai(e){return e!=null&&!zg.has(e)?(xe(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${So}"`),null):e}function Og(e,t){let r,n,l,o,a;if(_g(e)){let u=e.getAttribute("action");n=u?yt(u,t):null,r=e.getAttribute("method")||ko,l=ai(e.getAttribute("enctype"))||So,o=new FormData(e)}else if(bg(e)||Lg(e)&&(e.type==="submit"||e.type==="image")){let u=e.form;if(u==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let i=e.getAttribute("formaction")||u.getAttribute("action");if(n=i?yt(i,t):null,r=e.getAttribute("formmethod")||u.getAttribute("method")||ko,l=ai(e.getAttribute("formenctype"))||ai(u.getAttribute("enctype"))||So,o=new FormData(u,e),!Tg()){let{name:s,type:d,value:m}=e;if(d==="image"){let f=s?`${s}.`:"";o.append(`${f}x`,"0"),o.append(`${f}y`,"0")}else s&&o.append(s,m)}}else{if(Ca(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=ko,n=null,l=So,a=e}return o&&l==="text/plain"&&(a=o,o=void 0),{action:n,method:r.toLowerCase(),encType:l,formData:o,body:a}}function hs(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function $g(e,t){if(e.id in t)return t[e.id];try{let r=await Vm(()=>import(e.module),[]);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function jg(e){return e!=null&&typeof e.page=="string"}function Ug(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Vg(e,t,r){let n=await Promise.all(e.map(async l=>{let o=t.routes[l.route.id];if(o){let a=await $g(o,r);return a.links?a.links():[]}return[]}));return Kg(n.flat(1).filter(Ug).filter(l=>l.rel==="stylesheet"||l.rel==="preload").map(l=>l.rel==="stylesheet"?{...l,rel:"prefetch",as:"style"}:{...l,rel:"prefetch"}))}function id(e,t,r,n,l,o){let a=(i,s)=>r[s]?i.route.id!==r[s].route.id:!0,u=(i,s)=>{var d;return r[s].pathname!==i.pathname||((d=r[s].route.path)==null?void 0:d.endsWith("*"))&&r[s].params["*"]!==i.params["*"]};return o==="assets"?t.filter((i,s)=>a(i,s)||u(i,s)):o==="data"?t.filter((i,s)=>{var m;let d=n.routes[i.route.id];if(!d||!d.hasLoader)return!1;if(a(i,s)||u(i,s))return!0;if(i.route.shouldRevalidate){let f=i.route.shouldRevalidate({currentUrl:new URL(l.pathname+l.search+l.hash,window.origin),currentParams:((m=r[0])==null?void 0:m.params)||{},nextUrl:new URL(e,window.origin),nextParams:i.params,defaultShouldRevalidate:!0});if(typeof f=="boolean")return f}return!0}):[]}function Hg(e,t,{includeHydrateFallback:r}={}){return Wg(e.map(n=>{let l=t.routes[n.route.id];if(!l)return[];let o=[l.module];return l.clientActionModule&&(o=o.concat(l.clientActionModule)),l.clientLoaderModule&&(o=o.concat(l.clientLoaderModule)),r&&l.hydrateFallbackModule&&(o=o.concat(l.hydrateFallbackModule)),l.imports&&(o=o.concat(l.imports)),o}).flat(1))}function Wg(e){return[...new Set(e)]}function Qg(e){let t={},r=Object.keys(e).sort();for(let n of r)t[n]=e[n];return t}function Kg(e,t){let r=new Set,n=new Set(t);return e.reduce((l,o)=>{if(t&&!jg(o)&&o.as==="script"&&o.href&&n.has(o.href))return l;let u=JSON.stringify(Qg(o));return r.has(u)||(r.add(u),l.push({key:u,link:o})),l},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Yg=new Set([100,101,204,205]);function Gg(e,t){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname="_root.data":t&&yt(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function Vh(){let e=C.exports.useContext(Vr);return hs(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Jg(){let e=C.exports.useContext(_l);return hs(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var ps=C.exports.createContext(void 0);ps.displayName="FrameworkContext";function Hh(){let e=C.exports.useContext(ps);return hs(e,"You must render this element inside a <HydratedRouter> element"),e}function Xg(e,t){let r=C.exports.useContext(ps),[n,l]=C.exports.useState(!1),[o,a]=C.exports.useState(!1),{onFocus:u,onBlur:i,onMouseEnter:s,onMouseLeave:d,onTouchStart:m}=t,f=C.exports.useRef(null);C.exports.useEffect(()=>{if(e==="render"&&a(!0),e==="viewport"){let N=p=>{p.forEach(h=>{a(h.isIntersecting)})},P=new IntersectionObserver(N,{threshold:.5});return f.current&&P.observe(f.current),()=>{P.disconnect()}}},[e]),C.exports.useEffect(()=>{if(n){let N=setTimeout(()=>{a(!0)},100);return()=>{clearTimeout(N)}}},[n]);let w=()=>{l(!0)},k=()=>{l(!1),a(!1)};return r?e!=="intent"?[o,f,{}]:[o,f,{onFocus:Qn(u,w),onBlur:Qn(i,k),onMouseEnter:Qn(s,w),onMouseLeave:Qn(d,k),onTouchStart:Qn(m,w)}]:[!1,f,{}]}function Qn(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function qg({page:e,...t}){let{router:r}=Vh(),n=C.exports.useMemo(()=>tr(r.routes,e,r.basename),[r.routes,e,r.basename]);return n?C.exports.createElement(ev,{page:e,matches:n,...t}):null}function Zg(e){let{manifest:t,routeModules:r}=Hh(),[n,l]=C.exports.useState([]);return C.exports.useEffect(()=>{let o=!1;return Vg(e,t,r).then(a=>{o||l(a)}),()=>{o=!0}},[e,t,r]),n}function ev({page:e,matches:t,...r}){let n=vr(),{manifest:l,routeModules:o}=Hh(),{basename:a}=Vh(),{loaderData:u,matches:i}=Jg(),s=C.exports.useMemo(()=>id(e,t,i,l,n,"data"),[e,t,i,l,n]),d=C.exports.useMemo(()=>id(e,t,i,l,n,"assets"),[e,t,i,l,n]),m=C.exports.useMemo(()=>{if(e===n.pathname+n.search+n.hash)return[];let k=new Set,N=!1;if(t.forEach(p=>{var g;let h=l.routes[p.route.id];!h||!h.hasLoader||(!s.some(S=>S.route.id===p.route.id)&&p.route.id in u&&((g=o[p.route.id])==null?void 0:g.shouldRevalidate)||h.hasClientLoader?N=!0:k.add(p.route.id))}),k.size===0)return[];let P=Gg(e,a);return N&&k.size>0&&P.searchParams.set("_routes",t.filter(p=>k.has(p.route.id)).map(p=>p.route.id).join(",")),[P.pathname+P.search]},[a,u,n,l,s,t,e,o]),f=C.exports.useMemo(()=>Hg(d,l),[d,l]),w=Zg(d);return C.exports.createElement(C.exports.Fragment,null,m.map(k=>C.exports.createElement("link",{key:k,rel:"prefetch",as:"fetch",href:k,...r})),f.map(k=>C.exports.createElement("link",{key:k,rel:"modulepreload",href:k,...r})),w.map(({key:k,link:N})=>C.exports.createElement("link",{key:k,...N})))}function tv(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var Wh=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Wh&&(window.__reactRouterVersion="7.6.3")}catch{}function rv(e,t){return jy({basename:t==null?void 0:t.basename,unstable_getContext:t==null?void 0:t.unstable_getContext,future:t==null?void 0:t.future,history:uy({window:t==null?void 0:t.window}),hydrationData:(t==null?void 0:t.hydrationData)||nv(),routes:e,mapRouteProperties:Eg,hydrationRouteProperties:Cg,dataStrategy:t==null?void 0:t.dataStrategy,patchRoutesOnNavigation:t==null?void 0:t.patchRoutesOnNavigation,window:t==null?void 0:t.window}).initialize()}function nv(){let e=window==null?void 0:window.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:lv(e.errors)}),e}function lv(e){if(!e)return null;let t=Object.entries(e),r={};for(let[n,l]of t)if(l&&l.__type==="RouteErrorResponse")r[n]=new Xo(l.status,l.statusText,l.data,l.internal===!0);else if(l&&l.__type==="Error"){if(l.__subType){let o=window[l.__subType];if(typeof o=="function")try{let a=new o(l.message);a.stack="",r[n]=a}catch{}}if(r[n]==null){let o=new Error(l.message);o.stack="",r[n]=o}}else r[n]=l;return r}var Qh=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Kh=C.exports.forwardRef(function({onClick:t,discover:r="render",prefetch:n="none",relative:l,reloadDocument:o,replace:a,state:u,target:i,to:s,preventScrollReset:d,viewTransition:m,...f},w){let{basename:k}=C.exports.useContext(Ct),N=typeof s=="string"&&Qh.test(s),P,p=!1;if(typeof s=="string"&&N&&(P=s,Wh))try{let z=new URL(window.location.href),$=s.startsWith("//")?new URL(z.protocol+s):new URL(s),G=yt($.pathname,k);$.origin===z.origin&&G!=null?s=G+$.search+$.hash:p=!0}catch{xe(!1,`<Link to="${s}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let h=ig(s,{relative:l}),[g,S,F]=Xg(n,f),v=iv(s,{replace:a,state:u,target:i,preventScrollReset:d,relative:l,viewTransition:m});function y(z){t&&t(z),z.defaultPrevented||v(z)}let B=C.exports.createElement("a",{...f,...F,href:P||h,onClick:p||o?t:y,ref:tv(w,S),target:i,"data-discover":!N&&r==="render"?"true":void 0});return g&&!N?C.exports.createElement(C.exports.Fragment,null,B,C.exports.createElement(qg,{page:h})):B});Kh.displayName="Link";var Yh=C.exports.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:n="",end:l=!1,style:o,to:a,viewTransition:u,children:i,...s},d){let m=Il(a,{relative:s.relative}),f=vr(),w=C.exports.useContext(_l),{navigator:k,basename:N}=C.exports.useContext(Ct),P=w!=null&&fv(m)&&u===!0,p=k.encodeLocation?k.encodeLocation(m).pathname:m.pathname,h=f.pathname,g=w&&w.navigation&&w.navigation.location?w.navigation.location.pathname:null;r||(h=h.toLowerCase(),g=g?g.toLowerCase():null,p=p.toLowerCase()),g&&N&&(g=yt(g,N)||g);const S=p!=="/"&&p.endsWith("/")?p.length-1:p.length;let F=h===p||!l&&h.startsWith(p)&&h.charAt(S)==="/",v=g!=null&&(g===p||!l&&g.startsWith(p)&&g.charAt(p.length)==="/"),y={isActive:F,isPending:v,isTransitioning:P},B=F?t:void 0,z;typeof n=="function"?z=n(y):z=[n,F?"active":null,v?"pending":null,P?"transitioning":null].filter(Boolean).join(" ");let $=typeof o=="function"?o(y):o;return C.exports.createElement(Kh,{...s,"aria-current":B,className:z,ref:d,style:$,to:a,viewTransition:u},typeof i=="function"?i(y):i)});Yh.displayName="NavLink";var ov=C.exports.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:n,replace:l,state:o,method:a=ko,action:u,onSubmit:i,relative:s,preventScrollReset:d,viewTransition:m,...f},w)=>{let k=cv(),N=dv(u,{relative:s}),P=a.toLowerCase()==="get"?"get":"post",p=typeof u=="string"&&Qh.test(u),h=g=>{if(i&&i(g),g.defaultPrevented)return;g.preventDefault();let S=g.nativeEvent.submitter,F=(S==null?void 0:S.getAttribute("formmethod"))||a;k(S||g.currentTarget,{fetcherKey:t,method:F,navigate:r,replace:l,state:o,relative:s,preventScrollReset:d,viewTransition:m})};return C.exports.createElement("form",{ref:w,method:P,action:N,onSubmit:n?i:h,...f,"data-discover":!p&&e==="render"?"true":void 0})});ov.displayName="Form";function av(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Gh(e){let t=C.exports.useContext(Vr);return X(t,av(e)),t}function iv(e,{target:t,replace:r,state:n,preventScrollReset:l,relative:o,viewTransition:a}={}){let u=jh(),i=vr(),s=Il(e,{relative:o});return C.exports.useCallback(d=>{if(Mg(d,t)){d.preventDefault();let m=r!==void 0?r:pr(i)===pr(s);u(e,{replace:m,state:n,preventScrollReset:l,relative:o,viewTransition:a})}},[i,u,s,r,n,t,e,l,o,a])}var uv=0,sv=()=>`__${String(++uv)}__`;function cv(){let{router:e}=Gh("useSubmit"),{basename:t}=C.exports.useContext(Ct),r=wg();return C.exports.useCallback(async(n,l={})=>{let{action:o,method:a,encType:u,formData:i,body:s}=Og(n,t);if(l.navigate===!1){let d=l.fetcherKey||sv();await e.fetch(d,r,l.action||o,{preventScrollReset:l.preventScrollReset,formData:i,body:s,formMethod:l.method||a,formEncType:l.encType||u,flushSync:l.flushSync})}else await e.navigate(l.action||o,{preventScrollReset:l.preventScrollReset,formData:i,body:s,formMethod:l.method||a,formEncType:l.encType||u,replace:l.replace,state:l.state,fromRouteId:r,flushSync:l.flushSync,viewTransition:l.viewTransition})},[e,t,r])}function dv(e,{relative:t}={}){let{basename:r}=C.exports.useContext(Ct),n=C.exports.useContext(Nt);X(n,"useFormAction must be used inside a RouteContext");let[l]=n.matches.slice(-1),o={...Il(e||".",{relative:t})},a=vr();if(e==null){o.search=a.search;let u=new URLSearchParams(o.search),i=u.getAll("index");if(i.some(d=>d==="")){u.delete("index"),i.filter(m=>m).forEach(m=>u.append("index",m));let d=u.toString();o.search=d?`?${d}`:""}}return(!e||e===".")&&l.route.index&&(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(o.pathname=o.pathname==="/"?r:Pt([r,o.pathname])),pr(o)}function fv(e,t={}){let r=C.exports.useContext(ss);X(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:n}=Gh("useViewTransitionState"),l=Il(e,{relative:t.relative});if(!r.isTransitioning)return!1;let o=yt(r.currentLocation.pathname,n)||r.currentLocation.pathname,a=yt(r.nextLocation.pathname,n)||r.nextLocation.pathname;return Jo(l.pathname,a)!=null||Jo(l.pathname,o)!=null}[...Yg];/**
 * react-router v7.6.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function hv(e){return c(Dg,{flushSync:pu.exports.flushSync,...e})}function Jh(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var l=e.length;for(t=0;t<l;t++)e[t]&&(r=Jh(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function ye(){for(var e,t,r=0,n="",l=arguments.length;r<l;r++)(e=arguments[r])&&(t=Jh(e))&&(n&&(n+=" "),n+=t);return n}/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pv=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),mv=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,r,n)=>n?n.toUpperCase():r.toLowerCase()),ud=e=>{const t=mv(e);return t.charAt(0).toUpperCase()+t.slice(1)},Xh=(...e)=>e.filter((t,r,n)=>Boolean(t)&&t.trim()!==""&&n.indexOf(t)===r).join(" ").trim(),yv=e=>{for(const t in e)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var gv={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vv=C.exports.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:l="",children:o,iconNode:a,...u},i)=>C.exports.createElement("svg",{ref:i,...gv,width:t,height:t,stroke:e,strokeWidth:n?Number(r)*24/Number(t):r,className:Xh("lucide",l),...!o&&!yv(u)&&{"aria-hidden":"true"},...u},[...a.map(([s,d])=>C.exports.createElement(s,d)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=(e,t)=>{const r=C.exports.forwardRef(({className:n,...l},o)=>C.exports.createElement(vv,{ref:o,iconNode:t,className:Xh(`lucide-${pv(ud(e))}`,`lucide-${e}`,n),...l}));return r.displayName=ud(e),r};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xv=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],sd=q("activity",xv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wv=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],Zo=q("building",wv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kv=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],ea=q("calendar",kv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sv=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],qh=q("chevron-down",Sv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ev=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],Cv=q("chevron-left",Ev);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nv=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Dv=q("chevron-right",Nv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fv=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],cd=q("chevron-up",Fv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rv=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],Bv=q("circle-alert",Rv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Av=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],dd=q("clock",Av);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pv=[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]],bv=q("coins",Pv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _v=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],Lv=q("copy",_v);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Iv=[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]],ta=q("database",Iv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mv=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],lu=q("dollar-sign",Mv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tv=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],ou=q("download",Tv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zv=[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]],Ov=q("external-link",zv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $v=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],jv=q("file-text",$v);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uv=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],Ht=q("funnel",Uv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vv=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]],au=q("grid-3x3",Vv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hv=[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]],ra=q("hash",Hv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wv=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],Qv=q("info",Wv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kv=[["path",{d:"M9 17H7A5 5 0 0 1 7 7h2",key:"8i5ue5"}],["path",{d:"M15 7h2a5 5 0 1 1 0 10h-2",key:"1b9ql8"}],["line",{x1:"8",x2:"16",y1:"12",y2:"12",key:"1jonct"}]],ii=q("link-2",Kv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yv=[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]],Gv=q("link",Yv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jv=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Xv=q("menu",Jv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qv=[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]],Zv=q("pause",qv);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e1=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],t1=q("play",e1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r1=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],n1=q("plus",r1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l1=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],Zh=q("refresh-cw",l1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const o1=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],a1=q("rotate-ccw",o1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i1=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],na=q("search",i1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u1=[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]],s1=q("server",u1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c1=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Fl=q("settings",c1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d1=[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]],la=q("shopping-cart",d1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f1=[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]],h1=q("tag",f1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p1=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],iu=q("user",p1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m1=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],uu=q("users",m1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y1=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],g1=q("wifi-off",y1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v1=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]],x1=q("wifi",v1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w1=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],su=q("x",w1),k1=[{name:"\u7528\u6237\u670D\u52A1",href:"/user-services",icon:uu,description:"\u7528\u6237\u670D\u52A1\u8BB0\u5F55\u7BA1\u7406"},{name:"\u8BA2\u5355",href:"/orders",icon:la,description:"\u8BA2\u5355\u4FE1\u606F\u7BA1\u7406"},{name:"\u8BA2\u5355\u670D\u52A1",href:"/order-services",icon:Gv,description:"\u8BA2\u5355\u670D\u52A1\u5173\u8054"},{name:"CLI\u7248\u672C",href:"/cli-versions",icon:ou,description:"CLI\u7248\u672C\u7BA1\u7406"},{name:"\u8D27\u5E01",href:"/currencies",icon:lu,description:"\u8D27\u5E01\u4FE1\u606F\u7BA1\u7406"},{name:"\u670D\u52A1\u5206\u7C7B",href:"/service-categories",icon:au,description:"\u670D\u52A1\u5206\u7C7B\u7BA1\u7406"},{name:"\u670D\u52A1\u63D0\u4F9B\u5546",href:"/providers",icon:Zo,description:"\u670D\u52A1\u63D0\u4F9B\u5546\u7BA1\u7406"},{name:"\u670D\u52A1\u7C7B\u578B",href:"/service-types",icon:Fl,description:"\u670D\u52A1\u7C7B\u578B\u7BA1\u7406"}],S1=({isOpen:e,onToggle:t})=>E("div",{className:ye("fixed left-0 top-0 h-full bg-white shadow-lg transition-all duration-300 z-30",e?"w-64":"w-16"),children:[E("div",{className:"flex items-center justify-between p-4 border-b",children:[E("div",{className:ye("flex items-center space-x-2",!e&&"justify-center"),children:[c(ta,{className:"h-8 w-8 text-blue-600"}),e&&c("span",{className:"text-xl font-bold text-gray-900",children:"VCloud DB"})]}),c("button",{onClick:t,className:"p-1 rounded-md hover:bg-gray-100 transition-colors",children:e?c(Cv,{className:"h-5 w-5 text-gray-500"}):c(Dv,{className:"h-5 w-5 text-gray-500"})})]}),c("nav",{className:"mt-4 px-2",children:c("ul",{className:"space-y-1",children:k1.map(r=>{const n=r.icon;return c("li",{children:E(Yh,{to:r.href,className:({isActive:l})=>ye("flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors","hover:bg-gray-100 hover:text-gray-900",l?"bg-blue-100 text-blue-700":"text-gray-600",!e&&"justify-center"),title:e?void 0:r.name,children:[c(n,{className:ye("h-5 w-5",e?"mr-3":"mx-auto")}),e&&E("div",{className:"flex-1",children:[c("div",{className:"font-medium",children:r.name}),c("div",{className:"text-xs text-gray-500 mt-0.5",children:r.description})]})]})},r.href)})})})]});function E1(e){return window.go.backend.VCloudDBService.CountOrders(e)}function C1(e,t){return window.go.backend.VCloudDBService.CountRecords(e,t)}function N1(e){return window.go.backend.VCloudDBService.CountUserServices(e)}function D1(e){return window.go.backend.VCloudDBService.FindCliVersions(e)}function F1(e){return window.go.backend.VCloudDBService.FindCurrencies(e)}function R1(e){return window.go.backend.VCloudDBService.FindOrderServices(e)}function B1(e){return window.go.backend.VCloudDBService.FindOrders(e)}function A1(e,t){return window.go.backend.VCloudDBService.FindRecords(e,t)}function P1(e){return window.go.backend.VCloudDBService.FindUserServices(e)}function e0(){return window.go.backend.VCloudDBService.GetConnectionStatus()}function b1(e,t){return window.go.backend.VCloudDBService.GetRecordById(e,t)}function _1(){return window.go.backend.VCloudDBService.GetTableNames()}function L1(e){return window.go.backend.VCloudDBService.SetRPCURL(e)}const Jr={USER_SERVICE:"user_service",ORDER:"order",ORDER_SERVICE:"order_service",CLI_VERSION:"cli_version",CURRENCY:"currency",SERVICE_CATEGORY:"service_category",PROVIDER:"provider",SERVICE_TYPE:"service_type"},dn=class{constructor(){Yr(this,"cache",new Map);Yr(this,"CACHE_TTL",5*60*1e3)}static getInstance(){return dn.instance||(dn.instance=new dn),dn.instance}getCacheKey(t,r){return`${t}_${JSON.stringify(r)}`}getFromCache(t){const r=this.cache.get(t);return r&&Date.now()-r.timestamp<this.CACHE_TTL?r.data:(this.cache.delete(t),null)}setCache(t,r){this.cache.set(t,{data:r,timestamp:Date.now()})}clearCache(){this.cache.clear()}async handleApiCall(t,r,n=!1,l){try{if(n&&l){const a=this.getFromCache(l);if(a)return{data:a,success:!0}}const o=await t();return n&&l&&this.setCache(l,o),{data:o,success:!0}}catch(o){return console.error(`${r}:`,o),{data:null,success:!1,error:o instanceof Error?o.message:String(o)}}}async getConnectionStatus(){return this.handleApiCall(async()=>await e0(),"\u83B7\u53D6\u8FDE\u63A5\u72B6\u6001\u5931\u8D25")}async setRPCURL(t){return this.clearCache(),this.handleApiCall(()=>L1(t),"\u8BBE\u7F6ERPC URL\u5931\u8D25")}async findRecords(t,r={}){const n=this.getCacheKey("findRecords",{tableName:t,filter:r});return this.handleApiCall(async()=>(await A1(t,r)).map(o=>JSON.parse(o)),`\u67E5\u8BE2${t}\u8868\u8BB0\u5F55\u5931\u8D25`,!0,n)}async getRecordById(t,r){const n=this.getCacheKey("getRecordById",{tableName:t,id:r});return this.handleApiCall(async()=>{const l=await b1(t,r);return JSON.parse(l)},`\u83B7\u53D6${t}\u8868\u8BB0\u5F55\u5931\u8D25`,!0,n)}async countRecords(t,r={}){const n=this.getCacheKey("countRecords",{tableName:t,filter:r});return this.handleApiCall(()=>C1(t,r),`\u7EDF\u8BA1${t}\u8868\u8BB0\u5F55\u5931\u8D25`,!0,n)}async findUserServices(t={}){const r=this.getCacheKey("findUserServices",t);try{const n=this.getFromCache(r);if(n)return n;const[l,o]=await Promise.all([this.handleApiCall(async()=>(await P1(t)).map(i=>JSON.parse(i)),"\u67E5\u8BE2\u7528\u6237\u670D\u52A1\u5931\u8D25"),this.handleApiCall(()=>N1(t),"\u7EDF\u8BA1\u7528\u6237\u670D\u52A1\u5931\u8D25")]);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const a={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,a),a}catch(n){return console.error("\u67E5\u8BE2\u7528\u6237\u670D\u52A1\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findOrders(t={}){const r=this.getCacheKey("findOrders",t);try{const n=this.getFromCache(r);if(n)return n;const[l,o]=await Promise.all([this.handleApiCall(async()=>(await B1(t)).map(i=>JSON.parse(i)),"\u67E5\u8BE2\u8BA2\u5355\u5931\u8D25"),this.handleApiCall(()=>E1(t),"\u7EDF\u8BA1\u8BA2\u5355\u5931\u8D25")]);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const a={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,a),a}catch(n){return console.error("\u67E5\u8BE2\u8BA2\u5355\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findOrderServices(t={}){const r=this.getCacheKey("findOrderServices",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await R1(t)).map(i=>JSON.parse(i)),"\u67E5\u8BE2\u8BA2\u5355\u670D\u52A1\u5931\u8D25"),o=await this.countRecords(Jr.ORDER_SERVICE,t);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const a={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,a),a}catch(n){return console.error("\u67E5\u8BE2\u8BA2\u5355\u670D\u52A1\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findCliVersions(t={}){const r=this.getCacheKey("findCliVersions",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await D1(t)).map(i=>JSON.parse(i)),"\u67E5\u8BE2CLI\u7248\u672C\u5931\u8D25"),o=await this.countRecords(Jr.CLI_VERSION,t);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const a={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,a),a}catch(n){return console.error("\u67E5\u8BE2CLI\u7248\u672C\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findCurrencies(t={}){const r=this.getCacheKey("findCurrencies",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await F1(t)).map(i=>JSON.parse(i)),"\u67E5\u8BE2\u8D27\u5E01\u5931\u8D25"),o=await this.countRecords(Jr.CURRENCY,t);if(!l.success||!o.success)return{data:[],total:0,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!1,error:l.error||o.error};const a={data:l.data,total:o.data,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,a),a}catch(n){return console.error("\u67E5\u8BE2\u8D27\u5E01\u5931\u8D25:",n),{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findServiceCategories(t){const r=this.getCacheKey("findServiceCategories",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await this.findRecords(Jr.SERVICE_CATEGORY,t)).data,"\u67E5\u8BE2\u670D\u52A1\u5206\u7C7B\u5931\u8D25");if(!l.success)return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:l.error};const o={data:l.data,total:l.data.length,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,o),o}catch(n){return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findProviders(t){const r=this.getCacheKey("findProviders",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await this.findRecords(Jr.PROVIDER,t)).data,"\u67E5\u8BE2\u670D\u52A1\u63D0\u4F9B\u5546\u5931\u8D25");if(!l.success)return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:l.error};const o={data:l.data,total:l.data.length,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,o),o}catch(n){return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async findServiceTypes(t){const r=this.getCacheKey("findServiceTypes",t);try{const n=this.getFromCache(r);if(n)return n;const l=await this.handleApiCall(async()=>(await this.findRecords(Jr.SERVICE_TYPE,t)).data,"\u67E5\u8BE2\u670D\u52A1\u7C7B\u578B\u5931\u8D25");if(!l.success)return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:l.error};const o={data:l.data,total:l.data.length,page:Math.floor((t.offset||0)/(t.limit||20))+1,pageSize:t.limit||20,success:!0};return this.setCache(r,o),o}catch(n){return{data:[],total:0,page:1,pageSize:t.limit||20,success:!1,error:n instanceof Error?n.message:String(n)}}}async getTableNames(){return this.handleApiCall(()=>_1(),"\u83B7\u53D6\u8868\u540D\u5217\u8868\u5931\u8D25",!0,"tableNames")}};let Eo=dn;Yr(Eo,"instance");const Oe=Eo.getInstance();class I1{constructor(){Yr(this,"listeners",new Set);Yr(this,"globalAutoRefresh",{enabled:!1,interval:30})}subscribe(t){return this.listeners.add(t),()=>this.listeners.delete(t)}triggerGlobalRefresh(){this.listeners.forEach(t=>t())}setGlobalAutoRefresh(t){this.globalAutoRefresh=t}getGlobalAutoRefresh(){return this.globalAutoRefresh}}const Co=new I1;function xr(e,t,r=[],n){const[l,o]=C.exports.useState({data:[],total:0,loading:!1,error:null,page:1,pageSize:20,lastRefresh:void 0,autoRefresh:(n==null?void 0:n.enabled)||!1,refreshInterval:(n==null?void 0:n.interval)||30}),[a,u]=C.exports.useState(t),i=C.exports.useRef(),s=C.exports.useRef(),d=C.exports.useRef(null),m=C.exports.useCallback(async(P,p=!1)=>{i.current&&i.current.abort(),i.current=new AbortController,o(h=>({...h,loading:!p,error:null}));try{const h=await e(P);h.success?o(g=>({...g,data:h.data,total:h.total,page:h.page,pageSize:h.pageSize,loading:!1,error:null,lastRefresh:new Date})):o(g=>({...g,data:[],total:0,loading:!1,error:h.error||"\u67E5\u8BE2\u5931\u8D25"}))}catch(h){h instanceof Error&&h.name!=="AbortError"&&o(g=>({...g,data:[],total:0,loading:!1,error:h instanceof Error?h.message:String(h)}))}},[e]),f=C.exports.useCallback((P=!1)=>{m(a,P)},[m,a]),w=C.exports.useCallback(P=>{const p={...a,...P};u(p),m(p)},[a,m]),k=C.exports.useCallback((P,p)=>{const h=p||l.pageSize,g=(P-1)*h;w({offset:g,limit:h})},[l.pageSize,w]),N=C.exports.useCallback((P,p)=>{o(h=>({...h,autoRefresh:P,refreshInterval:p||h.refreshInterval}))},[]);return C.exports.useEffect(()=>(l.autoRefresh&&l.refreshInterval&&l.refreshInterval>0?s.current=setInterval(()=>{f(!0)},l.refreshInterval*1e3):s.current&&(clearInterval(s.current),s.current=void 0),()=>{s.current&&clearInterval(s.current)}),[l.autoRefresh,l.refreshInterval,f]),C.exports.useEffect(()=>(d.current=Co.subscribe(()=>{f()}),()=>{d.current&&d.current()}),[f]),C.exports.useEffect(()=>{m(a)},r),C.exports.useEffect(()=>()=>{i.current&&i.current.abort(),s.current&&clearInterval(s.current)},[]),{...l,params:a,refetch:f,updateParams:w,changePage:k,setAutoRefresh:N}}function M1(e={},t){return xr(Oe.findUserServices.bind(Oe),{limit:20,offset:0,...e},[],t)}function T1(e={},t){return xr(Oe.findOrders.bind(Oe),{limit:20,offset:0,...e},[],t)}function z1(e={},t){return xr(Oe.findOrderServices.bind(Oe),{limit:20,offset:0,...e},[],t)}function O1(e={},t){return xr(Oe.findCliVersions.bind(Oe),{limit:20,offset:0,...e},[],t)}function $1(e={},t){return xr(Oe.findCurrencies.bind(Oe),{limit:20,offset:0,...e},[],t)}function j1(e={},t){return xr(Oe.findServiceCategories.bind(Oe),{limit:20,offset:0,...e},[],t)}function U1(e={},t){return xr(Oe.findProviders.bind(Oe),{limit:20,offset:0,...e},[],t)}function V1(e={},t){return xr(Oe.findServiceTypes.bind(Oe),{limit:20,offset:0,...e},[],t)}function H1(){const[e,t]=C.exports.useState(Co.getGlobalAutoRefresh()),r=C.exports.useCallback(()=>{Co.triggerGlobalRefresh()},[]),n=C.exports.useCallback(l=>{t(l),Co.setGlobalAutoRefresh(l)},[]);return{globalConfig:e,triggerGlobalRefresh:r,setGlobalAutoRefresh:n}}const W1=({onMenuClick:e,sidebarOpen:t})=>{const[r,n]=C.exports.useState({connected:!1,rpcUrl:""}),[l,o]=C.exports.useState(!1),{triggerGlobalRefresh:a}=H1(),u=async()=>{o(!0);try{const i=await e0();n(i)}catch(i){n({connected:!1,rpcUrl:"",error:i instanceof Error?i.message:"\u8FDE\u63A5\u68C0\u67E5\u5931\u8D25"})}finally{o(!1)}};return C.exports.useEffect(()=>{u();const i=setInterval(u,3e4);return()=>clearInterval(i)},[]),c("header",{className:"bg-white shadow-sm border-b border-gray-200",children:E("div",{className:"flex items-center justify-between px-6 py-4",children:[E("div",{className:"flex items-center space-x-4",children:[c("button",{onClick:e,className:"p-2 rounded-md hover:bg-gray-100 transition-colors",children:c(Xv,{className:"h-5 w-5 text-gray-600"})}),E("nav",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[c("span",{children:"VCloud DB"}),c("span",{children:"/"}),c("span",{className:"text-gray-900 font-medium",children:"\u6570\u636E\u7BA1\u7406"})]})]}),E("div",{className:"flex items-center space-x-4",children:[E("button",{onClick:a,className:"flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-medium bg-blue-50 text-blue-700 hover:bg-blue-100 transition-colors",title:"\u5237\u65B0\u6240\u6709\u6570\u636E",children:[c(a1,{className:"h-4 w-4"}),c("span",{children:"\u5168\u5C40\u5237\u65B0"})]}),E("div",{className:"flex items-center space-x-2",children:[E("button",{onClick:u,disabled:l,className:ye("flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors",r.connected?"bg-green-100 text-green-700 hover:bg-green-200":"bg-red-100 text-red-700 hover:bg-red-200"),title:r.rpcUrl||"\u70B9\u51FB\u68C0\u67E5\u8FDE\u63A5\u72B6\u6001",children:[l?c(Zh,{className:"h-4 w-4 animate-spin"}):r.connected?c(x1,{className:"h-4 w-4"}):c(g1,{className:"h-4 w-4"}),c("span",{children:r.connected?"\u5DF2\u8FDE\u63A5":"\u672A\u8FDE\u63A5"})]}),r.error&&E("div",{className:"flex items-center space-x-1 text-red-600",children:[c(Bv,{className:"h-4 w-4"}),c("span",{className:"text-xs",title:r.error,children:"\u9519\u8BEF"})]})]}),c("button",{className:"p-2 rounded-md hover:bg-gray-100 transition-colors",title:"\u8BBE\u7F6E",children:c(Fl,{className:"h-5 w-5 text-gray-600"})})]})]})})},Q1=({children:e})=>{const[t,r]=C.exports.useState(!0);return E("div",{className:"min-h-screen bg-gray-50 flex",children:[c(S1,{isOpen:t,onToggle:()=>r(!t)}),E("div",{className:ye("flex-1 flex flex-col transition-all duration-300",t?"ml-64":"ml-16"),children:[c(W1,{onMenuClick:()=>r(!t),sidebarOpen:t}),c("main",{className:"flex-1 p-6 overflow-auto",children:e||c(Ag,{})})]})]})},ao=({title:e,value:t,icon:r,description:n,trend:l})=>c("div",{className:"bg-white rounded-lg shadow p-6",children:E("div",{className:"flex items-center justify-between",children:[E("div",{children:[c("p",{className:"text-sm font-medium text-gray-600",children:e}),c("p",{className:"text-3xl font-bold text-gray-900",children:t}),n&&c("p",{className:"text-sm text-gray-500 mt-1",children:n}),l&&E("div",{className:`flex items-center mt-2 text-sm ${l.isPositive?"text-green-600":"text-red-600"}`,children:[E("span",{children:[l.isPositive?"+":"",l.value,"%"]}),c("span",{className:"text-gray-500 ml-1",children:"vs \u4E0A\u6708"})]})]}),c("div",{className:"p-3 bg-blue-50 rounded-full",children:c(r,{className:"h-8 w-8 text-blue-600"})})]})}),K1=()=>E("div",{className:"space-y-6",children:[E("div",{children:[c("h1",{className:"text-2xl font-bold text-gray-900",children:"\u4EEA\u8868\u677F"}),c("p",{className:"text-gray-600 mt-1",children:"VCloud DB \u6570\u636E\u6982\u89C8"})]}),E("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[c(ao,{title:"\u7528\u6237\u670D\u52A1",value:"1,234",icon:uu,description:"\u6D3B\u8DC3\u670D\u52A1\u6570\u91CF",trend:{value:12,isPositive:!0}}),c(ao,{title:"\u8BA2\u5355\u603B\u6570",value:"5,678",icon:la,description:"\u7D2F\u8BA1\u8BA2\u5355\u6570\u91CF",trend:{value:8,isPositive:!0}}),c(ao,{title:"\u6570\u636E\u8868",value:"8",icon:ta,description:"\u53EF\u7528\u6570\u636E\u8868"}),c(ao,{title:"\u7CFB\u7EDF\u72B6\u6001",value:"\u6B63\u5E38",icon:sd,description:"\u670D\u52A1\u8FD0\u884C\u72B6\u6001"})]}),E("div",{className:"bg-white rounded-lg shadow p-6",children:[c("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"\u5FEB\u901F\u64CD\u4F5C"}),E("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[E("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[c(uu,{className:"h-6 w-6 text-blue-600 mb-2"}),c("div",{className:"font-medium text-gray-900",children:"\u67E5\u770B\u7528\u6237\u670D\u52A1"}),c("div",{className:"text-sm text-gray-500",children:"\u7BA1\u7406\u7528\u6237\u670D\u52A1\u8BB0\u5F55"})]}),E("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[c(la,{className:"h-6 w-6 text-green-600 mb-2"}),c("div",{className:"font-medium text-gray-900",children:"\u67E5\u770B\u8BA2\u5355"}),c("div",{className:"text-sm text-gray-500",children:"\u7BA1\u7406\u8BA2\u5355\u4FE1\u606F"})]}),E("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[c(ta,{className:"h-6 w-6 text-purple-600 mb-2"}),c("div",{className:"font-medium text-gray-900",children:"\u6570\u636E\u67E5\u8BE2"}),c("div",{className:"text-sm text-gray-500",children:"\u6267\u884C\u81EA\u5B9A\u4E49\u67E5\u8BE2"})]}),E("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[c(sd,{className:"h-6 w-6 text-orange-600 mb-2"}),c("div",{className:"font-medium text-gray-900",children:"\u7CFB\u7EDF\u76D1\u63A7"}),c("div",{className:"text-sm text-gray-500",children:"\u67E5\u770B\u7CFB\u7EDF\u72B6\u6001"})]})]})]}),E("div",{className:"bg-white rounded-lg shadow p-6",children:[c("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"\u6700\u8FD1\u6D3B\u52A8"}),E("div",{className:"space-y-3",children:[E("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[c("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),E("div",{className:"flex-1",children:[c("p",{className:"text-sm font-medium text-gray-900",children:"\u65B0\u7528\u6237\u670D\u52A1\u521B\u5EFA"}),c("p",{className:"text-xs text-gray-500",children:"2\u5206\u949F\u524D"})]})]}),E("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[c("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),E("div",{className:"flex-1",children:[c("p",{className:"text-sm font-medium text-gray-900",children:"\u8BA2\u5355\u72B6\u6001\u66F4\u65B0"}),c("p",{className:"text-xs text-gray-500",children:"5\u5206\u949F\u524D"})]})]}),E("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",children:[c("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),E("div",{className:"flex-1",children:[c("p",{className:"text-sm font-medium text-gray-900",children:"\u7CFB\u7EDF\u914D\u7F6E\u66F4\u65B0"}),c("p",{className:"text-xs text-gray-500",children:"10\u5206\u949F\u524D"})]})]})]})]})]});function wr({columns:e,data:t,loading:r=!1,pagination:n,sortConfig:l,onSort:o,rowKey:a="_id",onRowClick:u,emptyText:i="\u6682\u65E0\u6570\u636E",className:s}){const d=(k,N)=>typeof a=="function"?a(k):k[a]||N.toString(),m=k=>{if(!o)return;let N="asc";(l==null?void 0:l.key)===k&&l.direction==="asc"&&(N="desc"),o(k,N)},f=k=>(l==null?void 0:l.key)!==k?c(cd,{className:"h-4 w-4 text-gray-300"}):l.direction==="asc"?c(cd,{className:"h-4 w-4 text-gray-600"}):c(qh,{className:"h-4 w-4 text-gray-600"}),w=(k,N)=>{if(N.render){const P=t.indexOf(k),p=N.dataIndex?k[N.dataIndex]:k;return N.render(p,k,P)}return N.dataIndex?k[N.dataIndex]:""};return r?c("div",{className:ye("bg-white rounded-lg shadow",s),children:E("div",{className:"p-8 text-center",children:[c("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),c("p",{className:"text-gray-500 mt-2",children:"\u52A0\u8F7D\u4E2D..."})]})}):E("div",{className:ye("bg-white rounded-lg shadow overflow-hidden",s),children:[c("div",{className:"overflow-x-auto",children:E("table",{className:"min-w-full divide-y divide-gray-200",children:[c("thead",{className:"bg-gray-50",children:c("tr",{children:e.map(k=>c("th",{className:ye("px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",k.align==="center"&&"text-center",k.align==="right"&&"text-right",k.sortable&&"cursor-pointer hover:bg-gray-100"),style:{width:k.width},onClick:()=>k.sortable&&m(k.key),children:E("div",{className:"flex items-center space-x-1",children:[c("span",{children:k.title}),k.sortable&&f(k.key)]})},k.key))})}),c("tbody",{className:"bg-white divide-y divide-gray-200",children:t.length===0?c("tr",{children:c("td",{colSpan:e.length,className:"px-6 py-8 text-center text-gray-500",children:i})}):t.map((k,N)=>c("tr",{className:ye("hover:bg-gray-50 transition-colors",u&&"cursor-pointer"),onClick:()=>u==null?void 0:u(k,N),children:e.map(P=>c("td",{className:ye("px-6 py-4 whitespace-nowrap text-sm text-gray-900",P.align==="center"&&"text-center",P.align==="right"&&"text-right"),children:w(k,P)},P.key))},d(k,N)))})]})}),n&&c("div",{className:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6",children:E("div",{className:"flex items-center justify-between",children:[E("div",{className:"flex-1 flex justify-between sm:hidden",children:[c("button",{disabled:n.current<=1,onClick:()=>n.onChange(n.current-1,n.pageSize),className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"\u4E0A\u4E00\u9875"}),c("button",{disabled:n.current*n.pageSize>=n.total,onClick:()=>n.onChange(n.current+1,n.pageSize),className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"\u4E0B\u4E00\u9875"})]}),E("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[c("div",{children:E("p",{className:"text-sm text-gray-700",children:["\u663E\u793A\u7B2C"," ",c("span",{className:"font-medium",children:(n.current-1)*n.pageSize+1})," ","\u5230"," ",c("span",{className:"font-medium",children:Math.min(n.current*n.pageSize,n.total)})," ","\u6761\uFF0C\u5171"," ",c("span",{className:"font-medium",children:n.total})," \u6761\u8BB0\u5F55"]})}),c("div",{children:E("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px",children:[c("button",{disabled:n.current<=1,onClick:()=>n.onChange(n.current-1,n.pageSize),className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"\u4E0A\u4E00\u9875"}),c("button",{disabled:n.current*n.pageSize>=n.total,onClick:()=>n.onChange(n.current+1,n.pageSize),className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"\u4E0B\u4E00\u9875"})]})})]})]})})]})}function Y1(e){const{filename:t="export",columns:r,data:n}=e;if(!n||n.length===0){alert("\u6CA1\u6709\u6570\u636E\u53EF\u5BFC\u51FA");return}const l=r||Object.keys(n[0]).map(d=>({key:d,title:d})),o=l.map(d=>`"${d.title}"`).join(","),a=n.map(d=>l.map(m=>{let f=d[m.key];return m.render?f=m.render(f,d):f==null?f="":typeof f=="object"?f=JSON.stringify(f):typeof f=="boolean"?f=f?"\u662F":"\u5426":typeof f=="number"&&f>1e9&&(f=new Date(f*1e3).toLocaleString()),`"${String(f).replace(/"/g,'""')}"`}).join(",")),u=[o,...a].join(`
`),i="\uFEFF",s=new Blob([i+u],{type:"text/csv;charset=utf-8;"});t0(s,`${t}.csv`)}function G1(e){const{filename:t="export",columns:r,data:n}=e;if(!n||n.length===0){alert("\u6CA1\u6709\u6570\u636E\u53EF\u5BFC\u51FA");return}let l=n;r&&(l=n.map(u=>{const i={};return r.forEach(s=>{let d=u[s.key];s.render&&(d=s.render(d,u)),i[s.title]=d}),i}));const o=JSON.stringify(l,null,2),a=new Blob([o],{type:"application/json;charset=utf-8;"});t0(a,`${t}.json`)}function t0(e,t){const r=window.URL.createObjectURL(e),n=document.createElement("a");n.href=r,n.download=t,n.style.display="none",document.body.appendChild(n),n.click(),document.body.removeChild(n),window.URL.revokeObjectURL(r)}function Re(e){return!e||e===0?"-":new Date(e*1e3).toLocaleString()}function r0(e){return e?"\u662F":"\u5426"}function xn(e){return e?typeof e=="object"?JSON.stringify(e):String(e):"-"}const kr=({data:e,columns:t,filename:r="export",disabled:n=!1,className:l=""})=>{const[o,a]=C.exports.useState(!1),u=i=>{const s={data:e,columns:t,filename:`${r}_${new Date().toISOString().split("T")[0]}`};i==="csv"?Y1(s):G1(s),a(!1)};return n||!e||e.length===0?E("button",{disabled:!0,className:`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-400 bg-gray-100 cursor-not-allowed ${l}`,children:[c(ou,{className:"h-4 w-4 mr-2"}),"\u5BFC\u51FA\u6570\u636E"]}):E("div",{className:"relative",children:[E("button",{onClick:()=>a(!o),className:`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${l}`,children:[c(ou,{className:"h-4 w-4 mr-2"}),"\u5BFC\u51FA\u6570\u636E",c("svg",{className:"ml-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:c("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),o&&E(as,{children:[c("div",{className:"fixed inset-0 z-10",onClick:()=>a(!1)}),E("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-20",children:[E("div",{className:"py-1",children:[E("button",{onClick:()=>u("csv"),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900",children:[c(jv,{className:"h-4 w-4 mr-3 text-green-500"}),"\u5BFC\u51FA\u4E3A CSV",c("span",{className:"ml-auto text-xs text-gray-500",children:"Excel\u517C\u5BB9"})]}),E("button",{onClick:()=>u("json"),className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900",children:[c(ta,{className:"h-4 w-4 mr-3 text-blue-500"}),"\u5BFC\u51FA\u4E3A JSON",c("span",{className:"ml-auto text-xs text-gray-500",children:"\u7ED3\u6784\u5316\u6570\u636E"})]})]}),c("div",{className:"border-t border-gray-100 px-4 py-2",children:E("div",{className:"text-xs text-gray-500",children:["\u5171 ",e.length," \u6761\u8BB0\u5F55"]})})]})]})]})},ms=({isOpen:e,onClose:t,title:r,data:n,fields:l,icon:o})=>{if(!e||!n)return null;const a=s=>{navigator.clipboard.writeText(s).then(()=>{console.log("\u5DF2\u590D\u5236\u5230\u526A\u8D34\u677F")})},u=(s,d)=>{if(d==null)return c("span",{className:"text-gray-400",children:"-"});if(s.render)return s.render(d,n);switch(s.type){case"boolean":return c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${d?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:d?"\u662F":"\u5426"});case"timestamp":return E("div",{className:"flex items-center",children:[c(ea,{className:"h-4 w-4 text-gray-400 mr-2"}),c("span",{children:d?new Date(d*1e3).toLocaleString():"-"})]});case"json":return c("pre",{className:"bg-gray-50 p-2 rounded text-xs overflow-x-auto max-w-md",children:JSON.stringify(d,null,2)});case"address":return E("div",{className:"flex items-center",children:[c(iu,{className:"h-4 w-4 text-blue-500 mr-2"}),c("span",{className:"font-mono text-sm",children:d})]});case"id":return E("div",{className:"flex items-center",children:[c(ra,{className:"h-4 w-4 text-gray-500 mr-2"}),c("span",{className:"font-mono text-sm",children:d})]});case"amount":return E("div",{className:"flex items-center",children:[c(lu,{className:"h-4 w-4 text-green-500 mr-2"}),c("span",{className:"font-medium",children:Number(d).toLocaleString()})]});case"number":return c("span",{className:"font-medium",children:Number(d).toLocaleString()});default:return c("span",{children:String(d)})}},i=s=>{switch(s.type){case"timestamp":return c(ea,{className:"h-4 w-4 text-gray-400"});case"address":return c(iu,{className:"h-4 w-4 text-blue-500"});case"id":return c(ra,{className:"h-4 w-4 text-gray-500"});case"amount":return c(lu,{className:"h-4 w-4 text-green-500"});case"json":return c(Fl,{className:"h-4 w-4 text-purple-500"});default:return c(Qv,{className:"h-4 w-4 text-gray-400"})}};return E("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[c("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:t}),c("div",{className:"flex min-h-full items-center justify-center p-4",children:E("div",{className:"relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[E("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[E("div",{className:"flex items-center",children:[o&&c("div",{className:"mr-3",children:o}),c("h3",{className:"text-lg font-medium text-gray-900",children:r})]}),c("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 transition-colors",children:c(su,{className:"h-6 w-6"})})]}),c("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:c("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:l.map(s=>{const d=n[s.key],m=u(s,d);return E("div",{className:"space-y-2",children:[E("div",{className:"flex items-center justify-between",children:[E("label",{className:"flex items-center text-sm font-medium text-gray-700",children:[i(s),c("span",{className:"ml-2",children:s.label})]}),E("div",{className:"flex space-x-1",children:[s.copyable&&d&&c("button",{onClick:()=>a(String(d)),className:"p-1 text-gray-400 hover:text-gray-600 transition-colors",title:"\u590D\u5236",children:c(Lv,{className:"h-4 w-4"})}),s.linkable&&d&&c("button",{onClick:()=>window.open(String(d),"_blank"),className:"p-1 text-gray-400 hover:text-gray-600 transition-colors",title:"\u6253\u5F00\u94FE\u63A5",children:c(Ov,{className:"h-4 w-4"})})]})]}),c("div",{className:"bg-gray-50 rounded-md p-3 min-h-[2.5rem] flex items-center",children:m})]},s.key)})})}),c("div",{className:"flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50",children:c("button",{onClick:t,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"\u5173\u95ED"})})]})})]})},fd=[{label:"10\u79D2",value:10},{label:"30\u79D2",value:30},{label:"1\u5206\u949F",value:60},{label:"5\u5206\u949F",value:300},{label:"10\u5206\u949F",value:600}],Sr=({onRefresh:e,loading:t=!1,lastRefresh:r,autoRefresh:n=!1,refreshInterval:l=30,onAutoRefreshChange:o,className:a})=>{var f;const[u,i]=C.exports.useState(!1),s=w=>{const k=new Date,N=Math.floor((k.getTime()-w.getTime())/1e3);return N<60?`${N}\u79D2\u524D`:N<3600?`${Math.floor(N/60)}\u5206\u949F\u524D`:`${Math.floor(N/3600)}\u5C0F\u65F6\u524D`},d=()=>{o&&o(!n,l)},m=w=>{o&&o(n,w),i(!1)};return E("div",{className:ye("flex items-center space-x-2",a),children:[E("button",{onClick:e,disabled:t,className:ye("flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors","bg-blue-50 text-blue-700 hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed"),title:"\u624B\u52A8\u5237\u65B0\u6570\u636E",children:[c(Zh,{className:ye("h-4 w-4",t&&"animate-spin")}),c("span",{children:"\u5237\u65B0"})]}),o&&E("button",{onClick:d,className:ye("flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors",n?"bg-green-50 text-green-700 hover:bg-green-100":"bg-gray-50 text-gray-700 hover:bg-gray-100"),title:n?"\u505C\u6B62\u81EA\u52A8\u5237\u65B0":"\u5F00\u542F\u81EA\u52A8\u5237\u65B0",children:[n?c(Zv,{className:"h-4 w-4"}):c(t1,{className:"h-4 w-4"}),c("span",{children:n?"\u81EA\u52A8":"\u624B\u52A8"})]}),o&&E("div",{className:"relative",children:[E("button",{onClick:()=>i(!u),className:ye("flex items-center space-x-1 px-2 py-1.5 rounded-md text-sm text-gray-600 hover:bg-gray-100 transition-colors"),title:"\u8BBE\u7F6E\u5237\u65B0\u95F4\u9694",children:[c(dd,{className:"h-4 w-4"}),c("span",{children:(f=fd.find(w=>w.value===l))==null?void 0:f.label}),c(qh,{className:"h-3 w-3"})]}),u&&c("div",{className:"absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]",children:c("div",{className:"py-1",children:fd.map(w=>c("button",{onClick:()=>m(w.value),className:ye("w-full text-left px-3 py-2 text-sm hover:bg-gray-100 transition-colors",l===w.value&&"bg-blue-50 text-blue-700"),children:w.label},w.value))})})]}),r&&E("div",{className:"flex items-center space-x-1 text-xs text-gray-500",children:[c(dd,{className:"h-3 w-3"}),E("span",{children:["\u66F4\u65B0\u4E8E ",s(r)]})]}),u&&c("div",{className:"fixed inset-0 z-0",onClick:()=>i(!1)})]})},J1=()=>{const[e,t]=C.exports.useState(""),[r,n]=C.exports.useState({}),[l,o]=C.exports.useState(null),[a,u]=C.exports.useState(!1),{data:i,total:s,loading:d,error:m,page:f,pageSize:w,params:k,refetch:N,updateParams:P,changePage:p,lastRefresh:h,autoRefresh:g,refreshInterval:S,setAutoRefresh:F}=M1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),v=[{key:"serviceID",title:"\u670D\u52A1ID",dataIndex:"serviceID",width:200,sortable:!0,render:L=>c("span",{className:"font-mono text-sm text-blue-600",children:L})},{key:"address",title:"\u7528\u6237\u5730\u5740",dataIndex:"address",width:180,render:L=>c("span",{className:"font-mono text-xs text-gray-600",children:L?`${L.slice(0,8)}...${L.slice(-6)}`:"-"})},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546",dataIndex:"provider",width:120,sortable:!0},{key:"status",title:"\u72B6\u6001",dataIndex:"status",width:100,render:L=>c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${{active:"bg-green-100 text-green-800",inactive:"bg-gray-100 text-gray-800",pending:"bg-yellow-100 text-yellow-800",error:"bg-red-100 text-red-800"}[L]||"bg-gray-100 text-gray-800"}`,children:L})},{key:"serviceActivated",title:"\u670D\u52A1\u6FC0\u6D3B",dataIndex:"serviceActivated",width:100,align:"center",render:L=>c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${L?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:L?"\u5DF2\u6FC0\u6D3B":"\u672A\u6FC0\u6D3B"})},{key:"amount",title:"\u91D1\u989D",dataIndex:"amount",width:120,align:"right",sortable:!0,render:L=>c("span",{className:"font-medium",children:(L==null?void 0:L.toLocaleString())||0})},{key:"duration",title:"\u6301\u7EED\u65F6\u95F4",dataIndex:"duration",width:100,align:"right",render:L=>c("span",{children:L?`${L}\u5929`:"-"})},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:L=>c("span",{className:"text-sm text-gray-600",children:L?new Date(L*1e3).toLocaleString():"-"})}],y=(L,Z)=>{P({sortBy:L,sortDesc:Z==="desc",offset:0})},B=(L,Z)=>{p(L,Z)},z=L=>{o(L),u(!0)},$=()=>{u(!1),o(null)},G=[{key:"_id",label:"ID",type:"id",copyable:!0},{key:"serviceID",label:"\u670D\u52A1ID",type:"id",copyable:!0},{key:"address",label:"\u7528\u6237\u5730\u5740",type:"address",copyable:!0},{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text"},{key:"providerAddress",label:"\u63D0\u4F9B\u5546\u5730\u5740",type:"address",copyable:!0},{key:"status",label:"\u72B6\u6001",type:"text"},{key:"serviceActivated",label:"\u670D\u52A1\u5DF2\u6FC0\u6D3B",type:"boolean"},{key:"duration",label:"\u6301\u7EED\u65F6\u95F4\uFF08\u79D2\uFF09",type:"number"},{key:"amount",label:"\u91D1\u989D",type:"amount"},{key:"service",label:"\u670D\u52A1\u540D\u79F0",type:"text"},{key:"createdAt",label:"\u521B\u5EFA\u65F6\u95F4",type:"timestamp"},{key:"updatedAt",label:"\u66F4\u65B0\u65F6\u95F4",type:"timestamp"},{key:"endAt",label:"\u7ED3\u675F\u65F6\u95F4",type:"timestamp"},{key:"deletedAt",label:"\u5220\u9664\u65F6\u95F4",type:"timestamp"}],ge=()=>{const L={...r,offset:0};e.trim()&&(e.startsWith("0x")||e.length===42?L.address=e:L.serviceID=e),P(L)},we=()=>{N()},A=()=>{t(""),n({}),P({offset:0,limit:20,sortBy:"createdAt",sortDesc:!0})};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[c("h1",{className:"text-2xl font-bold text-gray-900",children:"\u7528\u6237\u670D\u52A1"}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u548C\u67E5\u770B\u7528\u6237\u670D\u52A1\u8BB0\u5F55"})]}),E("div",{className:"flex items-center space-x-3",children:[c(Sr,{onRefresh:we,loading:d,lastRefresh:h,autoRefresh:g,refreshInterval:S,onAutoRefreshChange:F}),c(kr,{data:i,columns:[{key:"_id",title:"ID"},{key:"serviceID",title:"\u670D\u52A1ID"},{key:"address",title:"\u7528\u6237\u5730\u5740"},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546"},{key:"providerAddress",title:"\u63D0\u4F9B\u5546\u5730\u5740"},{key:"status",title:"\u72B6\u6001"},{key:"serviceActivated",title:"\u670D\u52A1\u5DF2\u6FC0\u6D3B",render:L=>r0(L)},{key:"duration",title:"\u6301\u7EED\u65F6\u95F4\uFF08\u79D2\uFF09"},{key:"amount",title:"\u91D1\u989D"},{key:"service",title:"\u670D\u52A1\u540D\u79F0"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:L=>Re(L)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:L=>Re(L)},{key:"endAt",title:"\u7ED3\u675F\u65F6\u95F4",render:L=>Re(L)}],filename:"user_services",disabled:d||i.length===0}),E("button",{className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:[c(n1,{className:"h-4 w-4 mr-2"}),"\u65B0\u5EFA"]})]})]}),c("div",{className:"bg-white rounded-lg shadow p-6",children:E("div",{className:"flex items-center space-x-4",children:[c("div",{className:"flex-1",children:E("div",{className:"relative",children:[c(na,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),c("input",{type:"text",placeholder:"\u641C\u7D22\u670D\u52A1ID\u3001\u7528\u6237\u5730\u5740...",value:e,onChange:L=>t(L.target.value),onKeyPress:L=>L.key==="Enter"&&ge(),className:"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]})}),E("button",{onClick:ge,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:[c(na,{className:"h-4 w-4 mr-2"}),"\u641C\u7D22"]}),E("button",{onClick:A,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[c(Ht,{className:"h-4 w-4 mr-2"}),"\u6E05\u9664\u7B5B\u9009"]})]})}),m&&c("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:c("div",{className:"flex",children:E("div",{className:"ml-3",children:[c("h3",{className:"text-sm font-medium text-red-800",children:"\u52A0\u8F7D\u6570\u636E\u65F6\u51FA\u9519"}),c("div",{className:"mt-2 text-sm text-red-700",children:c("p",{children:m})}),c("div",{className:"mt-4",children:c("button",{onClick:we,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"\u91CD\u8BD5"})})]})})}),c(wr,{columns:v,data:i,loading:d,pagination:{current:f,pageSize:w,total:s,onChange:B},sortConfig:{key:k.sortBy||"createdAt",direction:k.sortDesc?"desc":"asc"},onSort:y,rowKey:"_id",onRowClick:z,emptyText:m?"\u52A0\u8F7D\u5931\u8D25":"\u6682\u65E0\u6570\u636E"}),c(ms,{isOpen:a,onClose:$,title:"\u7528\u6237\u670D\u52A1\u8BE6\u60C5",data:l,fields:G,icon:c(s1,{className:"h-6 w-6 text-blue-600"})})]})},Hr=({fields:e,onSubmit:t,onReset:r,loading:n=!1,className:l,showAdvanced:o=!1,initialValues:a={}})=>{const[u,i]=C.exports.useState(()=>{const S={};return e.forEach(F=>{F.defaultValue!==void 0&&(S[F.key]=F.defaultValue)}),{...S,...a}}),[s,d]=C.exports.useState({}),[m,f]=C.exports.useState(o),w=(S,F)=>{i(v=>({...v,[S]:F})),s[S]&&d(v=>{const y={...v};return delete y[S],y})},k=()=>{const S={};return e.forEach(F=>{if(F.validation){const v=F.validation(u[F.key]);v&&(S[F.key]=v)}}),d(S),Object.keys(S).length===0},N=S=>{if(S.preventDefault(),k()){const F=Object.entries(u).reduce((v,[y,B])=>(B!=null&&B!==""&&(v[y]=B),v),{});t(F)}},P=()=>{const S={};e.forEach(F=>{F.defaultValue!==void 0&&(S[F.key]=F.defaultValue)}),i(S),d({}),r==null||r()},p=S=>{var $;const F=u[S.key]||"",v=s[S.key],y=!!v,B=ye("block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 sm:text-sm",y?"border-red-300 focus:border-red-500 focus:ring-red-500":"border-gray-300 focus:border-blue-500 focus:ring-blue-500"),z=()=>{switch(S.type){case"text":return S.key.includes("address")?c(iu,{className:"h-4 w-4"}):S.key.includes("provider")?c(Zo,{className:"h-4 w-4"}):c(ra,{className:"h-4 w-4"});case"date":case"dateRange":return c(ea,{className:"h-4 w-4"});case"number":return c(ra,{className:"h-4 w-4"});default:return c(na,{className:"h-4 w-4"})}};switch(S.type){case"text":case"number":return E("div",{className:"relative",children:[c("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:S.label}),E("div",{className:"relative",children:[c("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400",children:z()}),c("input",{type:S.type,value:F,onChange:G=>w(S.key,G.target.value),placeholder:S.placeholder,className:ye(B,"pl-10")})]}),y&&c("p",{className:"mt-1 text-sm text-red-600",children:v})]},S.key);case"select":return E("div",{children:[c("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:S.label}),E("select",{value:F,onChange:G=>w(S.key,G.target.value),className:B,children:[c("option",{value:"",children:"\u8BF7\u9009\u62E9..."}),($=S.options)==null?void 0:$.map(G=>c("option",{value:String(G.value),children:G.label},String(G.value)))]}),y&&c("p",{className:"mt-1 text-sm text-red-600",children:v})]},S.key);case"boolean":return E("div",{children:[E("label",{className:"flex items-center",children:[c("input",{type:"checkbox",checked:F||!1,onChange:G=>w(S.key,G.target.checked),className:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"}),c("span",{className:"ml-2 text-sm font-medium text-gray-700",children:S.label})]}),y&&c("p",{className:"mt-1 text-sm text-red-600",children:v})]},S.key);case"date":return E("div",{children:[c("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:S.label}),E("div",{className:"relative",children:[c("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400",children:c(ea,{className:"h-4 w-4"})}),c("input",{type:"date",value:F,onChange:G=>w(S.key,G.target.value),className:ye(B,"pl-10")})]}),y&&c("p",{className:"mt-1 text-sm text-red-600",children:v})]},S.key);default:return null}},h=e.slice(0,3),g=e.slice(3);return c("div",{className:ye("bg-white rounded-lg shadow p-6",l),children:E("form",{onSubmit:N,className:"space-y-4",children:[c("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:h.map(p)}),g.length>0&&E(as,{children:[c("div",{className:"flex items-center justify-between pt-2",children:E("button",{type:"button",onClick:()=>f(!m),className:"flex items-center text-sm text-blue-600 hover:text-blue-800",children:[c(Ht,{className:"h-4 w-4 mr-1"}),"\u9AD8\u7EA7\u7B5B\u9009",m?c(su,{className:"h-4 w-4 ml-1"}):E("span",{className:"ml-1",children:["(",g.length,")"]})]})}),m&&c("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pt-4 border-t border-gray-200",children:g.map(p)})]}),E("div",{className:"flex items-center justify-between pt-4 border-t border-gray-200",children:[E("button",{type:"button",onClick:P,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[c(su,{className:"h-4 w-4 mr-2"}),"\u91CD\u7F6E"]}),E("button",{type:"submit",disabled:n,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50",children:[c(na,{className:ye("h-4 w-4 mr-2",n&&"animate-spin")}),n?"\u67E5\u8BE2\u4E2D...":"\u67E5\u8BE2"]})]})]})})},X1=()=>{const[e,t]=C.exports.useState(!1),[r,n]=C.exports.useState(null),[l,o]=C.exports.useState(!1),{data:a,total:u,loading:i,error:s,page:d,pageSize:m,params:f,refetch:w,updateParams:k,changePage:N,lastRefresh:P,autoRefresh:p,refreshInterval:h,setAutoRefresh:g}=T1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),S=[{key:"type",title:"\u8BA2\u5355\u7C7B\u578B",dataIndex:"type",width:120,sortable:!0,render:A=>c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${A==="purchase"?"bg-green-100 text-green-800":A==="refund"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:A==="purchase"?"\u8D2D\u4E70":A==="refund"?"\u9000\u6B3E":A})},{key:"address",title:"\u7528\u6237\u5730\u5740",dataIndex:"address",width:180,render:A=>c("span",{className:"font-mono text-xs text-gray-600",children:A?`${A.slice(0,8)}...${A.slice(-6)}`:"-"})},{key:"recipient",title:"\u63A5\u6536\u5730\u5740",dataIndex:"recipient",width:180,render:A=>c("span",{className:"font-mono text-xs text-gray-600",children:A?`${A.slice(0,8)}...${A.slice(-6)}`:"-"})},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546",dataIndex:"provider",width:120,sortable:!0},{key:"status",title:"\u8BA2\u5355\u72B6\u6001",dataIndex:"status",width:100,render:A=>{const L={pending:"bg-yellow-100 text-yellow-800",paid:"bg-green-100 text-green-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800",refunded:"bg-purple-100 text-purple-800"},Z={pending:"\u5F85\u652F\u4ED8",paid:"\u5DF2\u652F\u4ED8",completed:"\u5DF2\u5B8C\u6210",cancelled:"\u5DF2\u53D6\u6D88",refunded:"\u5DF2\u9000\u6B3E"};return c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${L[A]||"bg-gray-100 text-gray-800"}`,children:Z[A]||A})}},{key:"amount",title:"\u8BA2\u5355\u91D1\u989D",dataIndex:"amount",width:120,align:"right",sortable:!0,render:A=>c("span",{className:"font-medium",children:(A==null?void 0:A.toLocaleString())||0})},{key:"amountPaid",title:"\u5DF2\u652F\u4ED8\u91D1\u989D",dataIndex:"amountPaid",width:120,align:"right",render:A=>c("span",{className:"font-medium text-green-600",children:(A==null?void 0:A.toLocaleString())||0})},{key:"userServiceIDs",title:"\u5173\u8054\u670D\u52A1",dataIndex:"userServiceIDs",width:100,align:"center",render:A=>E("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:[(A==null?void 0:A.length)||0," \u4E2A"]})},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:A=>c("span",{className:"text-sm text-gray-600",children:A?new Date(A*1e3).toLocaleString():"-"})},{key:"paidTS",title:"\u652F\u4ED8\u65F6\u95F4",dataIndex:"paidTS",width:160,render:A=>c("span",{className:"text-sm text-gray-600",children:A?new Date(A*1e3).toLocaleString():"-"})}],F=[{key:"address",label:"\u7528\u6237\u5730\u5740",type:"text",placeholder:"\u8F93\u5165\u7528\u6237\u5730\u5740..."},{key:"recipient",label:"\u63A5\u6536\u5730\u5740",type:"text",placeholder:"\u8F93\u5165\u63A5\u6536\u5730\u5740..."},{key:"type",label:"\u8BA2\u5355\u7C7B\u578B",type:"select",options:[{label:"\u8D2D\u4E70",value:"purchase"},{label:"\u9000\u6B3E",value:"refund"}]},{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u63D0\u4F9B\u5546..."},{key:"status",label:"\u8BA2\u5355\u72B6\u6001",type:"select",options:[{label:"\u5F85\u652F\u4ED8",value:"pending"},{label:"\u5DF2\u652F\u4ED8",value:"paid"},{label:"\u5DF2\u5B8C\u6210",value:"completed"},{label:"\u5DF2\u53D6\u6D88",value:"cancelled"},{label:"\u5DF2\u9000\u6B3E",value:"refunded"}]},{key:"minAmount",label:"\u6700\u5C0F\u91D1\u989D",type:"number",placeholder:"\u8F93\u5165\u6700\u5C0F\u91D1\u989D..."},{key:"maxAmount",label:"\u6700\u5927\u91D1\u989D",type:"number",placeholder:"\u8F93\u5165\u6700\u5927\u91D1\u989D..."}],v=(A,L)=>{k({sortBy:A,sortDesc:L==="desc",offset:0})},y=(A,L)=>{N(A,L)},B=A=>{n(A),o(!0)},z=()=>{o(!1),n(null)},$=[{key:"_id",label:"ID",type:"id",copyable:!0},{key:"type",label:"\u8BA2\u5355\u7C7B\u578B",type:"text"},{key:"address",label:"\u7528\u6237\u5730\u5740",type:"address",copyable:!0},{key:"recipient",label:"\u63A5\u6536\u5730\u5740",type:"address",copyable:!0},{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text"},{key:"status",label:"\u8BA2\u5355\u72B6\u6001",type:"text"},{key:"amount",label:"\u8BA2\u5355\u91D1\u989D",type:"amount"},{key:"amountPaid",label:"\u5DF2\u652F\u4ED8\u91D1\u989D",type:"amount"},{key:"createdAt",label:"\u521B\u5EFA\u65F6\u95F4",type:"timestamp"},{key:"updatedAt",label:"\u66F4\u65B0\u65F6\u95F4",type:"timestamp"},{key:"paidTS",label:"\u652F\u4ED8\u65F6\u95F4",type:"timestamp"},{key:"filedTS",label:"\u63D0\u4EA4\u65F6\u95F4",type:"timestamp"},{key:"deletedAt",label:"\u5220\u9664\u65F6\u95F4",type:"timestamp"}],G=A=>{const L={offset:0,...A};A.minAmount&&(L.tsStart=A.minAmount),A.maxAmount&&(L.tsEnd=A.maxAmount),k(L),t(!1)},ge=()=>{w()},we=()=>{k({offset:0,limit:20,sortBy:"createdAt",sortDesc:!0}),t(!1)};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[c("h1",{className:"text-2xl font-bold text-gray-900",children:"\u8BA2\u5355\u7BA1\u7406"}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u548C\u67E5\u770B\u8BA2\u5355\u4FE1\u606F"})]}),E("div",{className:"flex items-center space-x-3",children:[c(Sr,{onRefresh:ge,loading:i,lastRefresh:P,autoRefresh:p,refreshInterval:h,onAutoRefreshChange:g}),E("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[c(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u9AD8\u7EA7\u7B5B\u9009"]}),c(kr,{data:a,columns:[{key:"_id",title:"ID"},{key:"type",title:"\u8BA2\u5355\u7C7B\u578B"},{key:"address",title:"\u7528\u6237\u5730\u5740"},{key:"recipient",title:"\u63A5\u6536\u5730\u5740"},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546"},{key:"status",title:"\u8BA2\u5355\u72B6\u6001"},{key:"amount",title:"\u8BA2\u5355\u91D1\u989D"},{key:"amountPaid",title:"\u5DF2\u652F\u4ED8\u91D1\u989D"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:A=>Re(A)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:A=>Re(A)},{key:"paidTS",title:"\u652F\u4ED8\u65F6\u95F4",render:A=>Re(A)},{key:"filedTS",title:"\u63D0\u4EA4\u65F6\u95F4",render:A=>Re(A)}],filename:"orders",disabled:i||a.length===0})]})]}),e&&c(Hr,{fields:F,onSubmit:G,onReset:we,loading:i,showAdvanced:!0}),s&&c("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:c("div",{className:"flex",children:E("div",{className:"ml-3",children:[c("h3",{className:"text-sm font-medium text-red-800",children:"\u52A0\u8F7D\u6570\u636E\u65F6\u51FA\u9519"}),c("div",{className:"mt-2 text-sm text-red-700",children:c("p",{children:s})}),c("div",{className:"mt-4",children:c("button",{onClick:ge,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"\u91CD\u8BD5"})})]})})}),c(wr,{columns:S,data:a,loading:i,pagination:{current:d,pageSize:m,total:u,onChange:y},sortConfig:{key:f.sortBy||"createdAt",direction:f.sortDesc?"desc":"asc"},onSort:v,rowKey:"_id",onRowClick:B,emptyText:s?"\u52A0\u8F7D\u5931\u8D25":"\u6682\u65E0\u8BA2\u5355\u6570\u636E"}),c(ms,{isOpen:l,onClose:z,title:"\u8BA2\u5355\u8BE6\u60C5",data:r,fields:$,icon:c(la,{className:"h-6 w-6 text-blue-600"})})]})},q1=()=>{const[e,t]=C.exports.useState(!1),[r,n]=C.exports.useState(null),[l,o]=C.exports.useState(!1),{data:a,total:u,loading:i,error:s,page:d,pageSize:m,params:f,refetch:w,updateParams:k,changePage:N,lastRefresh:P,autoRefresh:p,refreshInterval:h,setAutoRefresh:g}=z1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),S=[{key:"_id",title:"ID",dataIndex:"_id",width:120,render:A=>E("span",{className:"font-mono text-xs text-gray-600",children:[A.substring(0,8),"..."]})},{key:"orderID",title:"\u8BA2\u5355ID",dataIndex:"orderID",width:150,sortable:!0,render:A=>E("span",{className:"font-mono text-xs text-blue-600",children:[A.substring(0,12),"..."]})},{key:"userServiceID",title:"\u7528\u6237\u670D\u52A1ID",dataIndex:"userServiceID",width:150,sortable:!0,render:A=>E("span",{className:"font-mono text-xs text-green-600",children:[A.substring(0,12),"..."]})},{key:"orderStatus",title:"\u8BA2\u5355\u72B6\u6001",dataIndex:"orderStatus",width:120,sortable:!0,render:A=>c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${A==="paid"?"bg-green-100 text-green-800":A==="pending"?"bg-yellow-100 text-yellow-800":A==="cancelled"?"bg-red-100 text-red-800":A==="completed"?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:A==="paid"?"\u5DF2\u652F\u4ED8":A==="pending"?"\u5F85\u652F\u4ED8":A==="cancelled"?"\u5DF2\u53D6\u6D88":A==="completed"?"\u5DF2\u5B8C\u6210":A})},{key:"orderType",title:"\u8BA2\u5355\u7C7B\u578B",dataIndex:"orderType",width:120,sortable:!0,render:A=>c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${A==="purchase"?"bg-green-100 text-green-800":A==="refund"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:A==="purchase"?"\u8D2D\u4E70":A==="refund"?"\u9000\u6B3E":A})},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:A=>c("span",{className:"text-sm text-gray-600",children:new Date(A*1e3).toLocaleString()})},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",dataIndex:"updatedAt",width:160,sortable:!0,render:A=>c("span",{className:"text-sm text-gray-600",children:new Date(A*1e3).toLocaleString()})}],F=[{key:"orderID",label:"\u8BA2\u5355ID",type:"text",placeholder:"\u8F93\u5165\u8BA2\u5355ID..."},{key:"userServiceID",label:"\u7528\u6237\u670D\u52A1ID",type:"text",placeholder:"\u8F93\u5165\u7528\u6237\u670D\u52A1ID..."},{key:"orderStatus",label:"\u8BA2\u5355\u72B6\u6001",type:"select",options:[{label:"\u5F85\u652F\u4ED8",value:"pending"},{label:"\u5DF2\u652F\u4ED8",value:"paid"},{label:"\u5DF2\u5B8C\u6210",value:"completed"},{label:"\u5DF2\u53D6\u6D88",value:"cancelled"},{label:"\u5DF2\u9000\u6B3E",value:"refunded"}]},{key:"orderType",label:"\u8BA2\u5355\u7C7B\u578B",type:"select",options:[{label:"\u8D2D\u4E70",value:"purchase"},{label:"\u9000\u6B3E",value:"refund"}]}],v=(A,L)=>{k({sortBy:A,sortDesc:L==="desc",offset:0})},y=(A,L)=>{N(A,L)},B=A=>{n(A),o(!0)},z=()=>{o(!1),n(null)},$=[{key:"_id",label:"ID",type:"id",copyable:!0},{key:"orderID",label:"\u8BA2\u5355ID",type:"id",copyable:!0},{key:"userServiceID",label:"\u7528\u6237\u670D\u52A1ID",type:"id",copyable:!0},{key:"orderStatus",label:"\u8BA2\u5355\u72B6\u6001",type:"text"},{key:"orderType",label:"\u8BA2\u5355\u7C7B\u578B",type:"text"},{key:"createdAt",label:"\u521B\u5EFA\u65F6\u95F4",type:"timestamp"},{key:"updatedAt",label:"\u66F4\u65B0\u65F6\u95F4",type:"timestamp"},{key:"deletedAt",label:"\u5220\u9664\u65F6\u95F4",type:"timestamp"}],G=A=>{const L={offset:0,...A};k(L),t(!1)},ge=()=>{w()},we=()=>{k({orderID:void 0,userServiceID:void 0,orderStatus:void 0,orderType:void 0,offset:0}),t(!1)};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[E("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[c(ii,{className:"h-6 w-6 mr-2 text-blue-600"}),"\u8BA2\u5355\u670D\u52A1\u5173\u8054"]}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u8BA2\u5355\u4E0E\u670D\u52A1\u7684\u5173\u8054\u5173\u7CFB"})]}),E("div",{className:"flex items-center space-x-3",children:[c(Sr,{onRefresh:ge,loading:i,lastRefresh:P,autoRefresh:p,refreshInterval:h,onAutoRefreshChange:g}),E("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[c(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u663E\u793A\u7B5B\u9009"]}),c(kr,{data:a,columns:[{key:"_id",title:"ID"},{key:"orderID",title:"\u8BA2\u5355ID"},{key:"userServiceID",title:"\u7528\u6237\u670D\u52A1ID"},{key:"orderStatus",title:"\u8BA2\u5355\u72B6\u6001"},{key:"orderType",title:"\u8BA2\u5355\u7C7B\u578B"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:A=>Re(A)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:A=>Re(A)}],filename:"order_services",disabled:i||a.length===0})]})]}),e&&c("div",{className:"bg-white rounded-lg shadow p-6",children:c(Hr,{fields:F,onSubmit:G,onReset:we,loading:i,initialValues:f})}),c("div",{className:"bg-white rounded-lg shadow p-6",children:c("div",{className:"flex items-center justify-between",children:E("div",{className:"flex items-center space-x-4",children:[E("div",{className:"flex items-center",children:[c(ii,{className:"h-5 w-5 text-blue-500 mr-2"}),E("span",{className:"text-sm font-medium text-gray-900",children:["\u603B\u5173\u8054\u6570: ",u.toLocaleString()]})]}),f.orderID&&E("div",{className:"text-sm text-gray-600",children:["\u8BA2\u5355ID: ",f.orderID]}),f.userServiceID&&E("div",{className:"text-sm text-gray-600",children:["\u670D\u52A1ID: ",f.userServiceID]}),f.orderStatus&&E("div",{className:"text-sm text-gray-600",children:["\u72B6\u6001: ",f.orderStatus]})]})})}),c("div",{className:"bg-white rounded-lg shadow",children:c(wr,{columns:S,data:a,loading:i,error:s||void 0,pagination:{current:d,pageSize:m,total:u,onChange:y,showSizeChanger:!0,showQuickJumper:!0,showTotal:(A,L)=>`\u663E\u793A ${L[0]}-${L[1]} \u6761\uFF0C\u5171 ${A} \u6761\u8BB0\u5F55`},onSort:v,sortBy:f.sortBy,sortDesc:f.sortDesc,onRowClick:B})}),c(ms,{isOpen:l,onClose:z,title:"\u8BA2\u5355\u670D\u52A1\u5173\u8054\u8BE6\u60C5",data:r,fields:$,icon:c(ii,{className:"h-6 w-6 text-blue-600"})})]})},Z1=()=>{const[e,t]=C.exports.useState(!1),{data:r,total:n,loading:l,error:o,page:a,pageSize:u,params:i,refetch:s,updateParams:d,changePage:m,lastRefresh:f,autoRefresh:w,refreshInterval:k,setAutoRefresh:N}=O1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),P=[{key:"version",title:"\u7248\u672C\u53F7",dataIndex:"version",width:150,sortable:!0,render:y=>E("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-sm font-mono bg-blue-100 text-blue-800",children:[c(h1,{className:"h-3 w-3 mr-1"}),y]})},{key:"minimalSupported",title:"\u6700\u4F4E\u652F\u6301\u7248\u672C",dataIndex:"minimalSupported",width:150,render:y=>c("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-sm font-mono bg-gray-100 text-gray-800",children:y||"-"})},{key:"changeLog",title:"\u66F4\u65B0\u65E5\u5FD7",dataIndex:"changeLog",render:y=>c("div",{className:"max-w-md",children:c("p",{className:"text-sm text-gray-900 line-clamp-3",children:y||"\u6682\u65E0\u66F4\u65B0\u65E5\u5FD7"})})},{key:"createdAt",title:"\u53D1\u5E03\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:y=>c("span",{className:"text-sm text-gray-600",children:y?new Date(y*1e3).toLocaleString():"-"})},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",dataIndex:"updatedAt",width:160,render:y=>c("span",{className:"text-sm text-gray-600",children:y?new Date(y*1e3).toLocaleString():"-"})}],p=[{key:"version",label:"\u7248\u672C\u53F7",type:"text",placeholder:"\u8F93\u5165\u7248\u672C\u53F7..."},{key:"minimalSupported",label:"\u6700\u4F4E\u652F\u6301\u7248\u672C",type:"text",placeholder:"\u8F93\u5165\u6700\u4F4E\u652F\u6301\u7248\u672C..."}],h=(y,B)=>{d({sortBy:y,sortDesc:B==="desc",offset:0})},g=(y,B)=>{m(y,B)},S=y=>{d({offset:0,...y}),t(!1)},F=()=>{s()},v=()=>{d({offset:0,limit:20,sortBy:"createdAt",sortDesc:!0}),t(!1)};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[c("h1",{className:"text-2xl font-bold text-gray-900",children:"CLI\u7248\u672C\u7BA1\u7406"}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406CLI\u7248\u672C\u4FE1\u606F\u548C\u66F4\u65B0\u65E5\u5FD7"})]}),E("div",{className:"flex items-center space-x-3",children:[c(Sr,{onRefresh:F,loading:l,lastRefresh:f,autoRefresh:w,refreshInterval:k,onAutoRefreshChange:N}),E("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[c(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u7B5B\u9009"]}),c(kr,{data:r,columns:[{key:"_id",title:"ID"},{key:"version",title:"\u7248\u672C\u53F7"},{key:"minimalSupported",title:"\u6700\u4F4E\u652F\u6301\u7248\u672C"},{key:"changeLog",title:"\u66F4\u65B0\u65E5\u5FD7"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:y=>Re(y)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:y=>Re(y)}],filename:"cli_versions",disabled:l||r.length===0})]})]}),e&&c(Hr,{fields:p,onSubmit:S,onReset:v,loading:l}),o&&c("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:c("div",{className:"flex",children:E("div",{className:"ml-3",children:[c("h3",{className:"text-sm font-medium text-red-800",children:"\u52A0\u8F7D\u6570\u636E\u65F6\u51FA\u9519"}),c("div",{className:"mt-2 text-sm text-red-700",children:c("p",{children:o})}),c("div",{className:"mt-4",children:c("button",{onClick:F,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"\u91CD\u8BD5"})})]})})}),c(wr,{columns:P,data:r,loading:l,pagination:{current:a,pageSize:u,total:n,onChange:g},sortConfig:{key:i.sortBy||"createdAt",direction:i.sortDesc?"desc":"asc"},onSort:h,rowKey:"_id",onRowClick:y=>{console.log("\u70B9\u51FBCLI\u7248\u672C:",y)},emptyText:o?"\u52A0\u8F7D\u5931\u8D25":"\u6682\u65E0CLI\u7248\u672C\u6570\u636E"})]})},ex=()=>{const[e,t]=C.exports.useState(!1),{data:r,total:n,loading:l,error:o,page:a,pageSize:u,params:i,refetch:s,updateParams:d,changePage:m,lastRefresh:f,autoRefresh:w,refreshInterval:k,setAutoRefresh:N}=$1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),P=[{key:"nameOrId",title:"\u8D27\u5E01\u540D\u79F0/ID",dataIndex:"nameOrId",width:150,sortable:!0,render:y=>E("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-sm font-medium bg-green-100 text-green-800",children:[c(bv,{className:"h-3 w-3 mr-1"}),y]})},{key:"symbolName",title:"\u7B26\u53F7\u540D\u79F0",dataIndex:"symbolName",width:120,render:y=>c("span",{className:"font-mono text-sm font-bold text-blue-600",children:y||"-"})},{key:"contractId",title:"\u5408\u7EA6ID",dataIndex:"contractId",width:200,render:y=>c("span",{className:"font-mono text-xs text-gray-600",children:y?`${y.slice(0,10)}...${y.slice(-8)}`:"-"})},{key:"contractType",title:"\u5408\u7EA6\u7C7B\u578B",dataIndex:"contractType",width:120,render:y=>c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${y==="token"?"bg-blue-100 text-blue-800":y==="nft"?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"}`,children:y||"-"})},{key:"unit",title:"\u5355\u4F4D",dataIndex:"unit",width:100,align:"right",render:y=>c("span",{className:"font-medium",children:y||0})},{key:"exchangeRate",title:"\u6C47\u7387",dataIndex:"exchangeRate",width:120,align:"right",render:y=>c("span",{className:"font-medium text-green-600",children:y?y.toFixed(6):"0.000000"})},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:y=>c("span",{className:"text-sm text-gray-600",children:y?new Date(y*1e3).toLocaleString():"-"})}],p=[{key:"nameOrId",label:"\u8D27\u5E01\u540D\u79F0/ID",type:"text",placeholder:"\u8F93\u5165\u8D27\u5E01\u540D\u79F0\u6216ID..."},{key:"symbolName",label:"\u7B26\u53F7\u540D\u79F0",type:"text",placeholder:"\u8F93\u5165\u7B26\u53F7\u540D\u79F0..."},{key:"contractType",label:"\u5408\u7EA6\u7C7B\u578B",type:"select",options:[{label:"Token",value:"token"},{label:"NFT",value:"nft"}]},{key:"contractId",label:"\u5408\u7EA6ID",type:"text",placeholder:"\u8F93\u5165\u5408\u7EA6ID..."}],h=(y,B)=>{d({sortBy:y,sortDesc:B==="desc",offset:0})},g=(y,B)=>{m(y,B)},S=y=>{d({offset:0,...y}),t(!1)},F=()=>{s()},v=()=>{d({offset:0,limit:20,sortBy:"createdAt",sortDesc:!0}),t(!1)};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[c("h1",{className:"text-2xl font-bold text-gray-900",children:"\u8D27\u5E01\u7BA1\u7406"}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u8D27\u5E01\u4FE1\u606F\u548C\u6C47\u7387\u8BBE\u7F6E"})]}),E("div",{className:"flex items-center space-x-3",children:[c(Sr,{onRefresh:F,loading:l,lastRefresh:f,autoRefresh:w,refreshInterval:k,onAutoRefreshChange:N}),E("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:[c(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u7B5B\u9009"]}),c(kr,{data:r,columns:[{key:"_id",title:"ID"},{key:"nameOrId",title:"\u8D27\u5E01\u540D\u79F0/ID"},{key:"contractId",title:"\u5408\u7EA6ID"},{key:"symbolName",title:"\u7B26\u53F7\u540D\u79F0"},{key:"contractType",title:"\u5408\u7EA6\u7C7B\u578B"},{key:"unit",title:"\u5355\u4F4D"},{key:"exchangeRate",title:"\u6C47\u7387"},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:y=>Re(y)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:y=>Re(y)}],filename:"currencies",disabled:l||r.length===0})]})]}),e&&c(Hr,{fields:p,onSubmit:S,onReset:v,loading:l}),o&&c("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:c("div",{className:"flex",children:E("div",{className:"ml-3",children:[c("h3",{className:"text-sm font-medium text-red-800",children:"\u52A0\u8F7D\u6570\u636E\u65F6\u51FA\u9519"}),c("div",{className:"mt-2 text-sm text-red-700",children:c("p",{children:o})}),c("div",{className:"mt-4",children:c("button",{onClick:F,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:"\u91CD\u8BD5"})})]})})}),c(wr,{columns:P,data:r,loading:l,pagination:{current:a,pageSize:u,total:n,onChange:g},sortConfig:{key:i.sortBy||"createdAt",direction:i.sortDesc?"desc":"asc"},onSort:h,rowKey:"_id",onRowClick:y=>{console.log("\u70B9\u51FB\u8D27\u5E01:",y)},emptyText:o?"\u52A0\u8F7D\u5931\u8D25":"\u6682\u65E0\u8D27\u5E01\u6570\u636E"})]})},tx=()=>{const[e,t]=C.exports.useState(!1);C.exports.useState(null),C.exports.useState(!1);const{data:r,total:n,loading:l,error:o,page:a,pageSize:u,params:i,refetch:s,updateParams:d,changePage:m,lastRefresh:f,autoRefresh:w,refreshInterval:k,setAutoRefresh:N}=j1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),P=[{key:"_id",title:"ID",dataIndex:"_id",width:120,render:y=>E("span",{className:"font-mono text-xs text-gray-600",children:[y.substring(0,8),"..."]})},{key:"name",title:"\u5206\u7C7B\u540D\u79F0",dataIndex:"name",width:150,sortable:!0,render:y=>c("span",{className:"font-medium text-gray-900",children:y})},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546",dataIndex:"provider",width:150,sortable:!0,render:y=>c("span",{className:"text-blue-600 font-medium",children:y})},{key:"description",title:"\u63CF\u8FF0",dataIndex:"description",width:200,render:y=>c("span",{className:"text-sm text-gray-600 truncate",title:y,children:y||"-"})},{key:"apiHost",title:"API\u4E3B\u673A",dataIndex:"apiHost",width:180,render:y=>c("span",{className:"font-mono text-xs text-green-600",children:y||"-"})},{key:"serviceOptions",title:"\u670D\u52A1\u9009\u9879",dataIndex:"serviceOptions",width:150,render:y=>{const B=y?Object.keys(y).length:0;return E("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:[B," \u4E2A\u9009\u9879"]})}},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:y=>c("span",{className:"text-sm text-gray-600",children:new Date(y*1e3).toLocaleString()})},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",dataIndex:"updatedAt",width:160,sortable:!0,render:y=>c("span",{className:"text-sm text-gray-600",children:new Date(y*1e3).toLocaleString()})}],p=[{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u63D0\u4F9B\u5546\u540D\u79F0..."},{key:"name",label:"\u5206\u7C7B\u540D\u79F0",type:"text",placeholder:"\u8F93\u5165\u5206\u7C7B\u540D\u79F0..."}],h=(y,B)=>{d({sortBy:y,sortDesc:B==="desc",offset:0})},g=(y,B)=>{m(y,B)},S=y=>{const B={offset:0,...y};d(B),t(!1)},F=()=>{s()},v=()=>{d({provider:void 0,name:void 0,offset:0}),t(!1)};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[E("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[c(au,{className:"h-6 w-6 mr-2 text-blue-600"}),"\u670D\u52A1\u5206\u7C7B\u7BA1\u7406"]}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u670D\u52A1\u5206\u7C7B\u548C\u914D\u7F6E\u9009\u9879"})]}),E("div",{className:"flex items-center space-x-3",children:[c(Sr,{onRefresh:F,loading:l,lastRefresh:f,autoRefresh:w,refreshInterval:k,onAutoRefreshChange:N}),E("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[c(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u663E\u793A\u7B5B\u9009"]}),c(kr,{data:r,columns:[{key:"_id",title:"ID"},{key:"name",title:"\u5206\u7C7B\u540D\u79F0"},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546"},{key:"description",title:"\u63CF\u8FF0"},{key:"apiHost",title:"API\u4E3B\u673A"},{key:"serviceOptions",title:"\u670D\u52A1\u9009\u9879",render:y=>xn(y)},{key:"name2ID",title:"\u540D\u79F0\u6620\u5C04",render:y=>xn(y)},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:y=>Re(y)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:y=>Re(y)}],filename:"service_categories",disabled:l||r.length===0})]})]}),e&&c("div",{className:"bg-white rounded-lg shadow p-6",children:c(Hr,{fields:p,onSubmit:S,onReset:v,loading:l,initialValues:i})}),c("div",{className:"bg-white rounded-lg shadow p-6",children:c("div",{className:"flex items-center justify-between",children:E("div",{className:"flex items-center space-x-4",children:[E("div",{className:"flex items-center",children:[c(au,{className:"h-5 w-5 text-blue-500 mr-2"}),E("span",{className:"text-sm font-medium text-gray-900",children:["\u603B\u5206\u7C7B\u6570: ",n.toLocaleString()]})]}),i.provider&&E("div",{className:"text-sm text-gray-600",children:["\u63D0\u4F9B\u5546: ",i.provider]}),i.name&&E("div",{className:"text-sm text-gray-600",children:["\u5206\u7C7B: ",i.name]})]})})}),c("div",{className:"bg-white rounded-lg shadow",children:c(wr,{columns:P,data:r,loading:l,error:o||void 0,pagination:{current:a,pageSize:u,total:n,onChange:g,showSizeChanger:!0,showQuickJumper:!0,showTotal:(y,B)=>`\u663E\u793A ${B[0]}-${B[1]} \u6761\uFF0C\u5171 ${y} \u6761\u8BB0\u5F55`},onSort:h,sortBy:i.sortBy,sortDesc:i.sortDesc})})]})},rx=()=>{const[e,t]=C.exports.useState(!1),{data:r,total:n,loading:l,error:o,page:a,pageSize:u,params:i,refetch:s,updateParams:d,changePage:m,lastRefresh:f,autoRefresh:w,refreshInterval:k,setAutoRefresh:N}=U1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),P=[{key:"_id",title:"ID",dataIndex:"_id",width:120,render:y=>E("span",{className:"font-mono text-xs text-gray-600",children:[y.substring(0,8),"..."]})},{key:"name",title:"\u63D0\u4F9B\u5546\u540D\u79F0",dataIndex:"name",width:150,sortable:!0,render:y=>c("span",{className:"font-medium text-gray-900",children:y})},{key:"walletAddress",title:"\u94B1\u5305\u5730\u5740",dataIndex:"walletAddress",width:180,sortable:!0,render:y=>E("span",{className:"font-mono text-xs text-blue-600",children:[y.substring(0,12),"..."]})},{key:"publickey",title:"\u516C\u94A5",dataIndex:"publickey",width:150,render:y=>c("span",{className:"font-mono text-xs text-green-600",children:y?`${y.substring(0,12)}...`:"-"})},{key:"signAddress",title:"\u7B7E\u540D\u5730\u5740",dataIndex:"signAddress",width:150,render:y=>c("span",{className:"font-mono text-xs text-purple-600",children:y?`${y.substring(0,12)}...`:"-"})},{key:"apiHost",title:"API\u4E3B\u673A",dataIndex:"apiHost",width:180,render:y=>c("span",{className:"font-mono text-xs text-green-600",children:y||"-"})},{key:"category2ID",title:"\u5206\u7C7B\u6620\u5C04",dataIndex:"category2ID",width:120,render:y=>{const B=y?Object.keys(y).length:0;return E("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800",children:[B," \u4E2A\u5206\u7C7B"]})}},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:y=>c("span",{className:"text-sm text-gray-600",children:new Date(y*1e3).toLocaleString()})}],p=[{key:"name",label:"\u63D0\u4F9B\u5546\u540D\u79F0",type:"text",placeholder:"\u8F93\u5165\u63D0\u4F9B\u5546\u540D\u79F0..."},{key:"walletAddress",label:"\u94B1\u5305\u5730\u5740",type:"text",placeholder:"\u8F93\u5165\u94B1\u5305\u5730\u5740..."},{key:"publicKey",label:"\u516C\u94A5",type:"text",placeholder:"\u8F93\u5165\u516C\u94A5..."},{key:"signAddress",label:"\u7B7E\u540D\u5730\u5740",type:"text",placeholder:"\u8F93\u5165\u7B7E\u540D\u5730\u5740..."},{key:"apiHost",label:"API\u4E3B\u673A",type:"text",placeholder:"\u8F93\u5165API\u4E3B\u673A\u5730\u5740..."}],h=(y,B)=>{d({sortBy:y,sortDesc:B==="desc",offset:0})},g=(y,B)=>{m(y,B)},S=y=>{const B={offset:0,...y};d(B),t(!1)},F=()=>{s()},v=()=>{d({name:void 0,walletAddress:void 0,publicKey:void 0,signAddress:void 0,apiHost:void 0,offset:0}),t(!1)};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[E("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[c(Zo,{className:"h-6 w-6 mr-2 text-blue-600"}),"\u670D\u52A1\u63D0\u4F9B\u5546\u7BA1\u7406"]}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u670D\u52A1\u63D0\u4F9B\u5546\u4FE1\u606F\u548C\u914D\u7F6E"})]}),E("div",{className:"flex items-center space-x-3",children:[c(Sr,{onRefresh:F,loading:l,lastRefresh:f,autoRefresh:w,refreshInterval:k,onAutoRefreshChange:N}),E("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[c(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u663E\u793A\u7B5B\u9009"]}),c(kr,{data:r,columns:[{key:"_id",title:"ID"},{key:"name",title:"\u63D0\u4F9B\u5546\u540D\u79F0"},{key:"walletAddress",title:"\u94B1\u5305\u5730\u5740"},{key:"publickey",title:"\u516C\u94A5"},{key:"signAddress",title:"\u7B7E\u540D\u5730\u5740"},{key:"apiHost",title:"API\u4E3B\u673A"},{key:"category2ID",title:"\u5206\u7C7B\u6620\u5C04",render:y=>xn(y)},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:y=>Re(y)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:y=>Re(y)}],filename:"providers",disabled:l||r.length===0})]})]}),e&&c("div",{className:"bg-white rounded-lg shadow p-6",children:c(Hr,{fields:p,onSubmit:S,onReset:v,loading:l,initialValues:i})}),c("div",{className:"bg-white rounded-lg shadow p-6",children:c("div",{className:"flex items-center justify-between",children:E("div",{className:"flex items-center space-x-4",children:[E("div",{className:"flex items-center",children:[c(Zo,{className:"h-5 w-5 text-blue-500 mr-2"}),E("span",{className:"text-sm font-medium text-gray-900",children:["\u603B\u63D0\u4F9B\u5546\u6570: ",n.toLocaleString()]})]}),i.name&&E("div",{className:"text-sm text-gray-600",children:["\u540D\u79F0: ",i.name]}),i.walletAddress&&E("div",{className:"text-sm text-gray-600",children:["\u94B1\u5305: ",i.walletAddress]})]})})}),c("div",{className:"bg-white rounded-lg shadow",children:c(wr,{columns:P,data:r,loading:l,error:o||void 0,pagination:{current:a,pageSize:u,total:n,onChange:g,showSizeChanger:!0,showQuickJumper:!0,showTotal:(y,B)=>`\u663E\u793A ${B[0]}-${B[1]} \u6761\uFF0C\u5171 ${y} \u6761\u8BB0\u5F55`},onSort:h,sortBy:i.sortBy,sortDesc:i.sortDesc})})]})},nx=()=>{const[e,t]=C.exports.useState(!1),{data:r,total:n,loading:l,error:o,page:a,pageSize:u,params:i,refetch:s,updateParams:d,changePage:m,lastRefresh:f,autoRefresh:w,refreshInterval:k,setAutoRefresh:N}=V1({limit:20,offset:0,sortBy:"createdAt",sortDesc:!0}),P=[{key:"_id",title:"ID",dataIndex:"_id",width:120,render:y=>E("span",{className:"font-mono text-xs text-gray-600",children:[y.substring(0,8),"..."]})},{key:"name",title:"\u670D\u52A1\u7C7B\u578B\u540D\u79F0",dataIndex:"name",width:150,sortable:!0,render:y=>c("span",{className:"font-medium text-gray-900",children:y})},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546",dataIndex:"provider",width:150,sortable:!0,render:y=>c("span",{className:"text-blue-600 font-medium",children:y})},{key:"category",title:"\u670D\u52A1\u5206\u7C7B",dataIndex:"category",width:120,sortable:!0,render:y=>c("span",{className:"text-green-600 font-medium",children:y})},{key:"refundable",title:"\u53EF\u9000\u6B3E",dataIndex:"refundable",width:100,sortable:!0,render:y=>c("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${y?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:y?"\u662F":"\u5426"})},{key:"description",title:"\u63CF\u8FF0",dataIndex:"description",width:200,render:y=>c("span",{className:"text-sm text-gray-600 truncate",title:y,children:y||"-"})},{key:"apiHost",title:"API\u4E3B\u673A",dataIndex:"apiHost",width:180,render:y=>c("span",{className:"font-mono text-xs text-green-600",children:y||"-"})},{key:"durationToPrice",title:"\u4EF7\u683C\u8BBE\u7F6E",dataIndex:"durationToPrice",width:120,render:y=>{const B=y?y.length:0;return E("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800",children:[B," \u4E2A\u4EF7\u683C"]})}},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",dataIndex:"createdAt",width:160,sortable:!0,render:y=>c("span",{className:"text-sm text-gray-600",children:new Date(y*1e3).toLocaleString()})}],p=[{key:"name",label:"\u670D\u52A1\u7C7B\u578B\u540D\u79F0",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u7C7B\u578B\u540D\u79F0..."},{key:"provider",label:"\u670D\u52A1\u63D0\u4F9B\u5546",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u63D0\u4F9B\u5546..."},{key:"category",label:"\u670D\u52A1\u5206\u7C7B",type:"text",placeholder:"\u8F93\u5165\u670D\u52A1\u5206\u7C7B..."},{key:"categoryID",label:"\u5206\u7C7BID",type:"text",placeholder:"\u8F93\u5165\u5206\u7C7BID..."},{key:"refundable",label:"\u53EF\u9000\u6B3E",type:"select",options:[{label:"\u662F",value:"true"},{label:"\u5426",value:"false"}]}],h=(y,B)=>{d({sortBy:y,sortDesc:B==="desc",offset:0})},g=(y,B)=>{m(y,B)},S=y=>{const B={offset:0,...y};y.refundable&&(B.refundable=y.refundable==="true"),d(B),t(!1)},F=()=>{s()},v=()=>{d({name:void 0,provider:void 0,category:void 0,categoryID:void 0,refundable:void 0,offset:0}),t(!1)};return E("div",{className:"space-y-6",children:[E("div",{className:"flex items-center justify-between",children:[E("div",{children:[E("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[c(Fl,{className:"h-6 w-6 mr-2 text-blue-600"}),"\u670D\u52A1\u7C7B\u578B\u7BA1\u7406"]}),c("p",{className:"text-gray-600 mt-1",children:"\u7BA1\u7406\u670D\u52A1\u7C7B\u578B\u5B9A\u4E49\u548C\u914D\u7F6E"})]}),E("div",{className:"flex items-center space-x-3",children:[c(Sr,{onRefresh:F,loading:l,lastRefresh:f,autoRefresh:w,refreshInterval:k,onAutoRefreshChange:N}),E("button",{onClick:()=>t(!e),className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[c(Ht,{className:"h-4 w-4 mr-2"}),e?"\u9690\u85CF\u7B5B\u9009":"\u663E\u793A\u7B5B\u9009"]}),c(kr,{data:r,columns:[{key:"_id",title:"ID"},{key:"name",title:"\u670D\u52A1\u7C7B\u578B\u540D\u79F0"},{key:"provider",title:"\u670D\u52A1\u63D0\u4F9B\u5546"},{key:"category",title:"\u670D\u52A1\u5206\u7C7B"},{key:"categoryID",title:"\u5206\u7C7BID"},{key:"refundable",title:"\u53EF\u9000\u6B3E",render:y=>r0(y)},{key:"description",title:"\u63CF\u8FF0"},{key:"apiHost",title:"API\u4E3B\u673A"},{key:"serviceOptions",title:"\u670D\u52A1\u9009\u9879",render:y=>xn(y)},{key:"durationToPrice",title:"\u4EF7\u683C\u8BBE\u7F6E",render:y=>xn(y)},{key:"serviceOptionDesc",title:"\u9009\u9879\u63CF\u8FF0",render:y=>xn(y)},{key:"createdAt",title:"\u521B\u5EFA\u65F6\u95F4",render:y=>Re(y)},{key:"updatedAt",title:"\u66F4\u65B0\u65F6\u95F4",render:y=>Re(y)}],filename:"service_types",disabled:l||r.length===0})]})]}),e&&c("div",{className:"bg-white rounded-lg shadow p-6",children:c(Hr,{fields:p,onSubmit:S,onReset:v,loading:l,initialValues:i})}),c("div",{className:"bg-white rounded-lg shadow p-6",children:c("div",{className:"flex items-center justify-between",children:E("div",{className:"flex items-center space-x-4",children:[E("div",{className:"flex items-center",children:[c(Fl,{className:"h-5 w-5 text-blue-500 mr-2"}),E("span",{className:"text-sm font-medium text-gray-900",children:["\u603B\u670D\u52A1\u7C7B\u578B\u6570: ",n.toLocaleString()]})]}),i.name&&E("div",{className:"text-sm text-gray-600",children:["\u7C7B\u578B: ",i.name]}),i.provider&&E("div",{className:"text-sm text-gray-600",children:["\u63D0\u4F9B\u5546: ",i.provider]}),i.category&&E("div",{className:"text-sm text-gray-600",children:["\u5206\u7C7B: ",i.category]})]})})}),c("div",{className:"bg-white rounded-lg shadow",children:c(wr,{columns:P,data:r,loading:l,error:o||void 0,pagination:{current:a,pageSize:u,total:n,onChange:g,showSizeChanger:!0,showQuickJumper:!0,showTotal:(y,B)=>`\u663E\u793A ${B[0]}-${B[1]} \u6761\uFF0C\u5171 ${y} \u6761\u8BB0\u5F55`},onSort:h,sortBy:i.sortBy,sortDesc:i.sortDesc})})]})},lx=rv([{path:"/",element:c(Q1,{}),children:[{index:!0,element:c(Bg,{to:"/user-services",replace:!0})},{path:"dashboard",element:c(K1,{})},{path:"user-services",element:c(J1,{})},{path:"orders",element:c(X1,{})},{path:"order-services",element:c(q1,{})},{path:"cli-versions",element:c(Z1,{})},{path:"currencies",element:c(ex,{})},{path:"service-categories",element:c(tx,{})},{path:"providers",element:c(rx,{})},{path:"service-types",element:c(nx,{})}]}]);function ox(){return c(hv,{router:lx})}const ax=document.getElementById("root"),ix=kh(ax);ix.render(c(T0.StrictMode,{children:c(ox,{})}));
