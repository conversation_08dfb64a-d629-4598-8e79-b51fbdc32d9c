export namespace backend {
	
	export class CliVersionQueryParams {
	    ids?: string[];
	    createdAtStart?: number;
	    createdAtEnd?: number;
	    updatedAtStart?: number;
	    updatedAtEnd?: number;
	    offset?: number;
	    limit?: number;
	    sortBy?: string;
	    sortDesc?: boolean;
	    version?: string;
	    minimalSupported?: string;
	
	    static createFrom(source: any = {}) {
	        return new CliVersionQueryParams(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.ids = source["ids"];
	        this.createdAtStart = source["createdAtStart"];
	        this.createdAtEnd = source["createdAtEnd"];
	        this.updatedAtStart = source["updatedAtStart"];
	        this.updatedAtEnd = source["updatedAtEnd"];
	        this.offset = source["offset"];
	        this.limit = source["limit"];
	        this.sortBy = source["sortBy"];
	        this.sortDesc = source["sortDesc"];
	        this.version = source["version"];
	        this.minimalSupported = source["minimalSupported"];
	    }
	}
	export class CurrencyQueryParams {
	    ids?: string[];
	    createdAtStart?: number;
	    createdAtEnd?: number;
	    updatedAtStart?: number;
	    updatedAtEnd?: number;
	    offset?: number;
	    limit?: number;
	    sortBy?: string;
	    sortDesc?: boolean;
	    nameOrId?: string;
	    contractId?: string;
	    symbolName?: string;
	    contractType?: string;
	
	    static createFrom(source: any = {}) {
	        return new CurrencyQueryParams(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.ids = source["ids"];
	        this.createdAtStart = source["createdAtStart"];
	        this.createdAtEnd = source["createdAtEnd"];
	        this.updatedAtStart = source["updatedAtStart"];
	        this.updatedAtEnd = source["updatedAtEnd"];
	        this.offset = source["offset"];
	        this.limit = source["limit"];
	        this.sortBy = source["sortBy"];
	        this.sortDesc = source["sortDesc"];
	        this.nameOrId = source["nameOrId"];
	        this.contractId = source["contractId"];
	        this.symbolName = source["symbolName"];
	        this.contractType = source["contractType"];
	    }
	}
	export class OrderQueryParams {
	    ids?: string[];
	    createdAtStart?: number;
	    createdAtEnd?: number;
	    updatedAtStart?: number;
	    updatedAtEnd?: number;
	    offset?: number;
	    limit?: number;
	    sortBy?: string;
	    sortDesc?: boolean;
	    serviceID?: string;
	    service?: string;
	    address?: string;
	    recipient?: string;
	    type?: string;
	    statuses?: string[];
	    tsStart?: number;
	    tsEnd?: number;
	
	    static createFrom(source: any = {}) {
	        return new OrderQueryParams(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.ids = source["ids"];
	        this.createdAtStart = source["createdAtStart"];
	        this.createdAtEnd = source["createdAtEnd"];
	        this.updatedAtStart = source["updatedAtStart"];
	        this.updatedAtEnd = source["updatedAtEnd"];
	        this.offset = source["offset"];
	        this.limit = source["limit"];
	        this.sortBy = source["sortBy"];
	        this.sortDesc = source["sortDesc"];
	        this.serviceID = source["serviceID"];
	        this.service = source["service"];
	        this.address = source["address"];
	        this.recipient = source["recipient"];
	        this.type = source["type"];
	        this.statuses = source["statuses"];
	        this.tsStart = source["tsStart"];
	        this.tsEnd = source["tsEnd"];
	    }
	}
	export class OrderServiceQueryParams {
	    ids?: string[];
	    createdAtStart?: number;
	    createdAtEnd?: number;
	    updatedAtStart?: number;
	    updatedAtEnd?: number;
	    offset?: number;
	    limit?: number;
	    sortBy?: string;
	    sortDesc?: boolean;
	    orderID?: string;
	    userServiceID?: string;
	    orderStatus?: string;
	    orderType?: string;
	
	    static createFrom(source: any = {}) {
	        return new OrderServiceQueryParams(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.ids = source["ids"];
	        this.createdAtStart = source["createdAtStart"];
	        this.createdAtEnd = source["createdAtEnd"];
	        this.updatedAtStart = source["updatedAtStart"];
	        this.updatedAtEnd = source["updatedAtEnd"];
	        this.offset = source["offset"];
	        this.limit = source["limit"];
	        this.sortBy = source["sortBy"];
	        this.sortDesc = source["sortDesc"];
	        this.orderID = source["orderID"];
	        this.userServiceID = source["userServiceID"];
	        this.orderStatus = source["orderStatus"];
	        this.orderType = source["orderType"];
	    }
	}
	export class ProviderQueryParams {
	    ids?: string[];
	    createdAtStart?: number;
	    createdAtEnd?: number;
	    updatedAtStart?: number;
	    updatedAtEnd?: number;
	    offset?: number;
	    limit?: number;
	    sortBy?: string;
	    sortDesc?: boolean;
	    name?: string;
	    walletAddress?: string;
	    publickey?: string;
	    signAddress?: string;
	    apiHost?: string;
	
	    static createFrom(source: any = {}) {
	        return new ProviderQueryParams(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.ids = source["ids"];
	        this.createdAtStart = source["createdAtStart"];
	        this.createdAtEnd = source["createdAtEnd"];
	        this.updatedAtStart = source["updatedAtStart"];
	        this.updatedAtEnd = source["updatedAtEnd"];
	        this.offset = source["offset"];
	        this.limit = source["limit"];
	        this.sortBy = source["sortBy"];
	        this.sortDesc = source["sortDesc"];
	        this.name = source["name"];
	        this.walletAddress = source["walletAddress"];
	        this.publickey = source["publickey"];
	        this.signAddress = source["signAddress"];
	        this.apiHost = source["apiHost"];
	    }
	}
	export class ServiceCategoryQueryParams {
	    ids?: string[];
	    createdAtStart?: number;
	    createdAtEnd?: number;
	    updatedAtStart?: number;
	    updatedAtEnd?: number;
	    offset?: number;
	    limit?: number;
	    sortBy?: string;
	    sortDesc?: boolean;
	    provider?: string;
	    name?: string;
	
	    static createFrom(source: any = {}) {
	        return new ServiceCategoryQueryParams(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.ids = source["ids"];
	        this.createdAtStart = source["createdAtStart"];
	        this.createdAtEnd = source["createdAtEnd"];
	        this.updatedAtStart = source["updatedAtStart"];
	        this.updatedAtEnd = source["updatedAtEnd"];
	        this.offset = source["offset"];
	        this.limit = source["limit"];
	        this.sortBy = source["sortBy"];
	        this.sortDesc = source["sortDesc"];
	        this.provider = source["provider"];
	        this.name = source["name"];
	    }
	}
	export class ServiceTypeQueryParams {
	    ids?: string[];
	    createdAtStart?: number;
	    createdAtEnd?: number;
	    updatedAtStart?: number;
	    updatedAtEnd?: number;
	    offset?: number;
	    limit?: number;
	    sortBy?: string;
	    sortDesc?: boolean;
	    name?: string;
	    provider?: string;
	    category?: string;
	    categoryID?: string;
	    refundable?: boolean;
	
	    static createFrom(source: any = {}) {
	        return new ServiceTypeQueryParams(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.ids = source["ids"];
	        this.createdAtStart = source["createdAtStart"];
	        this.createdAtEnd = source["createdAtEnd"];
	        this.updatedAtStart = source["updatedAtStart"];
	        this.updatedAtEnd = source["updatedAtEnd"];
	        this.offset = source["offset"];
	        this.limit = source["limit"];
	        this.sortBy = source["sortBy"];
	        this.sortDesc = source["sortDesc"];
	        this.name = source["name"];
	        this.provider = source["provider"];
	        this.category = source["category"];
	        this.categoryID = source["categoryID"];
	        this.refundable = source["refundable"];
	    }
	}
	export class UserServiceQueryParams {
	    ids?: string[];
	    createdAtStart?: number;
	    createdAtEnd?: number;
	    updatedAtStart?: number;
	    updatedAtEnd?: number;
	    offset?: number;
	    limit?: number;
	    sortBy?: string;
	    sortDesc?: boolean;
	    serviceID?: string;
	    address?: string;
	    provider?: string;
	    providerAddress?: string;
	    status?: string;
	    serviceActivated?: boolean;
	
	    static createFrom(source: any = {}) {
	        return new UserServiceQueryParams(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.ids = source["ids"];
	        this.createdAtStart = source["createdAtStart"];
	        this.createdAtEnd = source["createdAtEnd"];
	        this.updatedAtStart = source["updatedAtStart"];
	        this.updatedAtEnd = source["updatedAtEnd"];
	        this.offset = source["offset"];
	        this.limit = source["limit"];
	        this.sortBy = source["sortBy"];
	        this.sortDesc = source["sortDesc"];
	        this.serviceID = source["serviceID"];
	        this.address = source["address"];
	        this.provider = source["provider"];
	        this.providerAddress = source["providerAddress"];
	        this.status = source["status"];
	        this.serviceActivated = source["serviceActivated"];
	    }
	}

}

