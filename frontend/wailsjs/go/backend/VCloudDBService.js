// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function CountOrders(arg1) {
  return window['go']['backend']['VCloudDBService']['CountOrders'](arg1);
}

export function CountRecords(arg1, arg2) {
  return window['go']['backend']['VCloudDBService']['CountRecords'](arg1, arg2);
}

export function CountUserServices(arg1) {
  return window['go']['backend']['VCloudDBService']['CountUserServices'](arg1);
}

export function FindCliVersions(arg1) {
  return window['go']['backend']['VCloudDBService']['FindCliVersions'](arg1);
}

export function FindCurrencies(arg1) {
  return window['go']['backend']['VCloudDBService']['FindCurrencies'](arg1);
}

export function FindOrderServices(arg1) {
  return window['go']['backend']['VCloudDBService']['FindOrderServices'](arg1);
}

export function FindOrders(arg1) {
  return window['go']['backend']['VCloudDBService']['FindOrders'](arg1);
}

export function FindProviders(arg1) {
  return window['go']['backend']['VCloudDBService']['FindProviders'](arg1);
}

export function FindRecords(arg1, arg2) {
  return window['go']['backend']['VCloudDBService']['FindRecords'](arg1, arg2);
}

export function FindServiceCategories(arg1) {
  return window['go']['backend']['VCloudDBService']['FindServiceCategories'](arg1);
}

export function FindServiceTypes(arg1) {
  return window['go']['backend']['VCloudDBService']['FindServiceTypes'](arg1);
}

export function FindUserServices(arg1) {
  return window['go']['backend']['VCloudDBService']['FindUserServices'](arg1);
}

export function GetConnectionStatus() {
  return window['go']['backend']['VCloudDBService']['GetConnectionStatus']();
}

export function GetRecordById(arg1, arg2) {
  return window['go']['backend']['VCloudDBService']['GetRecordById'](arg1, arg2);
}

export function GetTableNames() {
  return window['go']['backend']['VCloudDBService']['GetTableNames']();
}

export function SetRPCURL(arg1) {
  return window['go']['backend']['VCloudDBService']['SetRPCURL'](arg1);
}

export function WailsInit(arg1) {
  return window['go']['backend']['VCloudDBService']['WailsInit'](arg1);
}

export function WailsShutdown(arg1) {
  return window['go']['backend']['VCloudDBService']['WailsShutdown'](arg1);
}
