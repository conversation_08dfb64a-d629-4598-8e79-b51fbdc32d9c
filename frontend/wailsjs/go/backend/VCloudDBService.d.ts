// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT
import {backend} from '../models';
import {json} from '../models';
import {context} from '../models';

export function CountOrders(arg1:backend.OrderQueryParams):Promise<number>;

export function CountRecords(arg1:string,arg2:{[key: string]: any}):Promise<number>;

export function CountUserServices(arg1:backend.UserServiceQueryParams):Promise<number>;

export function FindCliVersions(arg1:backend.CliVersionQueryParams):Promise<Array<json.RawMessage>>;

export function FindCurrencies(arg1:backend.CurrencyQueryParams):Promise<Array<json.RawMessage>>;

export function FindOrderServices(arg1:backend.OrderServiceQueryParams):Promise<Array<json.RawMessage>>;

export function FindOrders(arg1:backend.OrderQueryParams):Promise<Array<json.RawMessage>>;

export function FindProviders(arg1:backend.ProviderQueryParams):Promise<Array<json.RawMessage>>;

export function FindRecords(arg1:string,arg2:{[key: string]: any}):Promise<Array<json.RawMessage>>;

export function FindServiceCategories(arg1:backend.ServiceCategoryQueryParams):Promise<Array<json.RawMessage>>;

export function FindServiceTypes(arg1:backend.ServiceTypeQueryParams):Promise<Array<json.RawMessage>>;

export function FindUserServices(arg1:backend.UserServiceQueryParams):Promise<Array<json.RawMessage>>;

export function GetConnectionStatus():Promise<{[key: string]: any}>;

export function GetRecordById(arg1:string,arg2:string):Promise<json.RawMessage>;

export function GetTableNames():Promise<Array<string>>;

export function SetRPCURL(arg1:string):Promise<void>;

export function WailsInit(arg1:context.Context):Promise<void>;

export function WailsShutdown(arg1:context.Context):Promise<void>;
