import { useState, useEffect, useCallback, useRef } from 'react';
import { vcloudDbApi, PaginatedResponse, ApiResponse, ConnectionStatus } from '../services/vcloudDbApi';
import {
  UserService,
  Order,
  OrderService,
  CliVersion,
  Currency,
  ServiceCategory,
  Provider,
  ServiceType,
  UserServiceQueryParams,
  OrderQueryParams,
  OrderServiceQueryParams,
  CliVersionQueryParams,
  CurrencyQueryParams,
  ServiceCategoryQueryParams,
  ProviderQueryParams,
  ServiceTypeQueryParams,
  TableName
} from '../types/models';

// 通用查询Hook状态
export interface QueryState<T> {
  data: T[];
  total: number;
  loading: boolean;
  error: string | null;
  page: number;
  pageSize: number;
}

// 连接状态Hook
export function useConnectionStatus() {
  const [status, setStatus] = useState<ConnectionStatus>({
    connected: false,
    rpcUrl: ''
  });
  const [loading, setLoading] = useState(false);
  const intervalRef = useRef<number>();

  const checkConnection = useCallback(async () => {
    setLoading(true);
    try {
      const response = await vcloudDbApi.getConnectionStatus();
      if (response.success) {
        setStatus(response.data);
      } else {
        setStatus({
          connected: false,
          rpcUrl: '',
          error: response.error
        });
      }
    } catch (error) {
      setStatus({
        connected: false,
        rpcUrl: '',
        error: error instanceof Error ? error.message : '连接检查失败'
      });
    } finally {
      setLoading(false);
    }
  }, []);

  const setRPCURL = useCallback(async (url: string) => {
    setLoading(true);
    try {
      const response = await vcloudDbApi.setRPCURL(url);
      if (response.success) {
        await checkConnection();
        return { success: true };
      } else {
        return { success: false, error: response.error };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '设置RPC URL失败'
      };
    } finally {
      setLoading(false);
    }
  }, [checkConnection]);

  useEffect(() => {
    checkConnection();
    
    // 每30秒检查一次连接状态
    intervalRef.current = setInterval(checkConnection, 30000);
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [checkConnection]);

  return {
    status,
    loading,
    checkConnection,
    setRPCURL
  };
}

// 通用分页查询Hook
export function usePaginatedQuery<T, P>(
  queryFn: (params: P) => Promise<PaginatedResponse<T>>,
  initialParams: P,
  dependencies: any[] = []
) {
  const [state, setState] = useState<QueryState<T>>({
    data: [],
    total: 0,
    loading: false,
    error: null,
    page: 1,
    pageSize: 20
  });

  const [params, setParams] = useState<P>(initialParams);
  const abortControllerRef = useRef<AbortController>();

  const executeQuery = useCallback(async (queryParams: P) => {
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await queryFn(queryParams);
      
      if (response.success) {
        setState(prev => ({
          ...prev,
          data: response.data,
          total: response.total,
          page: response.page,
          pageSize: response.pageSize,
          loading: false,
          error: null
        }));
      } else {
        setState(prev => ({
          ...prev,
          data: [],
          total: 0,
          loading: false,
          error: response.error || '查询失败'
        }));
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        setState(prev => ({
          ...prev,
          data: [],
          total: 0,
          loading: false,
          error: error instanceof Error ? error.message : String(error)
        }));
      }
    }
  }, [queryFn]);

  const refetch = useCallback(() => {
    executeQuery(params);
  }, [executeQuery, params]);

  const updateParams = useCallback((newParams: Partial<P>) => {
    const updatedParams = { ...params, ...newParams };
    setParams(updatedParams);
    executeQuery(updatedParams);
  }, [params, executeQuery]);

  const changePage = useCallback((page: number, pageSize?: number) => {
    const newPageSize = pageSize || state.pageSize;
    const offset = (page - 1) * newPageSize;
    updateParams({ offset, limit: newPageSize } as unknown as Partial<P>);
  }, [state.pageSize, updateParams]);

  useEffect(() => {
    executeQuery(params);
  }, dependencies);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    ...state,
    params,
    refetch,
    updateParams,
    changePage
  };
}

// 用户服务查询Hook
export function useUserServices(initialParams: UserServiceQueryParams = {}) {
  return usePaginatedQuery(
    vcloudDbApi.findUserServices.bind(vcloudDbApi),
    { limit: 20, offset: 0, ...initialParams },
    []
  );
}

// 订单查询Hook
export function useOrders(initialParams: OrderQueryParams = {}) {
  return usePaginatedQuery(
    vcloudDbApi.findOrders.bind(vcloudDbApi),
    { limit: 20, offset: 0, ...initialParams },
    []
  );
}

// 订单服务查询Hook
export function useOrderServices(initialParams: OrderServiceQueryParams = {}) {
  return usePaginatedQuery(
    vcloudDbApi.findOrderServices.bind(vcloudDbApi),
    { limit: 20, offset: 0, ...initialParams },
    []
  );
}

// CLI版本查询Hook
export function useCliVersions(initialParams: CliVersionQueryParams = {}) {
  return usePaginatedQuery(
    vcloudDbApi.findCliVersions.bind(vcloudDbApi),
    { limit: 20, offset: 0, ...initialParams },
    []
  );
}

// 货币查询Hook
export function useCurrencies(initialParams: CurrencyQueryParams = {}) {
  return usePaginatedQuery(
    vcloudDbApi.findCurrencies.bind(vcloudDbApi),
    { limit: 20, offset: 0, ...initialParams },
    []
  );
}

// 服务分类查询Hook
export function useServiceCategories(initialParams: ServiceCategoryQueryParams = {}) {
  return usePaginatedQuery(
    vcloudDbApi.findServiceCategories.bind(vcloudDbApi),
    { limit: 20, offset: 0, ...initialParams },
    []
  );
}

// 服务提供商查询Hook
export function useProviders(initialParams: ProviderQueryParams = {}) {
  return usePaginatedQuery(
    vcloudDbApi.findProviders.bind(vcloudDbApi),
    { limit: 20, offset: 0, ...initialParams },
    []
  );
}

// 服务类型查询Hook
export function useServiceTypes(initialParams: ServiceTypeQueryParams = {}) {
  return usePaginatedQuery(
    vcloudDbApi.findServiceTypes.bind(vcloudDbApi),
    { limit: 20, offset: 0, ...initialParams },
    []
  );
}

// 通用记录查询Hook
export function useRecord<T>(tableName: TableName, id: string | null) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchRecord = useCallback(async () => {
    if (!id) {
      setData(null);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await vcloudDbApi.getRecordById(tableName, id);
      if (response.success) {
        setData(response.data);
      } else {
        setError(response.error || '获取记录失败');
        setData(null);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '获取记录失败');
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [tableName, id]);

  useEffect(() => {
    fetchRecord();
  }, [fetchRecord]);

  return {
    data,
    loading,
    error,
    refetch: fetchRecord
  };
}

// 表名列表Hook
export function useTableNames() {
  const [tableNames, setTableNames] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTableNames = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await vcloudDbApi.getTableNames();
      if (response.success) {
        setTableNames(response.data);
      } else {
        setError(response.error || '获取表名列表失败');
        setTableNames([]);
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '获取表名列表失败');
      setTableNames([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchTableNames();
  }, [fetchTableNames]);

  return {
    tableNames,
    loading,
    error,
    refetch: fetchTableNames
  };
}

// 缓存管理Hook
export function useApiCache() {
  const clearCache = useCallback(() => {
    vcloudDbApi.clearCache();
  }, []);

  return {
    clearCache
  };
}
