import {
  FindRecords,
  GetRecordById,
  CountRecords,
  GetConnectionStatus,
  SetRPCURL,
  FindUserServices,
  FindOrders,
  FindOrderServices,
  FindCliVersions,
  FindCurrencies,
  FindServiceCategories,
  FindProviders,
  FindServiceTypes,
  CountUserServices,
  CountOrders,
  GetTableNames
} from '../../wailsjs/go/backend/VCloudDBService';

import {
  UserService,
  Order,
  OrderService,
  CliVersion,
  Currency,
  ServiceCategory,
  Provider,
  ServiceType,
  UserServiceQueryParams,
  OrderQueryParams,
  OrderServiceQueryParams,
  CliVersionQueryParams,
  CurrencyQueryParams,
  ServiceCategoryQueryParams,
  ProviderQueryParams,
  ServiceTypeQueryParams,
  TableName,
  TABLE_NAMES
} from '../types/models';

// API响应类型
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  success: boolean;
  error?: string;
}

export interface ConnectionStatus {
  connected: boolean;
  rpcUrl: string;
  lastConnected?: string;
  error?: string;
}

// 错误处理类
export class VCloudDbApiError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = 'VCloudDbApiError';
  }
}

// API服务类
export class VCloudDbApi {
  private static instance: VCloudDbApi;
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  private constructor() {}

  public static getInstance(): VCloudDbApi {
    if (!VCloudDbApi.instance) {
      VCloudDbApi.instance = new VCloudDbApi();
    }
    return VCloudDbApi.instance;
  }

  // 缓存管理
  private getCacheKey(method: string, params: any): string {
    return `${method}_${JSON.stringify(params)}`;
  }

  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  public clearCache(): void {
    this.cache.clear();
  }

  // 错误处理包装器
  private async handleApiCall<T>(
    apiCall: () => Promise<T>,
    errorMessage: string,
    useCache = false,
    cacheKey?: string
  ): Promise<ApiResponse<T>> {
    try {
      // 检查缓存
      if (useCache && cacheKey) {
        const cached = this.getFromCache<T>(cacheKey);
        if (cached) {
          return { data: cached, success: true };
        }
      }

      const result = await apiCall();
      
      // 设置缓存
      if (useCache && cacheKey) {
        this.setCache(cacheKey, result);
      }

      return { data: result, success: true };
    } catch (error) {
      console.error(`${errorMessage}:`, error);
      return {
        data: null as any,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // 连接管理
  public async getConnectionStatus(): Promise<ApiResponse<ConnectionStatus>> {
    return this.handleApiCall(
      async () => {
        const result = await GetConnectionStatus();
        return result as ConnectionStatus;
      },
      '获取连接状态失败'
    );
  }

  public async setRPCURL(url: string): Promise<ApiResponse<void>> {
    this.clearCache(); // 清除缓存，因为连接已更改
    return this.handleApiCall(
      () => SetRPCURL(url),
      '设置RPC URL失败'
    );
  }

  // 通用查询方法
  public async findRecords(
    tableName: TableName,
    filter: Record<string, any> = {}
  ): Promise<ApiResponse<any[]>> {
    const cacheKey = this.getCacheKey('findRecords', { tableName, filter });
    return this.handleApiCall(
      async () => {
        const records = await FindRecords(tableName, filter);
        return records.map(record => JSON.parse(record as any));
      },
      `查询${tableName}表记录失败`,
      true,
      cacheKey
    );
  }

  public async getRecordById(
    tableName: TableName,
    id: string
  ): Promise<ApiResponse<any>> {
    const cacheKey = this.getCacheKey('getRecordById', { tableName, id });
    return this.handleApiCall(
      async () => {
        const record = await GetRecordById(tableName, id);
        return JSON.parse(record as any);
      },
      `获取${tableName}表记录失败`,
      true,
      cacheKey
    );
  }

  public async countRecords(
    tableName: TableName,
    filter: Record<string, any> = {}
  ): Promise<ApiResponse<number>> {
    const cacheKey = this.getCacheKey('countRecords', { tableName, filter });
    return this.handleApiCall(
      () => CountRecords(tableName, filter),
      `统计${tableName}表记录失败`,
      true,
      cacheKey
    );
  }

  // 表特定查询方法
  public async findUserServices(
    params: UserServiceQueryParams = {}
  ): Promise<PaginatedResponse<UserService>> {
    const cacheKey = this.getCacheKey('findUserServices', params);
    
    try {
      // 检查缓存
      const cached = this.getFromCache<PaginatedResponse<UserService>>(cacheKey);
      if (cached) return cached;

      const [recordsResponse, countResponse] = await Promise.all([
        this.handleApiCall(
          async () => {
            const records = await FindUserServices(params);
            return records.map(record => JSON.parse(record as any));
          },
          '查询用户服务失败'
        ),
        this.handleApiCall(
          () => CountUserServices(params),
          '统计用户服务失败'
        )
      ]);

      if (!recordsResponse.success || !countResponse.success) {
        return {
          data: [],
          total: 0,
          page: Math.floor((params.offset || 0) / (params.limit || 20)) + 1,
          pageSize: params.limit || 20,
          success: false,
          error: recordsResponse.error || countResponse.error
        };
      }

      const result: PaginatedResponse<UserService> = {
        data: recordsResponse.data,
        total: countResponse.data,
        page: Math.floor((params.offset || 0) / (params.limit || 20)) + 1,
        pageSize: params.limit || 20,
        success: true
      };

      // 设置缓存
      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      console.error('查询用户服务失败:', error);
      return {
        data: [],
        total: 0,
        page: 1,
        pageSize: params.limit || 20,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  public async findOrders(
    params: OrderQueryParams = {}
  ): Promise<PaginatedResponse<Order>> {
    const cacheKey = this.getCacheKey('findOrders', params);

    try {
      const cached = this.getFromCache<PaginatedResponse<Order>>(cacheKey);
      if (cached) return cached;

      const [recordsResponse, countResponse] = await Promise.all([
        this.handleApiCall(
          async () => {
            const records = await FindOrders(params);
            return records.map(record => JSON.parse(record as any));
          },
          '查询订单失败'
        ),
        this.handleApiCall(
          () => CountOrders(params),
          '统计订单失败'
        )
      ]);

      if (!recordsResponse.success || !countResponse.success) {
        return {
          data: [],
          total: 0,
          page: Math.floor((params.offset || 0) / (params.limit || 20)) + 1,
          pageSize: params.limit || 20,
          success: false,
          error: recordsResponse.error || countResponse.error
        };
      }

      const result: PaginatedResponse<Order> = {
        data: recordsResponse.data,
        total: countResponse.data,
        page: Math.floor((params.offset || 0) / (params.limit || 20)) + 1,
        pageSize: params.limit || 20,
        success: true
      };

      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      console.error('查询订单失败:', error);
      return {
        data: [],
        total: 0,
        page: 1,
        pageSize: params.limit || 20,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // 其他表的查询方法
  public async findOrderServices(
    params: OrderServiceQueryParams = {}
  ): Promise<PaginatedResponse<OrderService>> {
    const cacheKey = this.getCacheKey('findOrderServices', params);

    try {
      const cached = this.getFromCache<PaginatedResponse<OrderService>>(cacheKey);
      if (cached) return cached;

      const recordsResponse = await this.handleApiCall(
        async () => {
          const records = await FindOrderServices(params);
          return records.map(record => JSON.parse(record as any));
        },
        '查询订单服务失败'
      );

      // OrderService表没有专门的计数方法，使用通用方法
      const countResponse = await this.countRecords(TABLE_NAMES.ORDER_SERVICE, params);

      if (!recordsResponse.success || !countResponse.success) {
        return {
          data: [],
          total: 0,
          page: Math.floor((params.offset || 0) / (params.limit || 20)) + 1,
          pageSize: params.limit || 20,
          success: false,
          error: recordsResponse.error || countResponse.error
        };
      }

      const result: PaginatedResponse<OrderService> = {
        data: recordsResponse.data,
        total: countResponse.data,
        page: Math.floor((params.offset || 0) / (params.limit || 20)) + 1,
        pageSize: params.limit || 20,
        success: true
      };

      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      console.error('查询订单服务失败:', error);
      return {
        data: [],
        total: 0,
        page: 1,
        pageSize: params.limit || 20,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  public async findCliVersions(
    params: CliVersionQueryParams = {}
  ): Promise<PaginatedResponse<CliVersion>> {
    const cacheKey = this.getCacheKey('findCliVersions', params);

    try {
      const cached = this.getFromCache<PaginatedResponse<CliVersion>>(cacheKey);
      if (cached) return cached;

      const recordsResponse = await this.handleApiCall(
        async () => {
          const records = await FindCliVersions(params);
          return records.map(record => JSON.parse(record as any));
        },
        '查询CLI版本失败'
      );

      const countResponse = await this.countRecords(TABLE_NAMES.CLI_VERSION, params);

      if (!recordsResponse.success || !countResponse.success) {
        return {
          data: [],
          total: 0,
          page: Math.floor((params.offset || 0) / (params.limit || 20)) + 1,
          pageSize: params.limit || 20,
          success: false,
          error: recordsResponse.error || countResponse.error
        };
      }

      const result: PaginatedResponse<CliVersion> = {
        data: recordsResponse.data,
        total: countResponse.data,
        page: Math.floor((params.offset || 0) / (params.limit || 20)) + 1,
        pageSize: params.limit || 20,
        success: true
      };

      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      console.error('查询CLI版本失败:', error);
      return {
        data: [],
        total: 0,
        page: 1,
        pageSize: params.limit || 20,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  public async findCurrencies(
    params: CurrencyQueryParams = {}
  ): Promise<PaginatedResponse<Currency>> {
    const cacheKey = this.getCacheKey('findCurrencies', params);

    try {
      const cached = this.getFromCache<PaginatedResponse<Currency>>(cacheKey);
      if (cached) return cached;

      const recordsResponse = await this.handleApiCall(
        async () => {
          const records = await FindCurrencies(params);
          return records.map(record => JSON.parse(record as any));
        },
        '查询货币失败'
      );

      const countResponse = await this.countRecords(TABLE_NAMES.CURRENCY, params);

      if (!recordsResponse.success || !countResponse.success) {
        return {
          data: [],
          total: 0,
          page: Math.floor((params.offset || 0) / (params.limit || 20)) + 1,
          pageSize: params.limit || 20,
          success: false,
          error: recordsResponse.error || countResponse.error
        };
      }

      const result: PaginatedResponse<Currency> = {
        data: recordsResponse.data,
        total: countResponse.data,
        page: Math.floor((params.offset || 0) / (params.limit || 20)) + 1,
        pageSize: params.limit || 20,
        success: true
      };

      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      console.error('查询货币失败:', error);
      return {
        data: [],
        total: 0,
        page: 1,
        pageSize: params.limit || 20,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // 服务分类查询
  public async findServiceCategories(params: ServiceCategoryQueryParams): Promise<PaginatedResponse<ServiceCategory>> {
    const cacheKey = this.getCacheKey('findServiceCategories', params);

    try {
      const cached = this.getFromCache<PaginatedResponse<ServiceCategory>>(cacheKey);
      if (cached) return cached;

      const recordsResponse = await this.handleApiCall(
        async () => {
          const records = await this.findRecords('ServiceCategory', params);
          return records.data as ServiceCategory[];
        },
        '查询服务分类失败'
      );

      if (!recordsResponse.success) {
        return {
          data: [],
          total: 0,
          page: 1,
          pageSize: params.limit || 20,
          success: false,
          error: recordsResponse.error
        };
      }

      const result: PaginatedResponse<ServiceCategory> = {
        data: recordsResponse.data,
        total: recordsResponse.data.length,
        page: Math.floor((params.offset || 0) / (params.limit || 20)) + 1,
        pageSize: params.limit || 20,
        success: true
      };

      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      return {
        data: [],
        total: 0,
        page: 1,
        pageSize: params.limit || 20,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // 服务提供商查询
  public async findProviders(params: ProviderQueryParams): Promise<PaginatedResponse<Provider>> {
    const cacheKey = this.getCacheKey('findProviders', params);

    try {
      const cached = this.getFromCache<PaginatedResponse<Provider>>(cacheKey);
      if (cached) return cached;

      const recordsResponse = await this.handleApiCall(
        async () => {
          const records = await this.findRecords('Provider', params);
          return records.data as Provider[];
        },
        '查询服务提供商失败'
      );

      if (!recordsResponse.success) {
        return {
          data: [],
          total: 0,
          page: 1,
          pageSize: params.limit || 20,
          success: false,
          error: recordsResponse.error
        };
      }

      const result: PaginatedResponse<Provider> = {
        data: recordsResponse.data,
        total: recordsResponse.data.length,
        page: Math.floor((params.offset || 0) / (params.limit || 20)) + 1,
        pageSize: params.limit || 20,
        success: true
      };

      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      return {
        data: [],
        total: 0,
        page: 1,
        pageSize: params.limit || 20,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // 服务类型查询
  public async findServiceTypes(params: ServiceTypeQueryParams): Promise<PaginatedResponse<ServiceType>> {
    const cacheKey = this.getCacheKey('findServiceTypes', params);

    try {
      const cached = this.getFromCache<PaginatedResponse<ServiceType>>(cacheKey);
      if (cached) return cached;

      const recordsResponse = await this.handleApiCall(
        async () => {
          const records = await this.findRecords('ServiceType', params);
          return records.data as ServiceType[];
        },
        '查询服务类型失败'
      );

      if (!recordsResponse.success) {
        return {
          data: [],
          total: 0,
          page: 1,
          pageSize: params.limit || 20,
          success: false,
          error: recordsResponse.error
        };
      }

      const result: PaginatedResponse<ServiceType> = {
        data: recordsResponse.data,
        total: recordsResponse.data.length,
        page: Math.floor((params.offset || 0) / (params.limit || 20)) + 1,
        pageSize: params.limit || 20,
        success: true
      };

      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      return {
        data: [],
        total: 0,
        page: 1,
        pageSize: params.limit || 20,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // 获取表名列表
  public async getTableNames(): Promise<ApiResponse<string[]>> {
    return this.handleApiCall(
      () => GetTableNames(),
      '获取表名列表失败',
      true,
      'tableNames'
    );
  }
}

// 导出单例实例
export const vcloudDbApi = VCloudDbApi.getInstance();
