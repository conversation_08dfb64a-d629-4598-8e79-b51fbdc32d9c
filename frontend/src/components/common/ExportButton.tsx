import React, { useState } from 'react';
import { Download, FileText, Database } from 'lucide-react';
import { exportToCSV, exportToJSON, ExportColumn, ExportOptions } from '../../utils/exportUtils';

export interface ExportButtonProps {
  data: any[];
  columns?: ExportColumn[];
  filename?: string;
  disabled?: boolean;
  className?: string;
}

export const ExportButton: React.FC<ExportButtonProps> = ({
  data,
  columns,
  filename = 'export',
  disabled = false,
  className = ''
}) => {
  const [showDropdown, setShowDropdown] = useState(false);

  const handleExport = (format: 'csv' | 'json') => {
    const options: ExportOptions = {
      data,
      columns,
      filename: `${filename}_${new Date().toISOString().split('T')[0]}`
    };

    if (format === 'csv') {
      exportToCSV(options);
    } else {
      exportToJSON(options);
    }

    setShowDropdown(false);
  };

  if (disabled || !data || data.length === 0) {
    return (
      <button
        disabled
        className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-400 bg-gray-100 cursor-not-allowed ${className}`}
      >
        <Download className="h-4 w-4 mr-2" />
        导出数据
      </button>
    );
  }

  return (
    <div className="relative">
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        className={`inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${className}`}
      >
        <Download className="h-4 w-4 mr-2" />
        导出数据
        <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {showDropdown && (
        <>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setShowDropdown(false)}
          />
          
          {/* 下拉菜单 */}
          <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-20">
            <div className="py-1">
              <button
                onClick={() => handleExport('csv')}
                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              >
                <FileText className="h-4 w-4 mr-3 text-green-500" />
                导出为 CSV
                <span className="ml-auto text-xs text-gray-500">Excel兼容</span>
              </button>
              
              <button
                onClick={() => handleExport('json')}
                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              >
                <Database className="h-4 w-4 mr-3 text-blue-500" />
                导出为 JSON
                <span className="ml-auto text-xs text-gray-500">结构化数据</span>
              </button>
            </div>
            
            {/* 导出信息 */}
            <div className="border-t border-gray-100 px-4 py-2">
              <div className="text-xs text-gray-500">
                共 {data.length} 条记录
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ExportButton;
