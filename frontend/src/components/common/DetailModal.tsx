import React from 'react';
import { X, Copy, ExternalLink, Calendar, User, Hash, DollarSign, Settings, Info } from 'lucide-react';

export interface DetailField {
  key: string;
  label: string;
  type?: 'text' | 'number' | 'boolean' | 'timestamp' | 'json' | 'address' | 'id' | 'amount';
  render?: (value: any, record: any) => React.ReactNode;
  copyable?: boolean;
  linkable?: boolean;
}

export interface DetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  data: any;
  fields: DetailField[];
  icon?: React.ReactNode;
}

export const DetailModal: React.FC<DetailModalProps> = ({
  isOpen,
  onClose,
  title,
  data,
  fields,
  icon
}) => {
  if (!isOpen || !data) return null;

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      // 可以添加一个toast提示
      console.log('已复制到剪贴板');
    });
  };

  const formatValue = (field: DetailField, value: any): React.ReactNode => {
    if (value === null || value === undefined) {
      return <span className="text-gray-400">-</span>;
    }

    // 如果有自定义渲染函数，使用它
    if (field.render) {
      return field.render(value, data);
    }

    // 根据类型格式化值
    switch (field.type) {
      case 'boolean':
        return (
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {value ? '是' : '否'}
          </span>
        );
      
      case 'timestamp':
        return (
          <div className="flex items-center">
            <Calendar className="h-4 w-4 text-gray-400 mr-2" />
            <span>{value ? new Date(value * 1000).toLocaleString() : '-'}</span>
          </div>
        );
      
      case 'json':
        return (
          <pre className="bg-gray-50 p-2 rounded text-xs overflow-x-auto max-w-md">
            {JSON.stringify(value, null, 2)}
          </pre>
        );
      
      case 'address':
        return (
          <div className="flex items-center">
            <User className="h-4 w-4 text-blue-500 mr-2" />
            <span className="font-mono text-sm">{value}</span>
          </div>
        );
      
      case 'id':
        return (
          <div className="flex items-center">
            <Hash className="h-4 w-4 text-gray-500 mr-2" />
            <span className="font-mono text-sm">{value}</span>
          </div>
        );
      
      case 'amount':
        return (
          <div className="flex items-center">
            <DollarSign className="h-4 w-4 text-green-500 mr-2" />
            <span className="font-medium">{Number(value).toLocaleString()}</span>
          </div>
        );
      
      case 'number':
        return <span className="font-medium">{Number(value).toLocaleString()}</span>;
      
      default:
        return <span>{String(value)}</span>;
    }
  };

  const getFieldIcon = (field: DetailField) => {
    switch (field.type) {
      case 'timestamp':
        return <Calendar className="h-4 w-4 text-gray-400" />;
      case 'address':
        return <User className="h-4 w-4 text-blue-500" />;
      case 'id':
        return <Hash className="h-4 w-4 text-gray-500" />;
      case 'amount':
        return <DollarSign className="h-4 w-4 text-green-500" />;
      case 'json':
        return <Settings className="h-4 w-4 text-purple-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* 弹窗内容 */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center">
              {icon && <div className="mr-3">{icon}</div>}
              <h3 className="text-lg font-medium text-gray-900">{title}</h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
          
          {/* 内容区域 */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {fields.map((field) => {
                const value = data[field.key];
                const displayValue = formatValue(field, value);
                
                return (
                  <div key={field.key} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <label className="flex items-center text-sm font-medium text-gray-700">
                        {getFieldIcon(field)}
                        <span className="ml-2">{field.label}</span>
                      </label>
                      
                      {/* 复制和链接按钮 */}
                      <div className="flex space-x-1">
                        {field.copyable && value && (
                          <button
                            onClick={() => handleCopy(String(value))}
                            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                            title="复制"
                          >
                            <Copy className="h-4 w-4" />
                          </button>
                        )}
                        {field.linkable && value && (
                          <button
                            onClick={() => window.open(String(value), '_blank')}
                            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                            title="打开链接"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 rounded-md p-3 min-h-[2.5rem] flex items-center">
                      {displayValue}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          
          {/* 底部操作区 */}
          <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailModal;
