import React from 'react';
import { clsx } from 'clsx';
import { Loader2, Database, Wifi, AlertCircle } from 'lucide-react';

interface LoadingStateProps {
  loading?: boolean;
  error?: string | null;
  empty?: boolean;
  emptyMessage?: string;
  loadingMessage?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'card' | 'inline';
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  loading = false,
  error = null,
  empty = false,
  emptyMessage = '暂无数据',
  loadingMessage = '加载中...',
  className,
  size = 'md',
  variant = 'default'
}) => {
  const sizeClasses = {
    sm: 'h-16',
    md: 'h-32',
    lg: 'h-48'
  };

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  const textSizes = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  const baseClasses = clsx(
    'flex flex-col items-center justify-center',
    sizeClasses[size],
    variant === 'card' && 'bg-white rounded-lg border border-gray-200 shadow-sm',
    variant === 'inline' && 'py-4',
    className
  );

  if (loading) {
    return (
      <div className={baseClasses}>
        <Loader2 className={clsx(iconSizes[size], 'animate-spin text-blue-500 mb-2')} />
        <p className={clsx(textSizes[size], 'text-gray-600')}>{loadingMessage}</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={baseClasses}>
        <AlertCircle className={clsx(iconSizes[size], 'text-red-500 mb-2')} />
        <p className={clsx(textSizes[size], 'text-red-600 text-center')}>
          {error}
        </p>
      </div>
    );
  }

  if (empty) {
    return (
      <div className={baseClasses}>
        <Database className={clsx(iconSizes[size], 'text-gray-400 mb-2')} />
        <p className={clsx(textSizes[size], 'text-gray-500')}>{emptyMessage}</p>
      </div>
    );
  }

  return null;
};

// 数据表格加载状态组件
interface TableLoadingStateProps {
  loading?: boolean;
  error?: string | null;
  empty?: boolean;
  emptyMessage?: string;
  rows?: number;
  columns?: number;
}

export const TableLoadingState: React.FC<TableLoadingStateProps> = ({
  loading = false,
  error = null,
  empty = false,
  emptyMessage = '暂无数据',
  rows = 5,
  columns = 4
}) => {
  if (loading) {
    return (
      <div className="animate-pulse">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="flex space-x-4 py-3 border-b border-gray-200">
            {Array.from({ length: columns }).map((_, colIndex) => (
              <div
                key={colIndex}
                className="flex-1 h-4 bg-gray-200 rounded"
                style={{ width: `${Math.random() * 40 + 60}%` }}
              />
            ))}
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  if (empty) {
    return (
      <div className="text-center py-8">
        <Database className="h-8 w-8 text-gray-400 mx-auto mb-2" />
        <p className="text-gray-500">{emptyMessage}</p>
      </div>
    );
  }

  return null;
};

// 连接状态指示器
interface ConnectionIndicatorProps {
  connected: boolean;
  loading?: boolean;
  error?: string;
  className?: string;
}

export const ConnectionIndicator: React.FC<ConnectionIndicatorProps> = ({
  connected,
  loading = false,
  error,
  className
}) => {
  return (
    <div className={clsx('flex items-center space-x-2', className)}>
      {loading ? (
        <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      ) : connected ? (
        <Wifi className="h-4 w-4 text-green-500" />
      ) : (
        <AlertCircle className="h-4 w-4 text-red-500" />
      )}
      <span className={clsx(
        'text-sm font-medium',
        loading && 'text-blue-600',
        connected && !loading && 'text-green-600',
        !connected && !loading && 'text-red-600'
      )}>
        {loading ? '连接中...' : connected ? '已连接' : '连接失败'}
      </span>
      {error && (
        <span className="text-xs text-red-500" title={error}>
          ({error})
        </span>
      )}
    </div>
  );
};

// 进度条组件
interface ProgressBarProps {
  progress: number; // 0-100
  label?: string;
  showPercentage?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'green' | 'yellow' | 'red';
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  label,
  showPercentage = true,
  className,
  size = 'md',
  color = 'blue'
}) => {
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };

  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    red: 'bg-red-500'
  };

  const clampedProgress = Math.max(0, Math.min(100, progress));

  return (
    <div className={clsx('w-full', className)}>
      {(label || showPercentage) && (
        <div className="flex justify-between items-center mb-1">
          {label && <span className="text-sm text-gray-600">{label}</span>}
          {showPercentage && (
            <span className="text-sm text-gray-600">{Math.round(clampedProgress)}%</span>
          )}
        </div>
      )}
      <div className={clsx('w-full bg-gray-200 rounded-full', sizeClasses[size])}>
        <div
          className={clsx('rounded-full transition-all duration-300', colorClasses[color], sizeClasses[size])}
          style={{ width: `${clampedProgress}%` }}
        />
      </div>
    </div>
  );
};

// 数据统计卡片
interface StatsCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
  loading?: boolean;
  icon?: React.ReactNode;
  className?: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  change,
  loading = false,
  icon,
  className
}) => {
  if (loading) {
    return (
      <div className={clsx('bg-white p-6 rounded-lg shadow-sm border border-gray-200 animate-pulse', className)}>
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-20" />
            <div className="h-8 bg-gray-200 rounded w-16" />
          </div>
          <div className="h-8 w-8 bg-gray-200 rounded" />
        </div>
      </div>
    );
  }

  return (
    <div className={clsx('bg-white p-6 rounded-lg shadow-sm border border-gray-200', className)}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change && (
            <p className={clsx(
              'text-sm flex items-center mt-1',
              change.type === 'increase' ? 'text-green-600' : 'text-red-600'
            )}>
              {change.type === 'increase' ? '↗' : '↘'} {Math.abs(change.value)}%
            </p>
          )}
        </div>
        {icon && (
          <div className="text-gray-400">
            {icon}
          </div>
        )}
      </div>
    </div>
  );
};
