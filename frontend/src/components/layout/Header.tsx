import React, { useState, useEffect } from 'react';
import { clsx } from 'clsx';
import {
  <PERSON>u,
  Setting<PERSON>,
  Wifi,
  WifiOff,
  RefreshCw,
  AlertCircle
} from 'lucide-react';
import { GetConnectionStatus } from '../../../wailsjs/go/backend/VCloudDBService';
import { ConnectionStatus } from '../../services/vcloudDbApi';

interface HeaderProps {
  onMenuClick: () => void;
  sidebarOpen: boolean;
}

export const Header: React.FC<HeaderProps> = ({ onMenuClick, sidebarOpen }) => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    connected: false,
    rpcUrl: ''
  });
  const [isRefreshing, setIsRefreshing] = useState(false);

  const checkConnection = async () => {
    setIsRefreshing(true);
    try {
      const status = await GetConnectionStatus();
      setConnectionStatus(status as ConnectionStatus);
    } catch (error) {
      setConnectionStatus({
        connected: false,
        rpcUrl: '',
        error: error instanceof Error ? error.message : '连接检查失败'
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    checkConnection();
    // 每30秒检查一次连接状态
    const interval = setInterval(checkConnection, 30000);
    return () => clearInterval(interval);
  }, []);

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between px-6 py-4">
        {/* 左侧：菜单按钮和面包屑 */}
        <div className="flex items-center space-x-4">
          <button
            onClick={onMenuClick}
            className="p-2 rounded-md hover:bg-gray-100 transition-colors"
          >
            <Menu className="h-5 w-5 text-gray-600" />
          </button>
          
          {/* 面包屑导航 */}
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <span>VCloud DB</span>
            <span>/</span>
            <span className="text-gray-900 font-medium">数据管理</span>
          </nav>
        </div>

        {/* 右侧：连接状态和设置 */}
        <div className="flex items-center space-x-4">
          {/* 连接状态指示器 */}
          <div className="flex items-center space-x-2">
            <button
              onClick={checkConnection}
              disabled={isRefreshing}
              className={clsx(
                'flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors',
                connectionStatus.connected
                  ? 'bg-green-100 text-green-700 hover:bg-green-200'
                  : 'bg-red-100 text-red-700 hover:bg-red-200'
              )}
              title={connectionStatus.rpcUrl || '点击检查连接状态'}
            >
              {isRefreshing ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : connectionStatus.connected ? (
                <Wifi className="h-4 w-4" />
              ) : (
                <WifiOff className="h-4 w-4" />
              )}
              <span>
                {connectionStatus.connected ? '已连接' : '未连接'}
              </span>
            </button>

            {connectionStatus.error && (
              <div className="flex items-center space-x-1 text-red-600">
                <AlertCircle className="h-4 w-4" />
                <span className="text-xs" title={connectionStatus.error}>
                  错误
                </span>
              </div>
            )}
          </div>

          {/* 设置按钮 */}
          <button
            className="p-2 rounded-md hover:bg-gray-100 transition-colors"
            title="设置"
          >
            <Settings className="h-5 w-5 text-gray-600" />
          </button>
        </div>
      </div>
    </header>
  );
};
