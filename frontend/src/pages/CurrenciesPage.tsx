import React, { useState } from 'react';
import { DataTable, Column } from '../components/common/DataTable';
import { QueryForm, FormField } from '../components/common/QueryForm';
import { ExportButton } from '../components/common/ExportButton';
import { Currency, CurrencyQueryParams } from '../types/models';
import { usePaginatedQuery } from '../hooks/useVCloudDb';
import { vcloudDbApi } from '../services/vcloudDbApi';
import { formatTimestamp } from '../utils/exportUtils';
import { DollarSign, RefreshCw, Filter, Coins } from 'lucide-react';

export const CurrenciesPage: React.FC = () => {
  const [showQueryForm, setShowQueryForm] = useState(false);

  const {
    data,
    total,
    loading,
    error,
    page: currentPage,
    pageSize,
    params,
    refetch,
    updateParams,
    changePage
  } = usePaginatedQuery(
    (params: CurrencyQueryParams) => vcloudDbApi.findCurrencies(params),
    { limit: 20, offset: 0, sortBy: 'createdAt', sortDesc: true },
    []
  );

  const columns: Column<Currency>[] = [
    {
      key: 'nameOrId',
      title: '货币名称/ID',
      dataIndex: 'nameOrId',
      width: 150,
      sortable: true,
      render: (value) => (
        <span className="inline-flex items-center px-2 py-1 rounded-md text-sm font-medium bg-green-100 text-green-800">
          <Coins className="h-3 w-3 mr-1" />
          {value}
        </span>
      )
    },
    {
      key: 'symbolName',
      title: '符号名称',
      dataIndex: 'symbolName',
      width: 120,
      render: (value) => (
        <span className="font-mono text-sm font-bold text-blue-600">
          {value || '-'}
        </span>
      )
    },
    {
      key: 'contractId',
      title: '合约ID',
      dataIndex: 'contractId',
      width: 200,
      render: (value) => (
        <span className="font-mono text-xs text-gray-600">
          {value ? `${value.slice(0, 10)}...${value.slice(-8)}` : '-'}
        </span>
      )
    },
    {
      key: 'contractType',
      title: '合约类型',
      dataIndex: 'contractType',
      width: 120,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'token' ? 'bg-blue-100 text-blue-800' :
          value === 'nft' ? 'bg-purple-100 text-purple-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {value || '-'}
        </span>
      )
    },
    {
      key: 'unit',
      title: '单位',
      dataIndex: 'unit',
      width: 100,
      align: 'right',
      render: (value) => (
        <span className="font-medium">{value || 0}</span>
      )
    },
    {
      key: 'exchangeRate',
      title: '汇率',
      dataIndex: 'exchangeRate',
      width: 120,
      align: 'right',
      render: (value) => (
        <span className="font-medium text-green-600">
          {value ? value.toFixed(6) : '0.000000'}
        </span>
      )
    },
    {
      key: 'createdAt',
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 160,
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-600">
          {value ? new Date(value * 1000).toLocaleString() : '-'}
        </span>
      )
    }
  ];

  const queryFields: FormField[] = [
    {
      key: 'nameOrId',
      label: '货币名称/ID',
      type: 'text',
      placeholder: '输入货币名称或ID...'
    },
    {
      key: 'symbolName',
      label: '符号名称',
      type: 'text',
      placeholder: '输入符号名称...'
    },
    {
      key: 'contractType',
      label: '合约类型',
      type: 'select',
      options: [
        { label: 'Token', value: 'token' },
        { label: 'NFT', value: 'nft' }
      ]
    },
    {
      key: 'contractId',
      label: '合约ID',
      type: 'text',
      placeholder: '输入合约ID...'
    }
  ];

  const handleSort = (key: string, direction: 'asc' | 'desc') => {
    updateParams({
      sortBy: key,
      sortDesc: direction === 'desc',
      offset: 0
    });
  };

  const handlePageChange = (page: number, size: number) => {
    changePage(page, size);
  };

  const handleQuery = (values: Record<string, any>) => {
    updateParams({
      offset: 0,
      ...values
    });
    setShowQueryForm(false);
  };

  const handleRefresh = () => {
    refetch();
  };

  const handleResetQuery = () => {
    updateParams({
      offset: 0,
      limit: 20,
      sortBy: 'createdAt',
      sortDesc: true
    });
    setShowQueryForm(false);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">货币管理</h1>
          <p className="text-gray-600 mt-1">管理货币信息和汇率设置</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </button>
          <button
            onClick={() => setShowQueryForm(!showQueryForm)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Filter className="h-4 w-4 mr-2" />
            {showQueryForm ? '隐藏筛选' : '筛选'}
          </button>
          <ExportButton
            data={data}
            columns={[
              { key: '_id', title: 'ID' },
              { key: 'nameOrId', title: '货币名称/ID' },
              { key: 'contractId', title: '合约ID' },
              { key: 'symbolName', title: '符号名称' },
              { key: 'contractType', title: '合约类型' },
              { key: 'unit', title: '单位' },
              { key: 'exchangeRate', title: '汇率' },
              { key: 'createdAt', title: '创建时间', render: (value) => formatTimestamp(value) },
              { key: 'updatedAt', title: '更新时间', render: (value) => formatTimestamp(value) }
            ]}
            filename="currencies"
            disabled={loading || data.length === 0}
          />
        </div>
      </div>

      {/* 查询表单 */}
      {showQueryForm && (
        <QueryForm
          fields={queryFields}
          onSubmit={handleQuery}
          onReset={handleResetQuery}
          loading={loading}
        />
      )}

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">加载数据时出错</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={handleRefresh}
                  className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                >
                  重试
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 数据表格 */}
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          onChange: handlePageChange
        }}
        sortConfig={{
          key: params.sortBy || 'createdAt',
          direction: params.sortDesc ? 'desc' : 'asc'
        }}
        onSort={handleSort}
        rowKey="_id"
        onRowClick={(record) => {
          console.log('点击货币:', record);
        }}
        emptyText={error ? '加载失败' : '暂无货币数据'}
      />
    </div>
  );
};
