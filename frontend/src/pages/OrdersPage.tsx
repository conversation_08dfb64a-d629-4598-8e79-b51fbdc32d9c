import React, { useState } from 'react';
import { DataTable, Column } from '../components/common/DataTable';
import { QueryForm, FormField } from '../components/common/QueryForm';
import { ExportButton } from '../components/common/ExportButton';
import { DetailModal, DetailField } from '../components/common/DetailModal';
import { Order, OrderQueryParams } from '../types/models';
import { useOrders } from '../hooks/useVCloudDb';
import { formatTimestamp } from '../utils/exportUtils';
import { Search, Filter, Download, Plus, RefreshCw, ShoppingCart } from 'lucide-react';

export const OrdersPage: React.FC = () => {
  const [showQueryForm, setShowQueryForm] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<Order | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  const {
    data,
    total,
    loading,
    error,
    page: currentPage,
    pageSize,
    params,
    refetch,
    updateParams,
    changePage
  } = useOrders({
    limit: 20,
    offset: 0,
    sortBy: 'createdAt',
    sortDesc: true
  });

  const columns: Column<Order>[] = [
    {
      key: 'type',
      title: '订单类型',
      dataIndex: 'type',
      width: 120,
      sortable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'purchase' ? 'bg-green-100 text-green-800' :
          value === 'refund' ? 'bg-red-100 text-red-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {value === 'purchase' ? '购买' : value === 'refund' ? '退款' : value}
        </span>
      )
    },
    {
      key: 'address',
      title: '用户地址',
      dataIndex: 'address',
      width: 180,
      render: (value) => (
        <span className="font-mono text-xs text-gray-600">
          {value ? `${value.slice(0, 8)}...${value.slice(-6)}` : '-'}
        </span>
      )
    },
    {
      key: 'recipient',
      title: '接收地址',
      dataIndex: 'recipient',
      width: 180,
      render: (value) => (
        <span className="font-mono text-xs text-gray-600">
          {value ? `${value.slice(0, 8)}...${value.slice(-6)}` : '-'}
        </span>
      )
    },
    {
      key: 'provider',
      title: '服务提供商',
      dataIndex: 'provider',
      width: 120,
      sortable: true
    },
    {
      key: 'status',
      title: '订单状态',
      dataIndex: 'status',
      width: 100,
      render: (value) => {
        const statusColors = {
          'pending': 'bg-yellow-100 text-yellow-800',
          'paid': 'bg-green-100 text-green-800',
          'completed': 'bg-blue-100 text-blue-800',
          'cancelled': 'bg-red-100 text-red-800',
          'refunded': 'bg-purple-100 text-purple-800'
        };
        const statusLabels = {
          'pending': '待支付',
          'paid': '已支付',
          'completed': '已完成',
          'cancelled': '已取消',
          'refunded': '已退款'
        };
        return (
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            statusColors[value as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'
          }`}>
            {statusLabels[value as keyof typeof statusLabels] || value}
          </span>
        );
      }
    },
    {
      key: 'amount',
      title: '订单金额',
      dataIndex: 'amount',
      width: 120,
      align: 'right',
      sortable: true,
      render: (value) => (
        <span className="font-medium">{value?.toLocaleString() || 0}</span>
      )
    },
    {
      key: 'amountPaid',
      title: '已支付金额',
      dataIndex: 'amountPaid',
      width: 120,
      align: 'right',
      render: (value) => (
        <span className="font-medium text-green-600">{value?.toLocaleString() || 0}</span>
      )
    },
    {
      key: 'userServiceIDs',
      title: '关联服务',
      dataIndex: 'userServiceIDs',
      width: 100,
      align: 'center',
      render: (value: string[]) => (
        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
          {value?.length || 0} 个
        </span>
      )
    },
    {
      key: 'createdAt',
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 160,
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-600">
          {value ? new Date(value * 1000).toLocaleString() : '-'}
        </span>
      )
    },
    {
      key: 'paidTS',
      title: '支付时间',
      dataIndex: 'paidTS',
      width: 160,
      render: (value) => (
        <span className="text-sm text-gray-600">
          {value ? new Date(value * 1000).toLocaleString() : '-'}
        </span>
      )
    }
  ];

  const queryFields: FormField[] = [
    {
      key: 'address',
      label: '用户地址',
      type: 'text',
      placeholder: '输入用户地址...'
    },
    {
      key: 'recipient',
      label: '接收地址',
      type: 'text',
      placeholder: '输入接收地址...'
    },
    {
      key: 'type',
      label: '订单类型',
      type: 'select',
      options: [
        { label: '购买', value: 'purchase' },
        { label: '退款', value: 'refund' }
      ]
    },
    {
      key: 'provider',
      label: '服务提供商',
      type: 'text',
      placeholder: '输入服务提供商...'
    },
    {
      key: 'status',
      label: '订单状态',
      type: 'select',
      options: [
        { label: '待支付', value: 'pending' },
        { label: '已支付', value: 'paid' },
        { label: '已完成', value: 'completed' },
        { label: '已取消', value: 'cancelled' },
        { label: '已退款', value: 'refunded' }
      ]
    },
    {
      key: 'minAmount',
      label: '最小金额',
      type: 'number',
      placeholder: '输入最小金额...'
    },
    {
      key: 'maxAmount',
      label: '最大金额',
      type: 'number',
      placeholder: '输入最大金额...'
    }
  ];

  const handleSort = (key: string, direction: 'asc' | 'desc') => {
    updateParams({
      sortBy: key,
      sortDesc: direction === 'desc',
      offset: 0
    });
  };

  const handlePageChange = (page: number, size: number) => {
    changePage(page, size);
  };

  const handleRowClick = (record: Order) => {
    setSelectedRecord(record);
    setShowDetailModal(true);
  };

  const handleCloseDetail = () => {
    setShowDetailModal(false);
    setSelectedRecord(null);
  };

  const detailFields: DetailField[] = [
    { key: '_id', label: 'ID', type: 'id', copyable: true },
    { key: 'type', label: '订单类型', type: 'text' },
    { key: 'address', label: '用户地址', type: 'address', copyable: true },
    { key: 'recipient', label: '接收地址', type: 'address', copyable: true },
    { key: 'provider', label: '服务提供商', type: 'text' },
    { key: 'status', label: '订单状态', type: 'text' },
    { key: 'amount', label: '订单金额', type: 'amount' },
    { key: 'amountPaid', label: '已支付金额', type: 'amount' },
    { key: 'createdAt', label: '创建时间', type: 'timestamp' },
    { key: 'updatedAt', label: '更新时间', type: 'timestamp' },
    { key: 'paidTS', label: '支付时间', type: 'timestamp' },
    { key: 'filedTS', label: '提交时间', type: 'timestamp' },
    { key: 'deletedAt', label: '删除时间', type: 'timestamp' }
  ];

  const handleQuery = (values: Record<string, any>) => {
    const queryParams: Partial<OrderQueryParams> = {
      offset: 0,
      ...values
    };

    // 处理金额范围
    if (values.minAmount) {
      queryParams.tsStart = values.minAmount;
    }
    if (values.maxAmount) {
      queryParams.tsEnd = values.maxAmount;
    }

    updateParams(queryParams);
    setShowQueryForm(false);
  };

  const handleRefresh = () => {
    refetch();
  };

  const handleResetQuery = () => {
    updateParams({
      offset: 0,
      limit: 20,
      sortBy: 'createdAt',
      sortDesc: true
    });
    setShowQueryForm(false);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">订单管理</h1>
          <p className="text-gray-600 mt-1">管理和查看订单信息</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </button>
          <button
            onClick={() => setShowQueryForm(!showQueryForm)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Filter className="h-4 w-4 mr-2" />
            {showQueryForm ? '隐藏筛选' : '高级筛选'}
          </button>
          <ExportButton
            data={data}
            columns={[
              { key: '_id', title: 'ID' },
              { key: 'type', title: '订单类型' },
              { key: 'address', title: '用户地址' },
              { key: 'recipient', title: '接收地址' },
              { key: 'provider', title: '服务提供商' },
              { key: 'status', title: '订单状态' },
              { key: 'amount', title: '订单金额' },
              { key: 'amountPaid', title: '已支付金额' },
              { key: 'createdAt', title: '创建时间', render: (value) => formatTimestamp(value) },
              { key: 'updatedAt', title: '更新时间', render: (value) => formatTimestamp(value) },
              { key: 'paidTS', title: '支付时间', render: (value) => formatTimestamp(value) },
              { key: 'filedTS', title: '提交时间', render: (value) => formatTimestamp(value) }
            ]}
            filename="orders"
            disabled={loading || data.length === 0}
          />
        </div>
      </div>

      {/* 查询表单 */}
      {showQueryForm && (
        <QueryForm
          fields={queryFields}
          onSubmit={handleQuery}
          onReset={handleResetQuery}
          loading={loading}
          showAdvanced={true}
        />
      )}

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">加载数据时出错</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={handleRefresh}
                  className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                >
                  重试
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 数据表格 */}
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          onChange: handlePageChange
        }}
        sortConfig={{
          key: params.sortBy || 'createdAt',
          direction: params.sortDesc ? 'desc' : 'asc'
        }}
        onSort={handleSort}
        rowKey="_id"
        onRowClick={handleRowClick}
        emptyText={error ? '加载失败' : '暂无订单数据'}
      />

      {/* 详情弹窗 */}
      <DetailModal
        isOpen={showDetailModal}
        onClose={handleCloseDetail}
        title="订单详情"
        data={selectedRecord}
        fields={detailFields}
        icon={<ShoppingCart className="h-6 w-6 text-blue-600" />}
      />
    </div>
  );
};
