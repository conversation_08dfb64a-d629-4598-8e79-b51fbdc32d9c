import React, { useState } from 'react';
import { DataTable, Column } from '../components/common/DataTable';
import { QueryForm, FormField } from '../components/common/QueryForm';
import { ExportButton } from '../components/common/ExportButton';
import { CliVersion, CliVersionQueryParams } from '../types/models';
import { useCliVersions } from '../hooks/useVCloudDb';
import { formatTimestamp } from '../utils/exportUtils';
import { Download, RefreshCw, Filter, Tag } from 'lucide-react';

export const CliVersionsPage: React.FC = () => {
  const [showQueryForm, setShowQueryForm] = useState(false);

  const {
    data,
    total,
    loading,
    error,
    page: currentPage,
    pageSize,
    params,
    refetch,
    updateParams,
    changePage
  } = useCliVersions({
    limit: 20,
    offset: 0,
    sortBy: 'createdAt',
    sortDesc: true
  });

  const columns: Column<CliVersion>[] = [
    {
      key: 'version',
      title: '版本号',
      dataIndex: 'version',
      width: 150,
      sortable: true,
      render: (value) => (
        <span className="inline-flex items-center px-2 py-1 rounded-md text-sm font-mono bg-blue-100 text-blue-800">
          <Tag className="h-3 w-3 mr-1" />
          {value}
        </span>
      )
    },
    {
      key: 'minimalSupported',
      title: '最低支持版本',
      dataIndex: 'minimalSupported',
      width: 150,
      render: (value) => (
        <span className="inline-flex items-center px-2 py-1 rounded-md text-sm font-mono bg-gray-100 text-gray-800">
          {value || '-'}
        </span>
      )
    },
    {
      key: 'changeLog',
      title: '更新日志',
      dataIndex: 'changeLog',
      render: (value) => (
        <div className="max-w-md">
          <p className="text-sm text-gray-900 line-clamp-3">
            {value || '暂无更新日志'}
          </p>
        </div>
      )
    },
    {
      key: 'createdAt',
      title: '发布时间',
      dataIndex: 'createdAt',
      width: 160,
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-600">
          {value ? new Date(value * 1000).toLocaleString() : '-'}
        </span>
      )
    },
    {
      key: 'updatedAt',
      title: '更新时间',
      dataIndex: 'updatedAt',
      width: 160,
      render: (value) => (
        <span className="text-sm text-gray-600">
          {value ? new Date(value * 1000).toLocaleString() : '-'}
        </span>
      )
    }
  ];

  const queryFields: FormField[] = [
    {
      key: 'version',
      label: '版本号',
      type: 'text',
      placeholder: '输入版本号...'
    },
    {
      key: 'minimalSupported',
      label: '最低支持版本',
      type: 'text',
      placeholder: '输入最低支持版本...'
    }
  ];

  const handleSort = (key: string, direction: 'asc' | 'desc') => {
    updateParams({
      sortBy: key,
      sortDesc: direction === 'desc',
      offset: 0
    });
  };

  const handlePageChange = (page: number, size: number) => {
    changePage(page, size);
  };

  const handleQuery = (values: Record<string, any>) => {
    updateParams({
      offset: 0,
      ...values
    });
    setShowQueryForm(false);
  };

  const handleRefresh = () => {
    refetch();
  };

  const handleResetQuery = () => {
    updateParams({
      offset: 0,
      limit: 20,
      sortBy: 'createdAt',
      sortDesc: true
    });
    setShowQueryForm(false);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">CLI版本管理</h1>
          <p className="text-gray-600 mt-1">管理CLI版本信息和更新日志</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </button>
          <button
            onClick={() => setShowQueryForm(!showQueryForm)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Filter className="h-4 w-4 mr-2" />
            {showQueryForm ? '隐藏筛选' : '筛选'}
          </button>
          <ExportButton
            data={data}
            columns={[
              { key: '_id', title: 'ID' },
              { key: 'version', title: '版本号' },
              { key: 'minimalSupported', title: '最低支持版本' },
              { key: 'changeLog', title: '更新日志' },
              { key: 'createdAt', title: '创建时间', render: (value) => formatTimestamp(value) },
              { key: 'updatedAt', title: '更新时间', render: (value) => formatTimestamp(value) }
            ]}
            filename="cli_versions"
            disabled={loading || data.length === 0}
          />
        </div>
      </div>

      {/* 查询表单 */}
      {showQueryForm && (
        <QueryForm
          fields={queryFields}
          onSubmit={handleQuery}
          onReset={handleResetQuery}
          loading={loading}
        />
      )}

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">加载数据时出错</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={handleRefresh}
                  className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                >
                  重试
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 数据表格 */}
      <DataTable
        columns={columns}
        data={data}
        loading={loading}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          onChange: handlePageChange
        }}
        sortConfig={{
          key: params.sortBy || 'createdAt',
          direction: params.sortDesc ? 'desc' : 'asc'
        }}
        onSort={handleSort}
        rowKey="_id"
        onRowClick={(record) => {
          console.log('点击CLI版本:', record);
        }}
        emptyText={error ? '加载失败' : '暂无CLI版本数据'}
      />
    </div>
  );
};
