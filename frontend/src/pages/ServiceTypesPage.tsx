import React, { useState } from 'react';
import { DataTable, Column } from '../components/common/DataTable';
import { QueryForm, FormField } from '../components/common/QueryForm';
import { ExportButton } from '../components/common/ExportButton';
import { ServiceType, ServiceTypeQueryParams } from '../types/models';
import { useServiceTypes } from '../hooks/useVCloudDb';
import { formatTimestamp, formatBoolean, formatObject } from '../utils/exportUtils';
import { Search, Filter, Download, Plus, RefreshCw, Settings } from 'lucide-react';

export const ServiceTypesPage: React.FC = () => {
  const [showQueryForm, setShowQueryForm] = useState(false);

  const {
    data,
    total,
    loading,
    error,
    page: currentPage,
    pageSize,
    params,
    refetch,
    updateParams,
    changePage
  } = useServiceTypes({
    limit: 20,
    offset: 0,
    sortBy: 'createdAt',
    sortDesc: true
  });

  const columns: Column<ServiceType>[] = [
    {
      key: '_id',
      title: 'ID',
      dataIndex: '_id',
      width: 120,
      render: (value) => (
        <span className="font-mono text-xs text-gray-600">
          {value.substring(0, 8)}...
        </span>
      )
    },
    {
      key: 'name',
      title: '服务类型名称',
      dataIndex: 'name',
      width: 150,
      sortable: true,
      render: (value) => (
        <span className="font-medium text-gray-900">{value}</span>
      )
    },
    {
      key: 'provider',
      title: '服务提供商',
      dataIndex: 'provider',
      width: 150,
      sortable: true,
      render: (value) => (
        <span className="text-blue-600 font-medium">{value}</span>
      )
    },
    {
      key: 'category',
      title: '服务分类',
      dataIndex: 'category',
      width: 120,
      sortable: true,
      render: (value) => (
        <span className="text-green-600 font-medium">{value}</span>
      )
    },
    {
      key: 'refundable',
      title: '可退款',
      dataIndex: 'refundable',
      width: 100,
      sortable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {value ? '是' : '否'}
        </span>
      )
    },
    {
      key: 'description',
      title: '描述',
      dataIndex: 'description',
      width: 200,
      render: (value) => (
        <span className="text-sm text-gray-600 truncate" title={value}>
          {value || '-'}
        </span>
      )
    },
    {
      key: 'apiHost',
      title: 'API主机',
      dataIndex: 'apiHost',
      width: 180,
      render: (value) => (
        <span className="font-mono text-xs text-green-600">
          {value || '-'}
        </span>
      )
    },
    {
      key: 'durationToPrice',
      title: '价格设置',
      dataIndex: 'durationToPrice',
      width: 120,
      render: (value) => {
        const priceCount = value ? value.length : 0;
        return (
          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
            {priceCount} 个价格
          </span>
        );
      }
    },
    {
      key: 'createdAt',
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 160,
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-600">
          {new Date(value * 1000).toLocaleString()}
        </span>
      )
    }
  ];

  const queryFields: FormField[] = [
    {
      key: 'name',
      label: '服务类型名称',
      type: 'text',
      placeholder: '输入服务类型名称...'
    },
    {
      key: 'provider',
      label: '服务提供商',
      type: 'text',
      placeholder: '输入服务提供商...'
    },
    {
      key: 'category',
      label: '服务分类',
      type: 'text',
      placeholder: '输入服务分类...'
    },
    {
      key: 'categoryID',
      label: '分类ID',
      type: 'text',
      placeholder: '输入分类ID...'
    },
    {
      key: 'refundable',
      label: '可退款',
      type: 'select',
      options: [
        { label: '是', value: 'true' },
        { label: '否', value: 'false' }
      ]
    }
  ];

  const handleSort = (key: string, direction: 'asc' | 'desc') => {
    updateParams({
      sortBy: key,
      sortDesc: direction === 'desc',
      offset: 0
    });
  };

  const handlePageChange = (page: number, size: number) => {
    changePage(page, size);
  };

  const handleQuery = (values: Record<string, any>) => {
    const queryParams: Partial<ServiceTypeQueryParams> = {
      offset: 0,
      ...values
    };

    // 处理布尔值转换
    if (values.refundable) {
      queryParams.refundable = values.refundable === 'true';
    }

    updateParams(queryParams);
    setShowQueryForm(false);
  };

  const handleRefresh = () => {
    refetch();
  };

  const handleResetQuery = () => {
    updateParams({
      name: undefined,
      provider: undefined,
      category: undefined,
      categoryID: undefined,
      refundable: undefined,
      offset: 0
    });
    setShowQueryForm(false);
  };

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Settings className="h-6 w-6 mr-2 text-blue-600" />
            服务类型管理
          </h1>
          <p className="text-gray-600 mt-1">管理服务类型定义和配置</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowQueryForm(!showQueryForm)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Filter className="h-4 w-4 mr-2" />
            {showQueryForm ? '隐藏筛选' : '显示筛选'}
          </button>
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </button>
          <ExportButton
            data={data}
            columns={[
              { key: '_id', title: 'ID' },
              { key: 'name', title: '服务类型名称' },
              { key: 'provider', title: '服务提供商' },
              { key: 'category', title: '服务分类' },
              { key: 'categoryID', title: '分类ID' },
              { key: 'refundable', title: '可退款', render: (value) => formatBoolean(value) },
              { key: 'description', title: '描述' },
              { key: 'apiHost', title: 'API主机' },
              { key: 'serviceOptions', title: '服务选项', render: (value) => formatObject(value) },
              { key: 'durationToPrice', title: '价格设置', render: (value) => formatObject(value) },
              { key: 'serviceOptionDesc', title: '选项描述', render: (value) => formatObject(value) },
              { key: 'createdAt', title: '创建时间', render: (value) => formatTimestamp(value) },
              { key: 'updatedAt', title: '更新时间', render: (value) => formatTimestamp(value) }
            ]}
            filename="service_types"
            disabled={loading || data.length === 0}
          />
        </div>
      </div>

      {/* 查询表单 */}
      {showQueryForm && (
        <div className="bg-white rounded-lg shadow p-6">
          <QueryForm
            fields={queryFields}
            onSubmit={handleQuery}
            onReset={handleResetQuery}
            loading={loading}
            initialValues={params}
          />
        </div>
      )}

      {/* 数据统计 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <Settings className="h-5 w-5 text-blue-500 mr-2" />
              <span className="text-sm font-medium text-gray-900">
                总服务类型数: {total.toLocaleString()}
              </span>
            </div>
            {params.name && (
              <div className="text-sm text-gray-600">
                类型: {params.name}
              </div>
            )}
            {params.provider && (
              <div className="text-sm text-gray-600">
                提供商: {params.provider}
              </div>
            )}
            {params.category && (
              <div className="text-sm text-gray-600">
                分类: {params.category}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 数据表格 */}
      <div className="bg-white rounded-lg shadow">
        <DataTable
          columns={columns}
          data={data as ServiceType[]}
          loading={loading}
          error={error || undefined}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: handlePageChange,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `显示 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
          onSort={handleSort}
          sortBy={params.sortBy}
          sortDesc={params.sortDesc}
        />
      </div>
    </div>
  );
};
