import React, { useState } from 'react';
import { DataTable, Column } from '../components/common/DataTable';
import { QueryForm, FormField } from '../components/common/QueryForm';
import { ExportButton } from '../components/common/ExportButton';
import { DetailModal, DetailField } from '../components/common/DetailModal';
import { RefreshControl } from '../components/common/RefreshControl';
import { OrderService, OrderServiceQueryParams } from '../types/models';
import { useOrderServices } from '../hooks/useVCloudDb';
import { formatTimestamp } from '../utils/exportUtils';
import { Search, Filter, Download, Plus, RefreshCw, Link2 } from 'lucide-react';

export const OrderServicesPage: React.FC = () => {
  const [showQueryForm, setShowQueryForm] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<OrderService | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  const {
    data,
    total,
    loading,
    error,
    page: currentPage,
    pageSize,
    params,
    refetch,
    updateParams,
    changePage,
    lastRefresh,
    autoRefresh,
    refreshInterval,
    setAutoRefresh
  } = useOrderServices({
    limit: 20,
    offset: 0,
    sortBy: 'createdAt',
    sortDesc: true
  });

  const columns: Column<OrderService>[] = [
    {
      key: '_id',
      title: 'ID',
      dataIndex: '_id',
      width: 120,
      render: (value) => (
        <span className="font-mono text-xs text-gray-600">
          {value.substring(0, 8)}...
        </span>
      )
    },
    {
      key: 'orderID',
      title: '订单ID',
      dataIndex: 'orderID',
      width: 150,
      sortable: true,
      render: (value) => (
        <span className="font-mono text-xs text-blue-600">
          {value.substring(0, 12)}...
        </span>
      )
    },
    {
      key: 'userServiceID',
      title: '用户服务ID',
      dataIndex: 'userServiceID',
      width: 150,
      sortable: true,
      render: (value) => (
        <span className="font-mono text-xs text-green-600">
          {value.substring(0, 12)}...
        </span>
      )
    },
    {
      key: 'orderStatus',
      title: '订单状态',
      dataIndex: 'orderStatus',
      width: 120,
      sortable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'paid' ? 'bg-green-100 text-green-800' :
          value === 'pending' ? 'bg-yellow-100 text-yellow-800' :
          value === 'cancelled' ? 'bg-red-100 text-red-800' :
          value === 'completed' ? 'bg-blue-100 text-blue-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {value === 'paid' ? '已支付' :
           value === 'pending' ? '待支付' :
           value === 'cancelled' ? '已取消' :
           value === 'completed' ? '已完成' :
           value}
        </span>
      )
    },
    {
      key: 'orderType',
      title: '订单类型',
      dataIndex: 'orderType',
      width: 120,
      sortable: true,
      render: (value) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'purchase' ? 'bg-green-100 text-green-800' :
          value === 'refund' ? 'bg-red-100 text-red-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {value === 'purchase' ? '购买' : value === 'refund' ? '退款' : value}
        </span>
      )
    },
    {
      key: 'createdAt',
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 160,
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-600">
          {new Date(value * 1000).toLocaleString()}
        </span>
      )
    },
    {
      key: 'updatedAt',
      title: '更新时间',
      dataIndex: 'updatedAt',
      width: 160,
      sortable: true,
      render: (value) => (
        <span className="text-sm text-gray-600">
          {new Date(value * 1000).toLocaleString()}
        </span>
      )
    }
  ];

  const queryFields: FormField[] = [
    {
      key: 'orderID',
      label: '订单ID',
      type: 'text',
      placeholder: '输入订单ID...'
    },
    {
      key: 'userServiceID',
      label: '用户服务ID',
      type: 'text',
      placeholder: '输入用户服务ID...'
    },
    {
      key: 'orderStatus',
      label: '订单状态',
      type: 'select',
      options: [
        { label: '待支付', value: 'pending' },
        { label: '已支付', value: 'paid' },
        { label: '已完成', value: 'completed' },
        { label: '已取消', value: 'cancelled' },
        { label: '已退款', value: 'refunded' }
      ]
    },
    {
      key: 'orderType',
      label: '订单类型',
      type: 'select',
      options: [
        { label: '购买', value: 'purchase' },
        { label: '退款', value: 'refund' }
      ]
    }
  ];

  const handleSort = (key: string, direction: 'asc' | 'desc') => {
    updateParams({
      sortBy: key,
      sortDesc: direction === 'desc',
      offset: 0
    });
  };

  const handlePageChange = (page: number, size: number) => {
    changePage(page, size);
  };

  const handleRowClick = (record: OrderService) => {
    setSelectedRecord(record);
    setShowDetailModal(true);
  };

  const handleCloseDetail = () => {
    setShowDetailModal(false);
    setSelectedRecord(null);
  };

  const detailFields: DetailField[] = [
    { key: '_id', label: 'ID', type: 'id', copyable: true },
    { key: 'orderID', label: '订单ID', type: 'id', copyable: true },
    { key: 'userServiceID', label: '用户服务ID', type: 'id', copyable: true },
    { key: 'orderStatus', label: '订单状态', type: 'text' },
    { key: 'orderType', label: '订单类型', type: 'text' },
    { key: 'createdAt', label: '创建时间', type: 'timestamp' },
    { key: 'updatedAt', label: '更新时间', type: 'timestamp' },
    { key: 'deletedAt', label: '删除时间', type: 'timestamp' }
  ];

  const handleQuery = (values: Record<string, any>) => {
    const queryParams: Partial<OrderServiceQueryParams> = {
      offset: 0,
      ...values
    };

    updateParams(queryParams);
    setShowQueryForm(false);
  };

  const handleRefresh = () => {
    refetch();
  };

  const handleResetQuery = () => {
    updateParams({
      orderID: undefined,
      userServiceID: undefined,
      orderStatus: undefined,
      orderType: undefined,
      offset: 0
    });
    setShowQueryForm(false);
  };

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Link2 className="h-6 w-6 mr-2 text-blue-600" />
            订单服务关联
          </h1>
          <p className="text-gray-600 mt-1">管理订单与服务的关联关系</p>
        </div>
        <div className="flex items-center space-x-3">
          <RefreshControl
            onRefresh={handleRefresh}
            loading={loading}
            lastRefresh={lastRefresh}
            autoRefresh={autoRefresh}
            refreshInterval={refreshInterval}
            onAutoRefreshChange={setAutoRefresh}
          />
          <button
            onClick={() => setShowQueryForm(!showQueryForm)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Filter className="h-4 w-4 mr-2" />
            {showQueryForm ? '隐藏筛选' : '显示筛选'}
          </button>
          <ExportButton
            data={data}
            columns={[
              { key: '_id', title: 'ID' },
              { key: 'orderID', title: '订单ID' },
              { key: 'userServiceID', title: '用户服务ID' },
              { key: 'orderStatus', title: '订单状态' },
              { key: 'orderType', title: '订单类型' },
              { key: 'createdAt', title: '创建时间', render: (value) => formatTimestamp(value) },
              { key: 'updatedAt', title: '更新时间', render: (value) => formatTimestamp(value) }
            ]}
            filename="order_services"
            disabled={loading || data.length === 0}
          />
        </div>
      </div>

      {/* 查询表单 */}
      {showQueryForm && (
        <div className="bg-white rounded-lg shadow p-6">
          <QueryForm
            fields={queryFields}
            onSubmit={handleQuery}
            onReset={handleResetQuery}
            loading={loading}
            initialValues={params}
          />
        </div>
      )}

      {/* 数据统计 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <Link2 className="h-5 w-5 text-blue-500 mr-2" />
              <span className="text-sm font-medium text-gray-900">
                总关联数: {total.toLocaleString()}
              </span>
            </div>
            {params.orderID && (
              <div className="text-sm text-gray-600">
                订单ID: {params.orderID}
              </div>
            )}
            {params.userServiceID && (
              <div className="text-sm text-gray-600">
                服务ID: {params.userServiceID}
              </div>
            )}
            {params.orderStatus && (
              <div className="text-sm text-gray-600">
                状态: {params.orderStatus}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 数据表格 */}
      <div className="bg-white rounded-lg shadow">
        <DataTable
          columns={columns}
          data={data}
          loading={loading}
          error={error || undefined}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: handlePageChange,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `显示 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
          onSort={handleSort}
          sortBy={params.sortBy}
          sortDesc={params.sortDesc}
          onRowClick={handleRowClick}
        />
      </div>

      {/* 详情弹窗 */}
      <DetailModal
        isOpen={showDetailModal}
        onClose={handleCloseDetail}
        title="订单服务关联详情"
        data={selectedRecord}
        fields={detailFields}
        icon={<Link2 className="h-6 w-6 text-blue-600" />}
      />
    </div>
  );
};
